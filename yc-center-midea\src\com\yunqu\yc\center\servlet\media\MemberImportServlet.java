package com.yunqu.yc.center.servlet.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppBaseServlet;
import com.yunqu.yc.center.util.MemberConfigUtil;
import org.apache.poi.ss.usermodel.Cell;
import org.apache.poi.ss.usermodel.Row;
import org.apache.poi.ss.usermodel.Sheet;
import org.apache.poi.ss.usermodel.Workbook;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.excel.utils.Utils;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;
import javax.servlet.http.Part;
import java.io.IOException;
import java.io.InputStream;
import java.io.OutputStream;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 会员策略Excel导入导出Servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/memberImport/*")
@MultipartConfig
public class MemberImportServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;
    
    // Excel模板表头
    private static final String[] TEMPLATE_HEADERS = {
        "会员id", "会员名称", "渠道key", "渠道名称", "身份等级（优先级排序）", "直接转人工", "转人工技能组id"
    };

    /**
     * 下载导入模板
     */
    public void actionForDownloadTemplate(){
        try {
            // 创建Excel工作簿
            Workbook workbook = new XSSFWorkbook();
            Sheet sheet = workbook.createSheet("会员策略导入模板");
            
            // 创建表头
            Row headerRow = sheet.createRow(0);
            for(int i = 0; i < TEMPLATE_HEADERS.length; i++) {
                Cell cell = headerRow.createCell(i);
                cell.setCellValue(TEMPLATE_HEADERS[i]);
            }
            
            // 添加示例数据：根据预设会员身份字典为每个身份生成一条示例
            Map<String, String> memberDictMap = MemberConfigUtil.getMemberDictMap();
            int rowIndex = 1;
            for (String memberId : memberDictMap.keySet()) {
                String memberName = memberDictMap.get(memberId);
                Row exampleRow = sheet.createRow(rowIndex++);
                exampleRow.createCell(0).setCellValue(memberId);
                exampleRow.createCell(1).setCellValue(memberName);
                exampleRow.createCell(2).setCellValue("");
                exampleRow.createCell(3).setCellValue("");
                exampleRow.createCell(4).setCellValue("");
                exampleRow.createCell(5).setCellValue("");
                exampleRow.createCell(6).setCellValue("");
            }
            // 自动调整列宽
            for(int i = 0; i < TEMPLATE_HEADERS.length; i++) {
                sheet.autoSizeColumn(i);
            }
            
            // 设置响应头
            HttpServletResponse response = getResponse();
            response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
            response.setHeader("Content-Disposition", "attachment; filename=member_strategy_template.xlsx");
            
            // 输出文件
            OutputStream outputStream = response.getOutputStream();
            workbook.write(outputStream);
            workbook.close();
            outputStream.close();
            
        } catch (IOException e) {
            this.error("下载模板失败", e);
        }
    }

    public EasyResult actionForImportExcel(){
        try {
            List<List<String>> list = new ArrayList<>();
            Part part=getFile("file");
            // 解析Excel文件
            List<Map<String, String>> dataList = parseExcelFile(part);
            if(dataList.isEmpty()) {
                return EasyResult.fail("Excel文件中没有有效数据");
            }

            // 校验数据并导入
            ImportResult result = importData(dataList);
            
            if(result.hasError()) {
                return EasyResult.fail("导入失败：" + result.getErrorMessage());
            }
            
            return EasyResult.ok(null, "导入成功，共处理 " + result.getSuccessCount() + " 条记录");
            
        } catch (Exception e) {
            this.error("导入Excel失败", e);
            return EasyResult.fail("导入失败：" + e.getMessage());
        }
    }
    
    /**
     * 解析Excel文件
     */
    private List<Map<String, String>> parseExcelFile(Part part) throws Exception {
        List<Map<String, String>> dataList = new ArrayList<>();
        InputStream is = null;

        try {
            is = part.getInputStream();
            Workbook workbook = new XSSFWorkbook(is);
            Sheet sheet = workbook.getSheetAt(0);

            // 获取表头行
            Row headerRow = sheet.getRow(0);
            if(headerRow == null) {
                throw new Exception("Excel文件格式错误：缺少表头");
            }

            // 校验表头
            for(int i = 0; i < TEMPLATE_HEADERS.length; i++) {
                Cell cell = headerRow.getCell(i);
                if(cell == null || !TEMPLATE_HEADERS[i].equals(cell.getStringCellValue())) {
                    throw new Exception("Excel文件格式错误：表头不匹配模板，请下载最新模板");
                }
            }

            // 读取数据行
            for(int rowIndex = 1; rowIndex <= sheet.getLastRowNum(); rowIndex++) {
                Row row = sheet.getRow(rowIndex);
                if(row == null) continue;

                Map<String, String> rowData = new HashMap<>();
                boolean hasData = false;

                for(int colIndex = 0; colIndex < TEMPLATE_HEADERS.length; colIndex++) {
                    Cell cell = row.getCell(colIndex);
                    String value = "";
                    if(null != cell) {
                        value = Utils.getCellValue(cell);
                    }
                    rowData.put(TEMPLATE_HEADERS[colIndex], value.trim());
                    if(StringUtils.isNotBlank(value)) {
                        hasData = true;
                    }
                }

                if(hasData) {
                    rowData.put("ROW_INDEX", String.valueOf(rowIndex + 1));
                    dataList.add(rowData);
                }
            }

            workbook.close();
            return dataList;

        } finally {
            if(is != null){
                try {
                    is.close();
                } catch (IOException e) {
                    this.error(e.getMessage(), e);
                }
            }
        }

    }
    
    /**
     * 导入数据
     */
    private ImportResult importData(List<Map<String, String>> dataList) throws SQLException {
        ImportResult result = new ImportResult();
        Map<String, String> memberConfigMap = new HashMap<>(); // 会员ID -> 配置ID
        
        for(Map<String, String> rowData : dataList) {
            try {
//                "会员id", "会员名称", "渠道key", "渠道名称", "身份等级（优先级排序）", "直接转人工", "转人工技能组id"
                String memberId = rowData.get(TEMPLATE_HEADERS[0]);
                String memberName = rowData.get(TEMPLATE_HEADERS[1]);
                String channelKey = rowData.get(TEMPLATE_HEADERS[2]);
                String channelName = rowData.get(TEMPLATE_HEADERS[3]);
                String rankIndexStr = rowData.get(TEMPLATE_HEADERS[4]);
                String isToAgentStr = rowData.get(TEMPLATE_HEADERS[5]);
                String agentGroupId = rowData.get(TEMPLATE_HEADERS[6]);
                String rowIndex = rowData.get("ROW_INDEX");
                
                // 数据校验
                if(StringUtils.isBlank(memberId)) {
                    result.addError("第" + rowIndex + "行：会员id不能为空");
                    continue;
                }
                if(StringUtils.isBlank(memberName)) {
                    result.addError("第" + rowIndex + "行：会员名称不能为空");
                    continue;
                }
                if(StringUtils.isBlank(channelKey)) {
                    result.addError("第" + rowIndex + "行：渠道key不能为空");
                    continue;
                }
                if(StringUtils.isBlank(rankIndexStr) || !rankIndexStr.matches("\\d+")) {
                    result.addError("第" + rowIndex + "行：身份等级必须是数字");
                    continue;
                }
                
                int rankIndex = Integer.parseInt(rankIndexStr);
                if(rankIndex < 1 || rankIndex > 20) {
                    result.addError("第" + rowIndex + "行：身份等级必须在1-20之间");
                    continue;
                }
                
                int isToAgent = 0;
                if("1".equals(isToAgentStr) || "是".equals(isToAgentStr)) {
                    isToAgent = 1;
                }
                
                // 处理会员身份策略配置
                String configId = memberConfigMap.get(memberId);
                if(configId == null) {
                    // 检查是否已存在
                    JSONObject existingConfig = this.getQuery().queryForRow(
                        "select CONFIG_ID from YCUSER.CC_MEDIA_MEMBER_CONFIG where MEMBER_ID = ?",
                        new Object[]{memberId}, new JSONMapperImpl()
                    );
                    
                    if(existingConfig != null) {
                        configId = existingConfig.getString("CONFIG_ID");
                    } else {
                        // 创建新的会员身份策略
                        configId = RandomKit.randomStr();
                        String memberdictName = MemberConfigUtil.getMemberDictMap().get(memberId);
                        if(StringUtils.isBlank(memberdictName)) {
                            result.addError("第" + rowIndex + "行：会员身份id【" + memberId + "】与数据字典不匹配！");
                            continue;
                        }

                        createMemberConfig(configId, memberId, memberdictName, rankIndex, isToAgent, agentGroupId);
                    }
                    memberConfigMap.put(memberId, configId);
                }
                
                // 检查渠道身份等级冲突
                if(MemberConfigUtil.checkChannelRankIndexConflict(channelKey, rankIndex, configId)) {
                    result.addError("第" + rowIndex + "行：渠道【" + channelName + "】的身份等级冲突");
                    continue;
                }
                // 检查是否已存在
                boolean has = this.getQuery().queryForExist(
                        "select count(1) from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ? and CHANNEL_KEY = ?",
                        new Object[]{configId, channelKey}
                );

                JSONObject memberStrategy = new JSONObject();
                memberStrategy.put("RANK_INDEX", rankIndexStr);
                memberStrategy.put("IS_TO_AGENT", isToAgent);
                memberStrategy.put("TO_AGENT_GROUP_ID", agentGroupId);
                if(!has) {
                    MemberConfigUtil.createChannelStrategy(configId, channelKey, channelName, memberStrategy);
                }else{
                    MemberConfigUtil.updateChannelStrategy(configId, channelKey, memberStrategy);
                }

                result.incrementSuccess();
                
            } catch (Exception e) {
                result.addError("第" + rowData.get("ROW_INDEX") + "行：处理失败 - " + e.getMessage());
            }
        }

        //更新每个身份策略的关联渠道
        for (String configId : memberConfigMap.values()) {
            MemberConfigUtil.reloadMemberChannelList(configId);
        }

        return result;
    }
    
    /**
     * 创建会员身份策略配置
     */
    private void createMemberConfig(String configId, String memberId, String memberName, 
                                  int rankIndex, int isToAgent, String agentGroupId) throws SQLException {
        EasyRecord record = new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CONFIG", "CONFIG_ID");
        record.setPrimaryValues(configId);
        record.set("MEMBER_ID", memberId);
        record.set("MEMBER_NAME", memberName);
        record.set("RANK_INDEX", rankIndex);
        record.set("IS_TO_AGENT", isToAgent);
        record.set("TO_AGENT_GROUP_ID", agentGroupId);
        record.set("QUEUE_LEVEL", rankIndex); // 默认排队等级等于身份等级
        record.set("CREATE_TIME", EasyDate.getCurrentDateString());
        record.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        
        this.getQuery().save(record);
    }

    /**
     * 导入结果类
     */
    private static class ImportResult {
        private List<String> errors = new ArrayList<>();
        private int successCount = 0;
        
        public void addError(String error) {
            errors.add(error);
        }
        
        public void incrementSuccess() {
            successCount++;
        }
        
        public boolean hasError() {
            return !errors.isEmpty();
        }
        
        public String getErrorMessage() {
            return java.lang.String.join("；", errors);
        }
        
        public int getSuccessCount() {
            return successCount;
        }
    }

    @Override
    protected String getResId() {
        return null;
    }
}
