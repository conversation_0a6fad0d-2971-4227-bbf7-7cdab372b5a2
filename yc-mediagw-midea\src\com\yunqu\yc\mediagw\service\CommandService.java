package com.yunqu.yc.mediagw.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.mediagw.base.*;
import com.yunqu.yc.mediagw.command.Command;
import com.yunqu.yc.mediagw.command.impl.*;
import com.yunqu.yc.mediagw.enums.MessageEnum;
import com.yunqu.yc.mediagw.http.Proxy;
import com.yunqu.yc.mediagw.log.CommonLogger;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.*;
import com.yunqu.yc.mediagw.service.largeModel.LargeModelHandle;
import com.yunqu.yc.mediagw.util.*;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceException;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;
import java.util.Map;

/**
 * 全媒体消息统一处理服务
 * 对内提供，外部通过接口访问
 *
 * <AUTHOR>
 */
public class CommandService extends IService {

    private static EasyCache cache = CacheManager.getMemcache();

    private static Logger logger = CommonLogger.getLogger("AIGC");

    private long thid;
    @Override
    public JSONObject invoke(JSONObject jsonObject) throws ServiceException {
        thid = Thread.currentThread().getId();
        VisitorModel visitor = null;
        try {

            String channelKey = StringUtils.trimToEmpty(jsonObject.getString("channelKey"));
            EntContext entContext = EntContext.getContext(channelKey);
            if (entContext == null || StringUtils.isBlank(entContext.getEntId())) {
                MediagwLogger.getLogger().error("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "] getEntInfo by ChannelKey fail.");
                return null;
            }

            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]收到客户端消息 << " + jsonObject);
            Object quoteData = JSONPath.eval(jsonObject, "$.data.userInfo.userData.quoteData");
            visitor = this.getVisitor(jsonObject);
            String sessionId = visitor.getSessionId();

            RequestDataModel requestDataModel = getRequestModel(jsonObject);
            JSONObject dataObject = requestDataModel.getData();

            String msgType = dataObject.getString("msgType");//消息类型，取值：text文本，  image图片，video视频 ，file附件 ，vox语音 ，event事件消息
            String msgContent = dataObject.getString("msgContent");
            String resMsgContent = dataObject.getString("msgContent");

            //替换表情代码为中文
            WeiXinEmoji emoji = WeiXinEmoji.getEmoji();
            msgContent = emoji.replaceEmojiCoreToTxt(msgContent);

            msgContent = StringUtils.isNotBlank(msgContent) ? msgContent.trim() : "";
            //替换请求来源消息中的文件服务器地址
            msgContent = entContext.replaceFileServerUrl(msgType,msgContent, "in");
            dataObject.put("msgContent", msgContent);

            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "]，bizType: " + visitor.getBizType());

            JSONObject userInfo = visitor.getUserInfo();
            dataObject.put("userInfo", visitor.getSimpleUserInfo());

            visitor.setData(dataObject.clone());

            if(quoteData!=null){
                JSONPath.set(dataObject, "$.userInfo.userData.quoteData", quoteData);
            }
            //1.01#20200214-1
            //非正在客服聊天时发起视频接入或关闭视频
            if (!"zxkf".equalsIgnoreCase(visitor.getBizType()) && "event".equals(msgType) && msgContent.contains("Video")) {
                if ("connVideo".equals(msgContent) || "inviteVideo".equals(msgContent)) {
                    requestDataModel.getData().put("msgContent", "当前未咨询人工客服，无法发起视频连接");
                    ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                }
                return null;
            }

            CommonUtil.checkImageMsg(channelKey,sessionId,jsonObject);

            //第一次进来，发送欢迎语请求，并生成本次的chatSessionid,专家渠道不推送导航
            if ("welcome".equalsIgnoreCase(visitor.getBizType())) {
                visitor.setSatisfy(false);
                visitor.setSatisfyRobot(false);
                if( !"000000".equals(channelKey)){
                    //非直接输入按键的推送欢迎语
                    String keyCode = visitor.getUserInfo().getString("keyCode");

                    //如果有带入会话id则直接使用
                    String chatSessionId = dataObject.getString("robotChatId");
                    if(StringUtils.isBlank(chatSessionId)){
                        chatSessionId = RandomKit.uniqueStr();
                    }

                    visitor.setChatSessionId(chatSessionId);
                    getUserMobile4a(visitor);
                    getUserLevel(visitor);
                    getUserMember(visitor);
                    requestDataModel.getData().put("chatSessionId", visitor.getChatSessionId());
                    JSONObject keyCodekey = entContext.getCKey(keyCode);

                    if (keyCodekey != null) {
                        //直接输入按键的将直接接入按键
                        visitor.setBizType("selectKey");
                        visitor.setSelectKey(keyCode);//设置按键
                        msgContent = keyCode;
                        requestDataModel.getData().put("msgContent", keyCode);
                    } else {
                        visitor.getUserInfo().remove("keyCode");
                        requestDataModel.getData().getJSONObject("userInfo").remove("keyCode");
                        Command command = new WelcomeCommand();
                        command.service(requestDataModel.toJSON());
                        return null;
                    }
                    //1.01#20210716-1
                    String event = dataObject.getString("event");
                    if ("cancelInviteVideo".equalsIgnoreCase(event)) {
                        MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 当前会话状态[welcome]，客户端发送event=cancelInviteVideo，并携带keyCode=" + keyCode + "，此时不进入按键流程!");
                        return null;
                    }
                }
            }

            // 统一处理结束事件
            if ("88".equals(msgContent) || "88".equals(msgContent.replace("\n", ""))) {
                ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);
                //清除按键信息
                visitor.clearSelectKey();
//				visitor.clearAgent();//1.01#20210527-1
                requestDataModel.setEvent("end");

                ////2.0#20201014-1 更新接入记录
                if ("robot".equalsIgnoreCase(visitor.getBizType())) {
                    String dateTime = EasyCalendar.newInstance().getDateTime("-");
                    JSONObject accesssData = new JSONObject();
                    accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                    accesssData.put("ROBOT_END_TIME", dateTime);
                    accesssData.put("END_TIME", dateTime);
                    accesssData.put("CLEAR_CAUSE", 1);//1.机器人正常结束
                    AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);

                    //通知机器人结束会话
//                    CommonUtil.sendMsgToRobot(jsonObject, visitor);
                    ChatClickTagLog.getInstance().closeChat(channelKey,sessionId,visitor.getChatSessionId());
                    CommonUtil.notifyRobotLogout(visitor,"CommandService:msgContent=="+msgContent);
                }

                // 收到结束留言命令
                if ("word".equalsIgnoreCase(visitor.getBizType())) {
                    msgContent = "感谢您的留言，我们会尽快跟进处理！";
                    requestDataModel.getData().put("msgContent", msgContent);
                    requestDataModel.getData().put("msgType", "text");
                    visitor.setBizType("welcome");
                    ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                    return null;
                }

                //20210106 增加强制结束会话标记，不进入满意度，在发起视频会话时，用户点击强制结束上一通文本会话时发送data.notSatisfy=1。
                int notSatisfy = requestDataModel.getData().getIntValue("notSatisfy");
                if ("zxkf".equals(visitor.getBizType()) && notSatisfy == 1) {
                    cache.put(MediaConstants.NOT_IN_SATISFY_FLAG + sessionId, "1", 60);
                    visitor.setBizType("welcome");
                    MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 用户点击强制结束上一通文本会话，进入视频会话时发送data.notSatisfy=1");
                }

                Command command = new ByeCommand();
                command.service(requestDataModel.toJSON());
                return null;
            }

            //坐席开始咨询专家
            String notBizType = "zxkf,queue,satisfy";
            if ("000000".equals(channelKey) && !notBizType.contains(visitor.getBizType())) {
                visitor.setBizType("getAgent");
                visitor.setChatSessionId(RandomKit.uniqueStr());
                requestDataModel.getData().put("chatSessionId", visitor.getChatSessionId());
                visitor.setSelectKey(visitor.getUserInfo().getString("keyCode"));//设置按键
                //保存用户消息
                ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);
            }

//			MediagwLogger.getLogger().info("isWorkTime<"+isWorkTime+">  << time1:"+time1+"time2:"+time2+"time3:"+time3+"time4:"+time4+",conf:"+conf.toJSONString());


            //进入选择按键事件
            if ("selectKey".equalsIgnoreCase(visitor.getBizType())) {
                //20201009保存用户输入的按键内容
                if (!"zxkf".equals(resMsgContent)) {
                    ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);
                }
                String intoSelectKeyFlag = CacheUtil.get("intoSelectKeyFlag:" + sessionId);
                if(intoSelectKeyFlag!=null){
                    MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] " +
                            "用户会话已进入选择按键状态（bizType=selectKey），按键逻辑未处理完成，本次用户消息不处理！intoSelectKeyFlag："+intoSelectKeyFlag);
                    return null;
                }
                userInfo = visitor.getUserInfo();

                //20210107直接输入按键的将直接接入按键
                String keyCode = visitor.getUserInfo().getString("keyCode");
                JSONObject keyCodekey = entContext.getCKey(keyCode);

                //如果有带入会话id则直接使用
                String chatSessionId = dataObject.getString("robotChatId");
                if(StringUtils.isNotBlank(chatSessionId)){
                    visitor.setChatSessionId(chatSessionId);
                }
                if (keyCodekey != null) {
                    visitor.setSelectKey(keyCode);//设置按键
                    msgContent = keyCode;
                    requestDataModel.getData().put("msgContent", keyCode);
                } else {
                    visitor.getUserInfo().remove("keyCode");
                    requestDataModel.getData().getJSONObject("userInfo").remove("keyCode");
                }
                JSONObject ckey = entContext.getCKey(msgContent.replace("\n", ""));

                MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 进入选择按键事件，选择按键,key:" + ckey);
                //KEY_TYPE按键类型，1 转人工 2 转机器人 3 回复语 4 工作时间优先人工
                int keyType = 2;
                String keyContent = "";
                if (ckey == null) {
                    Command command = new WelcomeCommand();
                    command.service(requestDataModel.toJSON());
                    return null;
                }
                visitor.setSelectKey(msgContent);//设置按键
                keyType = ckey.getIntValue("KEY_TYPE");
                keyContent = ckey.getString("KEY_CONTENT");
                userInfo.put("keyName", ckey.getString("KEY_NAME"));
                userInfo.put("keyCode", ckey.getString("KEY_CODE"));
                visitor.setUserInfo(userInfo);
                //携带用户所选的导航名称
                requestDataModel.getData().put("userInfo", visitor.getSimpleUserInfo());

                //回复语按键，直接回复按键的回复内容
                if (keyType == 3) {//KEY_TYPE按键类型，1 转人工 2 转机器人 3 回复语
                    visitor.setBizType("selectKey");
                    requestDataModel.getData().put("msgContent", keyContent);
                    requestDataModel.getData().put("msgType", "text");
                    ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                    //20210401 发送回复语之后要清空按键信息，防止进入死循环
                    requestDataModel.setEvent("end");
                    visitor.clearSelectKey();
                    return null;
                }

                //每个访客会话初始状态第一次进入按键流程，设置10秒的标识，防止短时间内访客发送新消息又再次进入按键流程（转人工或初始化机器人）
                CacheUtil.put("intoSelectKeyFlag:" + sessionId,visitor.getChatSessionId(),10);

                //只要选择了正确按键（机器人，人工）就写入接入记录
                JSONObject accesssData = new JSONObject();
                String dateTime = EasyCalendar.newInstance().getDateTime("-");
                accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                accesssData.put("ENT_ID", entContext.getEntId());
                accesssData.put("SESSION_ID", sessionId);
                accesssData.put("CHANNEL_ID", entContext.getChannelId());
                accesssData.put("CHANNEL_KEY_ID", ckey.getString("KEY_ID"));
                accesssData.put("START_TIME", dateTime);
                accesssData.put("CLEAR_CAUSE", 0);//0.初始状态
                AccessRecord.getInstance().saveAccessRecord(visitor.getChannelKey(), accesssData);
                ChatClickTagLog.getInstance().save(channelKey,sessionId,visitor.getChatSessionId());
                // 保存手机号拓展表
                JSONObject phoneData = new JSONObject();
                phoneData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                phoneData.put("ENT_ID", entContext.getEntId());
                phoneData.put("SESSION_ID", sessionId);
                phoneData.put("PHONE", visitor.getUserInfo().getString("mobile"));
                savePhone(channelKey,phoneData);
                //转人工按键，不在工作时间内，提示用户留言
                if (!entContext.isWorkTime() && keyType == 1) {
                    MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不在工作时间内，提示用户留言");

                    accesssData = new JSONObject();
                    accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                    accesssData.put("END_TIME", dateTime);
                    accesssData.put("CLEAR_CAUSE", 6);//6.非工作时间结束

                    //留言超时时间小于等于0，不进入留言，直接结束会话
                    //判断非工作时间是否开启留言功能
                    long wordTimeout = entContext.wordTimeout();
                    if(!entContext.isUnWork2WordFlag()||wordTimeout <= 0) {
                        MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 非工作时间未开启留言 或者 留言超时时间小于0，不进入留言流程，直接结束会话");
                        visitor.setBizType("welcome");
                        requestDataModel.setEvent("end");
                        visitor.clearSelectKey();
                    }else{
                        visitor.setBizType("word");
                        requestDataModel.setEvent("word");
                        accesssData.put("IS_IN_WORD", 1);
                    }
                    requestDataModel.getData().put("msgContent", entContext.getNotServieMsg());
                    requestDataModel.getData().put("msgType", "text");
                    ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                    //更新接入记录
                    AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
                    return null;
                }

                //直接转人工：1.白名单用户；2.渠道配置-H5客户端配置-机器人开关：关；3.用户标签满足渠道转人工标签  4:渠道配置-H5客户端配置-工作时间优先人工 且工作时间
                if (entContext.checkInWhite(sessionId) || (keyType == 2 && !entContext.isOpenRobot())
                        || entContext.checkUserTag(visitor) || (keyType == 4 && entContext.isWorkTime())) {
                    MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId
                            + "] 直接转人工：1.白名单用户；2.渠道关闭机器人时直接转人工；3.用户标签满足渠道转人工标签;4:工作时间且工作时间优先人工");
                    visitor.setBizType("getAgent");
                } else {
                    Command command = new SelectKeyCommand();
                    command.service(requestDataModel.toJSON());
                    return null;
                }
            }

            if ("zxkf".equalsIgnoreCase(visitor.getBizType())) {
                //过滤空的消息
                String custCont = requestDataModel.getData().getString("msgContent");
                if (StringUtils.isBlank(custCont)) {
                    return null;
                }

                //1.01#20210427-1 接收并处理微信公众号客户端发送的视频挂断事件消息；
                //20210421 兼容美的服务号的视频挂断消息，视频挂断事件，用户点击微信公众号的消息，例如：如当前不便接通，可<a href=\"weixin://bizmsgmenu?msgmenucontent=视频已挂断&msgmenuid=999900\">【点此挂断】</a>；1分钟内未接通，邀请将自动关闭
                if (requestDataModel.getData().containsKey("bizMsgMenuId")) {
                    msgContent = requestDataModel.getData().getString("bizMsgMenuId");
                    MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 收到微信公众号客户端的视频挂断事件，bizMsgMenuId:" + msgContent);
                    msgType = "event";
                }
                //事件消息
                if ("event".equals(msgType)) {
                    //20201207参照《2021年美的用户交互中心平台视频客服项目详细设计说明书-20201123》
                    String event = dataObject.getString("event");
                    if (StringUtils.isBlank(event)) {
                        event = msgContent;
                    }
                    if ("999900".equals(msgContent)) {
                        event = "disagreeInviteVideo";
                    }
                    //视频事件消息
                    if (event.toLowerCase().contains("video")) {
                        JSONObject json = requestDataModel.toJSON();
//						json.put("command",msgContent);
                        json.put("command", event);
                        json.put("connType", "1");
                        new VideoCommand().service(json);
//						MediagwLogger.getLogger().info("<CommandService> channelKey["+channelKey+"]，sessionId["+sessionId+"]收到客户端的视频事件消息并发送到mediacenter，msgContent<"+msgContent+">--->"+json);
                        return null;
                    }
                    //普通消息
                } else {
                    visitor.addZxkfUserChatCount();
                    //对客户消息内容打标签
                    requestDataModel = setTagToCustMsg(requestDataModel);
                    new MessageCommand().service(requestDataModel.toJSON());
                    return null;
                }

            }

            //进入留言流程,机器人不在干预
            if ("word".equalsIgnoreCase(visitor.getBizType())) {
                //保存用户消息
                ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);
//				MediagwLogger.getLogger().info("save word << "+jsonObject.toJSONString());
                //保存留言
                ChatMessage.saveWord(visitor, dataObject);
                return null;
            }


            //如果进入满意度流程
            if ("satisfy".equals(visitor.getBizType())) {

                try {
                    IService service = ServiceContext.getService("MEDIA-SATISFY-SERVICE");
                    if (service != null) {
                        dataObject.put("chatSessionId", visitor.getChatSessionId());
                        dataObject.put("event", visitor.getBizType());
//						MediagwLogger.getLogger().info("<MEDIA-SATISFY-SERVICE> >> "+requestDataModel.toJSON());
                        JSONObject result = service.invoke(requestDataModel.toJSON());
//						MediagwLogger.getLogger().info("<MEDIA-SATISFY-SERVICE>  << "+result);
                        JSONObject _data = result.getJSONObject("data");

                        // 如果收到满意度结束标志，删除当前用户的会话信息
                        dataObject.put("msgType", "text");
                        dataObject.put("msgContent", _data.getString("msgContent"));
                        if ("000000".equals(visitor.getChannelKey())) {
                            dataObject.put("mediaEvent", "close");//用于关闭专家咨询页面的输入框
                        }
                        visitor.setSatisfy(true);
                        jsonObject.put("data", dataObject);
                        jsonObject.put("event", "end");
                        ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                        visitor.setBizType("welcome");
                        visitor.setChatSessionId(RandomKit.uniqueStr());
                        return null;
                    }
                    CommonUtil.notifyRobotLogout(visitor,"CommandService:bizType=satisfy");

                } catch (Exception ex) {
                    MediagwLogger.getLogger().error(ex.getMessage(), ex);
                }
            }

            //如果是机器人事件，则调用机器人接口处理消息,如果异常，机器人返回转客服，或者没有找到服务，则
            if ("robot".equalsIgnoreCase(visitor.getBizType())) {
                if (StringUtils.isBlank(msgContent)) {
                    MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 当前msgContent为空，不咨询机器人!");
                    return null;
                }

                //美居APP原生在线客服发送的提示类型消息不触发机器人
                if ("tips".equals(msgType)) {
                    MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 当前msgType=tips，不咨询机器人!");
                    return null;
                }
                //保存用户消息
                ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);

                //news 消息只传对应的url，如：商品信息url
                JSONObject jsonObject_clone = jsonObject.clone();
                if ("news".equals(msgType)) {
                    try {
                        JSONObject newsObj = JSONObject.parseObject(msgContent);
                        String sendContent = newsObj.getString("agentUrl");
                        if(StringUtils.isBlank(sendContent)){
                            sendContent = newsObj.getString("userUrl");
                        }
                        if(StringUtils.isBlank(sendContent)){
                            sendContent = newsObj.getString("url");
                        }
                        //当没有链接时，发送content内容
                        if(StringUtils.isBlank(sendContent)){
                            sendContent = newsObj.getString("content");
                        }
                        JSONPath.set(jsonObject_clone,"$.data.msgContent",sendContent);
                    } catch (Exception e) {
                        MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 咨询机器人之前，处理news消息异常："+e.getMessage());
                    }
                }

//                MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + visitor.getChannelKey() + "]，sessionId[" + sessionId + "] 咨询机器人： " + msgContent);
                // 当msgType=image时，需要调用aigc接口进行图片识别，在获取到识别结果后需要记录一张埋点表，用于后续的统计分析
                checkImageMsg(channelKey, jsonObject);
                JSONObject result = CommonUtil.sendMsgToRobot(jsonObject_clone, visitor);
//				MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey["+channelKey+"]，sessionId["+sessionId+"] 请求机器人接口响应： "+result);
                if (result == null) {
                    MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 没有找到机器人网关服务，直接转人工!");
                    visitor.setBizType("getAgent");
                } else {
                    jsonObject.put("channelType", visitor.getChannelType());
                    JSONObject robotgwData = result.getJSONObject("data");

                    if ("zxkf".equalsIgnoreCase(robotgwData.getString("command"))) {//机器人解答不了自动转人工，或者主动转人工
                        visitor.setBizType("getAgent");
                        //2.0#20201014-1
                        String dateTime = EasyCalendar.newInstance().getDateTime("-");
                        JSONObject accesssData = new JSONObject();
                        accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                        accesssData.put("END_TIME", dateTime);
                        accesssData.put("ROBOT_END_TIME", dateTime);
                        accesssData.put("CLEAR_CAUSE", 1);//1.机器人正常结束
                        AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
                        //保存机器人小结，继续转人工
                        if (StringUtils.isNotBlank(robotgwData.getString("content"))) {
                            robotgwData.put("msgContent", robotgwData.getString("content"));
                            result.put("data", robotgwData);
                            ChatMessage.saveMessage(visitor.getChannelKey(), result, ChatMessage.MSG_SENDER_ROBOT);
                        }
                        MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "]，BizType[" + visitor.getBizType() + "] 机器人要求转人工！！！！！");
                    } else {
                        visitor.addRobotChatCount();
                        dataObject.put("robotEvaluation", entContext.showRobotEvaluation(sessionId));
                        JSONObject msgObject = new JSONObject();
                        msgObject.put("content", robotgwData.getString("content"));
                        dataObject.put("msgType", robotgwData.getString("msgType"));
                        if (!"text".equals(robotgwData.getString("msgType"))) {
                            msgObject.put("bakup", robotgwData.getString("bakup"));
                            msgObject.put("format", robotgwData.getString("format"));
                            msgObject.put("picurl", robotgwData.getString("picurl"));
                            msgObject.put("url", robotgwData.getString("url"));
                        }
                        dataObject.put("msgContent", msgObject.toJSONString());
                        JSONObject robotData = robotgwData.getJSONObject("robotData");
                        if (robotData == null) {
                            robotData = msgObject;
                            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + visitor.getChannelKey() + "]，sessionId[" + sessionId + "] 咨询机器人，返回CSS工单数据。");
                        }

                        Object bottomNavList = JSONPath.eval(robotData, "$.bottomNavList");
                        if (bottomNavList != null) {
                            robotData.put("bottomNavList", entContext.filterBottomNavList(bottomNavList));
                        }
                        dataObject.put("robotData", robotData);
                        dataObject.put("sender", "robot");
                        requestDataModel.setData(dataObject);
                        //机器人消息内容是空的，不保存会话记录
                        if (StringUtils.isBlank(robotgwData.getString("msgContent")) || StringUtils.isBlank(robotgwData.getString("content"))) {
                            MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 机器人消息内容为空！！！ ");
                        }
                        JSONObject cssData = robotData.getJSONObject("cssData");
                        if(cssData!=null){
                            JSONPath.set(robotgwData,"$.robotData.cssData.showTxtContent",1);
                            JSONPath.set(dataObject,"$.robotData.cssData.showTxtContent",1);
                        }
                        requestDataModel.setSerialId(result.getString("serialId"));
                        ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
                        robotgwData.put("msgContent", msgObject.toJSONString());
                        result.put("data", robotgwData);
                        ChatMessage.saveMessage(visitor.getChannelKey(), result, ChatMessage.MSG_SENDER_ROBOT);
                        return null;
                    }
                }
            }

            //机器人结束聊天后，进入getAgent流程
            //1.01#20200103-1
            if ("getAgent".equalsIgnoreCase(visitor.getBizType())) {
                visitor.setRobotChatCount(0);
                //不在工作时间内
                if (!entContext.isWorkTime()) {
                    JSONObject accesssData = new JSONObject();
                    String dateTime = EasyCalendar.newInstance().getDateTime("-");
                    accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                    accesssData.put("END_TIME", dateTime);
                    accesssData.put("ROBOT_END_TIME", dateTime);
                    accesssData.put("CLEAR_CAUSE", 6);//6.非工作时间进留言
                    //留言超时时间小于0，不进入留言流程
                    long wordTimeout = entContext.wordTimeout();
                    if (!entContext.isUnWork2WordFlag()||wordTimeout <= 0) {
                        MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不在工作时间内，回复非工作时间提示语，未开启留言 或者 留言超时时间小于0，不进入留言流程！！！ ");
                        visitor.setBizType("welcome");
                        requestDataModel.setEvent("end");
                        visitor.clearSelectKey();
                    } else {
                        MediagwLogger.getLogger().warn("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不在工作时间内，回复非工作时间提示语，进入留言流程！！！ ");
                        //进入留言流程
                        visitor.setBizType("word");
                        requestDataModel.setEvent("word");
                        accesssData.put("IS_IN_WORD", 1);
                    }
                    //回复非工作时间提示语
                    requestDataModel.getData().put("msgContent", entContext.getNotServieMsg());
                    requestDataModel.getData().put("msgType", "text");
                    ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());

                    //更新接入记录
                    AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
                    CommonUtil.notifyRobotLogout(visitor,"CommandService:bizType=getAgent---->NotWorkTime");
                    return null;
                }
                String vSelectKey = visitor.getSelectKey();
                MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 机器人结束聊天后，进入getAgent流程 visitor.getSelectKey()-->" + vSelectKey);
                if (StringUtils.isNotBlank(vSelectKey)) {
                    dataObject.put("msgContent", vSelectKey);
                } else {
                    dataObject.put("msgContent", msgContent);
                }

                // 美的大模型（AIGC）  异步处理
//              如果用户转人工了  开辟一个新的线程去处理
                JSONObject json = new JSONObject();
                json.put("channelKey",visitor.getChannelKey());
                json.put("sessionId",visitor.getSessionId());
                json.put("chatSessionId",visitor.getChatSessionId());
                new Thread(new LargeModelHandle(json)).start();

                requestDataModel.setEvent(visitor.getBizType());
                Command command = new GetAgentCommand();
                command.service(requestDataModel.toJSON());
                return null;
            }

            //正在排队中，不接受88以外的命令
            if ("queue".equals(visitor.getBizType())) {

                //事件消息
                if ("event".equals(msgType)) {

                    //20201207参照《2021年美的用户交互中心平台视频客服项目详细设计说明书-20201123》
                    String event = dataObject.getString("event");
                    if (StringUtils.isBlank(event)) {
                        event = msgContent;
                    }
                    //20210421 兼容视频挂断消息，视频挂断事件，用户点击微信公众号的消息，例如：如当前不便接通，可<a href=\"weixin://bizmsgmenu?msgmenucontent=视频已挂断&msgmenuid=999900\">【点此挂断】</a>；1分钟内未接通，邀请将自动关闭
                    if ("999900".equals(msgContent)) {
                        event = "disagreeInviteVideo";
                    }

                    //视频事件消息
                    if (event.toLowerCase().indexOf("video") > -1) {
                        MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "]正在排队中，客户端取消视频邀请，需要结束文本会话的排队!");
                        //清除按键信息
                        visitor.clearSelectKey();
                        requestDataModel.setEvent("end");
                        Command command = new ByeCommand();
                        command.service(requestDataModel.toJSON());
                        return null;
                    }
                }

                //20201009保存用户正在排队输入的内容
                ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_CUST);
                String queueNo = CacheUtil.get("lastNotifyQueueNo_" + sessionId);
                requestDataModel.setEvent("queue");
                String qmsg = entContext.getInQueueMsg();
                qmsg = qmsg.replaceAll("#sortPos#", queueNo);
                requestDataModel.getData().put("msgContent", qmsg);
                requestDataModel.getData().put("msgType", "text");
                requestDataModel.getData().put("queueNo", queueNo);
                JSONPath.remove(requestDataModel.getData(),"$.userInfo.quoteData");
                ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
//				jsonObject.put("msgContent", msgContent);
//				ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject,ChatMessage.MSG_SENDER_SYS);
            }
        } catch (Exception ex) {
            MediagwLogger.getLogger().error(ex.getMessage(), ex);
        }
        return null;
    }

    public static void savePhone(String channelKey,JSONObject data){
        EasyCalendar easyCalendar = EasyCalendar.newInstance();
        try {
            EasySQL sql = new EasySQL("SELECT COUNT(1) FROM YCBUSI.CC_MEDIA_PHONE_RECORD WHERE 1=1 ");
            sql.append(data.getString("CHAT_SESSION_ID"),"and CHAT_SESSION_ID = ?");
            MediagwLogger.getLogger().info("查询手机号拓展表："+sql.getSQL()+",param:"+JSON.toJSONString(sql.getParams()));
            int count = QueryFactory.getQuery().queryForInt(sql.getSQL(), sql.getParams());
            EntContext context = EntContext.getContext(channelKey);
            EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_PHONE_RECORD"),"CHAT_SESSION_ID");
            record.set("DATE_ID", easyCalendar.getDateInt());
            record.set("CREATE_TIME", easyCalendar.getDateTime("-"));
            if(data!=null&&!data.isEmpty()) {
                record.setColumns(data);
            }
            MediagwLogger.getLogger().info("保存手机号拓展表信息："+JSON.toJSONString(data));
            if (count>0){
                QueryFactory.getQuery().update(record);
            }else {
                QueryFactory.getQuery().save(record);
            }
        } catch (Exception e) {
            MediagwLogger.getLogger().error("保存手机号拓展表失败"+e.getMessage(),e);
        }
    }


    public static void checkImageMsg(String channelKey, JSONObject jsonObject) {
        try {
            JSONObject data = jsonObject.getJSONObject("data");
            String sessionId = data.getString("sessionId");

            String msgType = data.getString("msgType");
            String msgContent = data.getString("msgContent");
            if (!"image".equals(msgType) || StringUtils.isBlank(msgContent)) {
                return ;
            }

            EntContext context = EntContext.getContext(channelKey);
            if (!context.openAigcOcr()) {
                return ;
            }

            // 调用AIGC OCR接口识别图片内容
            logger.info("<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] checkImageMsg");
            msgContent = Constants.getImageDomain() + msgContent;
            JSONObject aigcOcrData = getAigcOcrData(msgContent);
            if (aigcOcrData == null) {
                logger.info("channelKey[" + channelKey + "] AIGC OCR接口识别图片内容失败");
                return ;
            }

            logger.info("AIGC识别出的结果为：" + aigcOcrData.toJSONString());
            saveImageRecognitionResult(channelKey, jsonObject, aigcOcrData);

            data.put("msgType", "text");
            data.put("msgContent", "");
            if ("0".equals(aigcOcrData.getString("code")) && StringUtils.isNotBlank(aigcOcrData.getString("data"))) {
                String dataStr = aigcOcrData.getString("data");
                Map<String, Object> map = JSON.parseObject(dataStr, Map.class);
                logger.info("AIGC大模型返回结果map：" + JSON.toJSONString(map));
                if (!map.isEmpty()){
                    String aigcStr = map.values().stream().findFirst().orElse("").toString();
                    List<String> aigcDataList = JSON.parseObject(aigcStr, List.class);
                    if (!aigcDataList.isEmpty()) {
                        msgContent = aigcDataList.get(0);
                        data.put("msgContent", msgContent);
                    }
                    String key = map.keySet().stream().findFirst().orElse("");
                    switch(key) {
                        case "家电品类":
                            data.put("msgContent", "图片内容识别为家电品类");
                            break;
                        case "部件":
                            data.put("msgContent", "图片内容识别为家电部件");
                            break;
                        case "服务单号":
                            if (!msgContent.startsWith("FW")){
                                data.put("msgContent", "图片内容识别为非FW服务单号");
                            }
                            break;
                        case "故障代码":
//                            data.put("msgContent", "图片识别为故障代码");
                            break;
                        case "信息提示":
                            if (!MessageEnum.isMessageExists(msgContent)){
                                data.put("msgContent", "图片识别为非电子包修卡内容");
                            }
                            break;
                        default:

                    }
                }
            }
            jsonObject.put("data", data);
            logger.info("最终发送给云问的jsonObject:"+jsonObject.toJSONString());
        } catch (Exception e) {
            logger.error("调用AIGC大模型失败: " + e.getMessage(), e);
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }

    private static JSONObject getAigcOcrData(String imgUrl) {
        try {
            JSONObject requestParams = new JSONObject();
            requestParams.put("taskId", RandomKit.randomStr());
            JSONObject inputParam = new JSONObject();
            inputParam.put("resourceUrl", imgUrl);
            requestParams.put("inputParam", inputParam);
            return AIGCUtil.postAIGC(Constants.getAIGCOcrUrl(), requestParams);
        } catch (Exception e) {
            logger.error("调用AIGC大模型失败: " + e.getMessage(), e);
        }
        return null;
    }

    private static void saveImageRecognitionResult(String channelKey, JSONObject jsonObject, JSONObject aigcOcrData) {
        try {
            JSONObject data = jsonObject.getJSONObject("data");
            EasyCalendar cal = EasyCalendar.newInstance();
            Integer dataId = cal.getDateInt();
            String chatSessionId = data.getString("sessionId");
            String mediaContent = data.getString("msgContent");
            String messageId = jsonObject.getString("serialId");
            String successFlag = "0";
            String aigcResponse = "";

            if (aigcOcrData != null) {
                successFlag = "0";
                aigcResponse = aigcOcrData.getJSONObject("data") == null ? "" : aigcOcrData.getJSONObject("data").toJSONString();
            }

            EasyRecord record = new EasyRecord("CC_MEDIA_AIGC_RECORD", "ID");
            record.setPrimaryValues(RandomKit.uniqueStr());
            record.set("DATA_ID", dataId);
            record.set("CHANNEL_KEY", channelKey);
            record.set("CHAT_SESSION_ID", chatSessionId);
            record.set("MESSAGE_ID", messageId);
            record.set("MEDIA_CONTENT", mediaContent);
            record.set("SUCCESS_FLAG", successFlag);
            record.set("AIGC_RESULT", aigcResponse);
            record.set("CREATE_TIME", cal.getDateTime("-"));

            EasyQuery query = QueryFactory.getQuery();
            query.save(record);
        } catch (Exception e) {
            logger.error("保存AIGC识别结果失败: " + e.getMessage(), e);
        }
    }


    /**
     * 对客户消息内容打标签MEDIA-TAG-SERVICE
     *
     * @param requestDataModel
     * @return
     */
    private RequestDataModel setTagToCustMsg(RequestDataModel requestDataModel) {
        try {
            List<String> serviceList = ServiceContext.findByPrefix("MEDIA-TAG-SERVICE");
            if(serviceList==null|| serviceList.isEmpty()){
                return requestDataModel;
            }
            IService service = ServiceContext.getService(serviceList.get(0));
            if (service != null) {
//				MediagwLogger.getLogger().info("<MEDIA-TAG-SERVICE> >> "+requestDataModel.toJSON());
                JSONObject result = service.invoke(requestDataModel.toJSON());
//				MediagwLogger.getLogger().info("<MEDIA-TAG-SERVICE>  << "+result);

                if (result != null) {
                    requestDataModel.setData(result.getJSONObject("data"));
                }
            }
        } catch (ServiceException e) {
            MediagwLogger.getLogger().error("对客户消息内容打标签失败，"+e.getMessage());
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
        return requestDataModel;
    }

    /**
     * 获取用户会员等级
     * @param visitorInfo
     */
    private void getUserLevel(VisitorModel visitorInfo) {
        long thid = Thread.currentThread().getId();
        long timeMillis = System.currentTimeMillis();
        try {
            String channelKey = visitorInfo.getChannelKey();
            String sessionId = visitorInfo.getSessionId();
            JSONObject json = new JSONObject();
            json.put("from", "frommediagw");
            json.put("sender", "MIDEA_AGENT");
            json.put("password", "YQ_85521717");
            json.put("serialId", RandomKit.uniqueStr());
            json.put("command", "custMgrSrhCust");
            json.put("serviceId", "CUSTMGR_INTERFACE");
            json.put("timestamp", timeMillis);
            json.put("busiType", "01");
            json.put("userAcc", "jiangxl6");
            json.put("channelNo", "000000");
            json.put("mobile", visitorInfo.getUserInfo().getString("mobile"));
            json.put("phoneNum", visitorInfo.getUserInfo().getString("mobile"));
            json.put("accountType", visitorInfo.getUserInfo().getString("accountType"));
            json.put("customerId", visitorInfo.getSessionId());
            json.put("chatSessionId", visitorInfo.getChatSessionId());
            json.put("channelKey", visitorInfo.getChannelKey());
            json.put("needLevel", "Y");//是否需要查询客户会员等级名称 Y-需要，N-不需要
            json.put("add", false);//客户资料界面传来的标识
            json.put("custMgrTag", "Y");//是否需要查询客户标签 Y-需要，N-不需要
            json.put("needTag", "Y");//是否需要查询客户标签 Y-需要，N-不需要
            json.put("ddYlable", "Y");//是否需要查询地动仪标签 Y-需要，N-不需要
            json.put("dyylable", "Y");
            String reqUrl = Constants.getInReceiveUrl();

//            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求用户会员等级接口：reqUrl:" + reqUrl + " >> " + json);
            String invoke = Proxy.doPostJson(reqUrl, json);
            long time = System.currentTimeMillis() - timeMillis;
            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求用户会员等级接口响应结果（耗时："+time+"ms） << " + invoke);
            JSONObject userInfo = visitorInfo.getUserInfo();
            if (StringUtils.isBlank(invoke)) {
                userInfo.remove("userLevel");
                userInfo.remove("level");
                userInfo.remove("levelCode");
                userInfo.remove("levelName");
                userInfo.remove("feelevelName");
                userInfo.remove("levelCode");
                userInfo.remove("lightWord");
                userInfo.remove("tags");
                userInfo.remove("ddYlable");
                return;
            }
            JSONObject parseObject = JSONObject.parseObject(invoke);
            if (parseObject == null) {
                userInfo.remove("userLevel");
                userInfo.remove("level");
                userInfo.remove("levelCode");
                userInfo.remove("levelName");
                userInfo.remove("feelevelName");
                userInfo.remove("levelCode");
                userInfo.remove("lightWord");
                userInfo.remove("tags");
                userInfo.remove("ddYlable");
                return;
            }
            //先取feelevelName没有再取levelName
            String feelevelName = (String) JSONPath.eval(parseObject, "$.custInfo.feelevelName");
            String levelName = (String) JSONPath.eval(parseObject, "$.custInfo.levelName");
            String mobile = (String) JSONPath.eval(parseObject, "$.custInfo.mobile");
            String isMember = (String) JSONPath.eval(parseObject, "$.custInfo.isMember");
            String userLevel = (String) JSONPath.eval(parseObject, "$.custInfo.userLevel");//优先判断，如果不为空设置为最高级别，
            String levelCode = (String) JSONPath.eval(parseObject, "$.levelCode");//会员等级对应的编码，唯一
            JSONArray tags = parseObject.getJSONArray("tags");
            JSONArray ddYlable = (JSONArray) JSONPath.eval(parseObject, "$.custInfo.ddYlable");
            String level = "level1";
            levelName = "普通会员".equals(levelName) ? "美的会员" : levelName;//1.01#20210517-1
            if (StringUtils.isNotBlank(userLevel)) {
                level = "level6";
            } else {
                level = VisitorInfos.getLevel(levelName);
            }
            //1.01#20210521-1
            feelevelName = StringUtils.isNotBlank(feelevelName) ? feelevelName : levelName;
            feelevelName = StringUtils.isNotBlank(feelevelName) ? feelevelName : "客户";
            levelName = StringUtils.isNotBlank(levelName) ? levelName : "客户";
            userInfo.put("userLevel", userLevel);
            userInfo.put("level", level);
            userInfo.put("levelName", levelName);
            if (StringUtils.isNotBlank(mobile)) userInfo.put("mobile", mobile);
            userInfo.put("feelevelName", feelevelName);
            userInfo.put("levelCode", levelCode);
            //坐席前端所需参数userInfo:{"lightWord":{"#level#":"level5","#levelName#":"钻石会员"}}
            JSONObject lightWord = new JSONObject();
            lightWord.put("#level#", level);
            lightWord.put("#levelName#", levelName);
            lightWord.put("#feelevelName#", feelevelName);
            userInfo.put("lightWord", lightWord);
            userInfo.put("tags", tags);
            userInfo.put("ddYlable", ddYlable);
            if (StringUtils.isNotBlank(isMember)) {
                userInfo.put("isMember", isMember);
            }
            this.setBrandLevelName(parseObject, visitorInfo.getSessionId(), visitorInfo.getChatSessionId());
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }

    private void getUserMobile4a(VisitorModel visitor) {
        JSONObject userInfo = visitor.getUserInfo();
        if (StringUtils.isNotBlank(userInfo.getString("mobile"))) {
            return;
        }
        long timeMillis = System.currentTimeMillis();

        JSONObject json = new JSONObject();
        json.put("sender", "MIDEA_AGENT");
        json.put("password", "YQ_85521717");
        json.put("serialId", RandomKit.uniqueStr());
        json.put("command", "user4A");
        json.put("serviceId", "MIXGW_4A_INTEFACE");
        json.put("timestamp", System.currentTimeMillis());
        JSONObject data = new JSONObject();
        data.put("username", visitor.getSessionId());
        json.put("data", data);
        String sign = MD5Util.getHexMD5(json.getString("sender") + json.getString("password") + json.getString("timestamp") + json.getString("serialId")).toUpperCase();
        //		SecurityUtil.encryptMsgByMD5(json.getString("sender")+json.getString("password")+json.getString("timestamp")+json.getString("serialId"));
        json.put("signature", sign);
        String reqUrl = Constants.getInReceiveUrl();
        try {
//            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + visitor.getChannelKey() + "]，sessionId[" + visitor.getSessionId() + "] 请求4A接口查询用户手机号码：reqUrl:" + reqUrl + " >> " + json);
            String invoke = Proxy.doPostJson(reqUrl, json, null, 3000);
            long time = System.currentTimeMillis() - timeMillis;
            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + visitor.getChannelKey() + "]，sessionId[" + visitor.getSessionId() + "] 请求4A接口查询用户手机号码响应结果（耗时："+time+"ms） << " + invoke);
            if (StringUtils.isBlank(invoke)) {
                return;
            }
            JSONObject reqResult = JSONObject.parseObject(invoke);
            if (reqResult == null) {
                return;
            }
            if (!"000".equals(reqResult.getString("respCode"))) {
                return;
            }
            JSONObject respData = reqResult.getJSONObject("respData");
            if (respData == null) {
                return;
            }
            userInfo.put("mobile", JSONPath.eval(respData, "$.result.mobile"));
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }


    public VisitorModel getVisitor(JSONObject jsonObject) {
        String channelKey = StringUtils.trimToEmpty(jsonObject.getString("channelKey"));
        EntContext entContext = EntContext.getContext(channelKey);

        JSONObject msgData = jsonObject.getJSONObject("data");
        JSONObject msgDataObject = msgData.clone();
        String sessionId = msgDataObject.getString("sessionId");
        JSONObject msgUserInfo = msgDataObject.getJSONObject("userInfo");
        if (msgUserInfo == null) {
            msgUserInfo = new JSONObject();
        }
        msgUserInfo.remove("quoteData");
        JSONObject msgUserData = msgUserInfo.getJSONObject("userData");
        if (msgUserData != null) {
            msgUserData.remove("quoteData");
            msgUserInfo.put("levelName", msgUserData.getString("levelName"));
        }
        //如果访问者信息不存在的时候，创建，初始化设置成为机器人处理。
        VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
        String userBrokerName = MediaConstants.getUserBrokerName();
        if (visitor == null) {
            visitor = new VisitorModel();
            visitor.setSessionId(sessionId);
            visitor.setChatSessionId(RandomKit.uniqueStr());
            visitor.setSeq(VisitorInfos.getInstance().nextSeq());
            visitor.setBrokerName(userBrokerName);
            visitor.setBizType("welcome");

            visitor.setCallbackUrl(jsonObject.getString("callbackUrl"));

            if (StringUtils.isBlank(msgUserInfo.getString("nickname"))) {
                msgUserInfo.put("nickname", "访客" + visitor.getSeq());
            }

            //如果是坐席咨询专家组时，会携带一个agentId和keyCode,此时直接进入在线客服
            if (StringUtils.isNotBlank(msgUserInfo.getString("agentId"))) {
                visitor.setBizType("getAgent");
                visitor.setSelectKey(msgUserInfo.getString("keyCode"));//设置按键

                msgUserInfo.put("account", msgUserInfo.getString("agentId"));
                if (StringUtils.isBlank(msgUserInfo.getString("nickname"))) {
                    msgUserInfo.put("nickname", "坐席" + msgUserInfo.getString("agentId"));
                }
            }
            visitor.setUserInfo(msgUserInfo);
            VisitorInfos.getInstance().addVisitorModel(sessionId, visitor);
        }

        if (StringUtils.isNotBlank(msgUserInfo.getString("phoneNum"))) {
            msgUserInfo.put("mobile", msgUserInfo.getString("phoneNum"));
        }
        if (StringUtils.isNotBlank(msgUserInfo.getString("headImgurl"))) {
            msgUserInfo.put("headimgurl", msgUserInfo.remove("headImgurl"));
        }
        if (StringUtils.isNotBlank(msgUserInfo.getString("headImgUrl"))) {
            msgUserInfo.put("headimgurl", msgUserInfo.remove("headImgUrl"));
        }

        //更新用户信息
        JSONObject oldUserInfo = visitor.getUserInfo();
        String oldNickName = StringUtils.isNotBlank(oldUserInfo.getString("nickname")) ? oldUserInfo.getString("nickname") : "访客" + visitor.getSeq();
        String oldHeadimgurl = oldUserInfo.getString("headimgurl");
        if (StringUtils.isBlank(msgUserInfo.getString("nickname"))) {
            msgUserInfo.put("nickname", oldNickName);
        }
        if (StringUtils.isBlank(msgUserInfo.getString("headimgurl"))) {
            msgUserInfo.put("headimgurl", oldHeadimgurl);
        }

        oldUserInfo.putAll(msgUserInfo);

        if (StringUtils.isNotBlank(msgUserInfo.getString("keyCode"))) {
            visitor.setSelectKey(msgUserInfo.getString("keyCode"));//设置按键
        }

        if (StringUtils.isNotBlank(jsonObject.getString("callbackUrl"))) {
            visitor.setCallbackUrl(jsonObject.getString("callbackUrl"));
        }

        if (StringUtils.isBlank(visitor.getBrokerName())) {
            visitor.setBrokerName(userBrokerName);
        }

        String mobile = (String) JSONPath.eval(msgUserInfo, "$.userData.mobile");
        if (StringUtils.isNotBlank(mobile)) {
            oldUserInfo.put("mobile", mobile);
        }
        oldUserInfo.remove("quoteData");
        if(oldUserInfo.getJSONObject("userData")!=null){
            oldUserInfo.getJSONObject("userData").remove("quoteData");
        }

        //更新渠道配置信息
        visitor.setEntId(entContext.getEntId());
        visitor.setChannelId(entContext.getChannelId());
        visitor.setChannelType(entContext.getChannelType());
        visitor.setChannelKey(channelKey);
        visitor.setLastChatTime(System.currentTimeMillis());
        MediaCacheUtil.put(MediaConstants.BROKER_USER_NAME + sessionId, userBrokerName, Constants.getVisitorCacheTimeOut());

        //更新用户绑定的队列名称
        if ("end".equals(visitor.getBizType()) || "welcome".equals(visitor.getBizType())) {
            VisitorInfos.getInstance().addVisitorModel(sessionId, visitor);
            visitor.setBrokerName(userBrokerName);
        }

        return visitor;
    }

    protected RequestDataModel getRequestModel(JSONObject jsonObject) {

        VisitorModel visitor = getVisitor(jsonObject);
        String channelKey = visitor.getChannelKey();
        EntContext entContext = EntContext.getContext(channelKey);
        EasyCalendar cal = EasyCalendar.newInstance();
        JSONObject dataObject = new JSONObject();
        dataObject.putAll(jsonObject.getJSONObject("data"));
        dataObject.put("userInfo", visitor.getSimpleUserInfo());
        dataObject.put("channelKey", channelKey);
        dataObject.put("channelName", entContext.getChannelName());
        dataObject.put("channelType", entContext.getChannelType());
        dataObject.put("channelId", entContext.getChannelId());
        dataObject.put("chatSessionId", visitor.getChatSessionId());
        dataObject.put("sessionId", visitor.getSessionId());
        if (StringUtils.isBlank(dataObject.getString("msgTime"))) {
            dataObject.put("msgTime", cal.getDateTime("-"));//消息发送时间
        }
        if (StringUtils.isBlank(dataObject.getString("msgType"))) {
            dataObject.put("msgType", "text");//消息发送时间
        }

        RequestDataModel requestDataModel = new RequestDataModel();
        requestDataModel.setSerialId(RandomKit.uniqueStr());
        requestDataModel.setChannelKey(channelKey);
        requestDataModel.setEntId(visitor.getEntId());
        requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
        requestDataModel.setEvent(visitor.getBizType());
        requestDataModel.setData(dataObject);
        requestDataModel.setTimestamp(System.currentTimeMillis() + "");
        return requestDataModel;

    }

    /**
     * 缓存会员等级名称
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2022/3/28 15:37
     * @Param jsonObject:
     * @Param getSessionId:
     * @return: java.lang.String
     */
    private void setBrandLevelName(JSONObject jsonObject, String getSessionId, String chatSessionId) {
        try {
            String feelevelName = "";
            String levelName = "";
            String brand = "";
            JSONObject custInfo = jsonObject.getJSONObject("custInfo");
            if(custInfo!=null){
                feelevelName = custInfo.getString("feelevelName");
                levelName = custInfo.getString("levelName");
                brand = custInfo.getString("brand");
            }

            if (StringUtils.isBlank(brand)) {
                brand = "1";
            }

            //品牌：1美的会员，2小天鹅会员
            String brandName = "";
            if ("1".equals(brand)) { //美的会员
                brandName = "美的";
            } else if ("2".equals(brand)) { //小天鹅会员
                brandName = "小天鹅";
            }

            if (StringUtils.isAllBlank(levelName, feelevelName)) {
                return;
            }
            //合并fullName = brandName + levelName;
            String fullName = brandName;
            if (!"".equals(feelevelName)) {
                fullName += "PRO会员";
            } else if ("普通会员".equals(levelName)) {
                fullName += "会员";
            } else if ("白银会员".equals(levelName)) {
                fullName += "白银会员";
            } else if ("黄金会员".equals(levelName)) {
                fullName += "黄金会员";
            } else if ("铂金会员".equals(levelName)) {
                fullName += "铂金会员";
            } else if ("钻石会员".equals(levelName)) {
                fullName += "钻石会员";
            } else if ("至尊会员".equals(levelName)) {
                fullName += "至尊会员";
            } else if ("至尊会员(比佛利)".equals(levelName)) {
                fullName += "至尊会员(比佛利)";
            }
            // 20250320 美的品牌 新增美粉会员等级（黑钻美粉，白金美粉）
            if("美的".equals(fullName)){
                fullName = "美的会员";
                if ("美的会员".equals(levelName)) {
                    fullName = "美的会员";
                } else if ("蓝卡美粉".equals(levelName)) {
                    fullName = "蓝卡美粉";
                } else if ("银卡美粉".equals(levelName)) {
                    fullName = "银卡美粉";
                } else if ("金卡美粉".equals(levelName)) {
                    fullName = "金卡美粉";
                } else if ("白金卡美粉".equals(levelName)) {
                    fullName = "白金卡美粉";
                } else if ("黑钻卡美粉".equals(levelName)) {
                    fullName = "黑钻卡美粉";
                }
            }
            //缓存用户的会员等级名称，用于yc-mediacenter 使用
            String cacheKey = "levelNameContent_" + getSessionId;
            CacheManager.getMemcache().put(cacheKey, fullName, 24 * 3600);
            MediagwLogger.getLogger().info("setBrandLevelName()--->cacheKey:" + cacheKey + "===" + fullName);

            //保存会话用户会员等级名称
            levelName = StringUtils.isBlank(levelName) ? feelevelName : levelName + "," + feelevelName;
            JSONObject json = new JSONObject();
            json.put("SERIAL_ID", chatSessionId);
            json.put("LEVEL_NAME", levelName);
            EasyRecord record = new EasyRecord("CC_MEDIA_RECORD_LEVEL", "SERIAL_ID").setColumns(json);
            EasyQuery ywQuery = QueryFactory.getYwQuery();
            ywQuery.save(record);
            String dateTime = EasyCalendar.newInstance().getDateTime("-");
            List<JSONObject> ddYlable = (List<JSONObject>) JSONPath.eval(jsonObject, "$.custInfo.ddYlable");
            if (ddYlable != null && !ddYlable.isEmpty()) {
                for (JSONObject object : ddYlable) {
                    String lable = object.toString();
                    json = new JSONObject();
                    json.put("SERIAL_ID", chatSessionId);
                    json.put("LABLE_NAME", lable);
                    json.put("CREATE_TIME", dateTime);
                    record = new EasyRecord("CC_MEDIA_RECORD_LABLE", "SERIAL_ID").setColumns(json);
                    ywQuery.save(record);
                }
            }
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }

    /**
     * 获取用户会员身份
     * @param visitorInfo
     */
    private void getUserMember(VisitorModel visitorInfo) {
        long thid = Thread.currentThread().getId();
        long timeMillis = System.currentTimeMillis();
        try {
            String channelKey = visitorInfo.getChannelKey();
            String sessionId = visitorInfo.getSessionId();
            JSONObject json = new JSONObject();
            JSONObject data = new JSONObject();
            json.put("from", "frommediagw");
            json.put("sender", "CC_MEDIA_VIP");
            json.put("password", "YQ_85521717");
            json.put("timestamp", timeMillis);
            json.put("serialId", RandomKit.uniqueStr());
            String sign = MD5Util.getHexMD5(json.getString("sender") + json.getString("password") + json.getString("timestamp") + json.getString("serialId")).toUpperCase();
            json.put("signature", sign);
            json.put("serviceId", "AS-MEDIA-VIP-SEARCH");
            json.put("command", "getVipInfo");
            json.put("busiType", "01");
            json.put("userAcc", "jiangxl6");
            json.put("channelNo", "000000");
            json.put("data", data);
            data.put("openId", sessionId);
            data.put("mobile", visitorInfo.getUserInfo().getString("mobile"));
            data.put("uid", sessionId);
            String reqUrl = Constants.getInReceiveUrl();

            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户会员身份接口：reqUrl:" + reqUrl + " >> " + json);
            String invoke = Proxy.doPostJson(reqUrl, json);
            long time = System.currentTimeMillis() - timeMillis;
            MediagwLogger.getLogger().info("Thread["+thid+"]<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户会员身份接口响应结果（耗时："+time+"ms） << " + invoke);
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }

}
