package com.yunqu.yc.media.center.model;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.listener.GlobalContextListener;
import com.yunqu.yc.media.center.log.MediaCenterDeamonLogger;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.util.CacheUtil;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;

public class SkillGroup {
	/**
	 * 技能组Id
	 */
	String skillGroupId;
	/**
	 * 技能组名称
	 */
	String skillGroupName;
	/**
	 * 企业Id
	 */
	String entId;
	/**
	 * 策略
	 */
	String policy;
	
	/**
	 * 加载状态
	 */
	String loadStatus;
	
	
	String keyId;
	
	
	//关联的所有坐席集合
	Set<Agent> agents = new HashSet<Agent>();
	
	//在线坐席集合
	private Set<Agent>  onlineAgents  = new HashSet<Agent>();

	//身份策略排队队列有序集合，按照身份策略中的“排队优先级”
	private Map<String,LinkedBlockingQueue<UserSession>> memberQueueMap = new LinkedHashMap<>();

	//排队队列有序集合，必须按照会员等级由高到低写入，vip1用户>PRO会员=钻石会员>黄金会员>美的会员=普通会员>普通用户
	private Map<String,LinkedBlockingQueue<UserSession>> defQueueMap = new LinkedHashMap<>();

	private Thread thread;
	
	
	private boolean runState =  true;
	
	private int idxOrder = 0;
	
	//2.0#20200214-1
	public SkillGroup() {
		//初始化会员等级队列
		defQueueMap.put("level7", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level6", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level5", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level4", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level3", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level2", new LinkedBlockingQueue<UserSession>(10000));
		defQueueMap.put("level1", new LinkedBlockingQueue<UserSession>(10000));
	}

	public  void stopQueueThread(){
		this.runState = false;
		try {
			thread.interrupt();
		} catch (Exception e) {
		}
		
	}
	
	public  void initQueueThread(){
		thread  = new Thread(new QueueThread());
		thread.start();
	}
	
	/**
	 * 判断是否有空闲的坐席
	 * @return
	 */
	public boolean  hasFeeAgent(){
		HashSet<Agent> agents1 = new HashSet<>(onlineAgents);
		for(Agent agent :agents1){
			if(agent.isReady() && (!agent.isBusy())) return true;
		}
		return false;
	}

	/**
	 * 判断是否有置闲的坐席
	 * @return
	 */
	public boolean  hasReadyAgent(){
		HashSet<Agent> agents1 = new HashSet<>(onlineAgents);
		for(Agent agent :agents1){
			if(agent.isReady()) return true;
		}
		return false;
	}
	
	//释放技能组相关的信息
	public void release(){
		onlineAgents.clear();
		for(Agent agent:agents){
			agent.removeSkillGroup(this.skillGroupId);
		}
	}
	
	public boolean hasOnlineAgent(){
		if(onlineAgents.size()>0) return true;
		return false;
	}
	
	/**
	 * 添加在线坐席
	 * @param agent
	 */
	public void addOnlineAgent(Agent agent){
		if(onlineAgents.contains(agent)) return ;
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] SkillGroup.addOnlineAgent("+this.getSkillGroupId()+","+this.getSkillGroupName()+"),当前在线坐席数:"+this.onlineAgents.size()+"，当前登录坐席："+agent);
		onlineAgents.add(agent);
	}
	
	public Thread getThread() {
		return thread;
	}

	public void setThread(Thread thread) {
		this.thread = thread;
	}

	public Set<Agent> getOnlineAgents(){
		return onlineAgents;
	}
	
	public int getOnlineAgentSize(){
		return onlineAgents.size();
	}
	
	public void removeAgent(Agent agent){
		this.agents.remove(agent);
	}
	
	public void removeOnlineAgent(Agent agent){
		if(agent!=null) {
			onlineAgents.remove(agent);
		}
	}

	/**
	 * 获取其他在线置闲坐席
	 * 会话转移到技能组时
	 * @param agentId
	 * @return
	 */
	public Agent getOtherReadyAgent(String agentId){
		Agent agent = null;
		if(onlineAgents.size()==0) {
			return null;
		}
		Collection<Agent> coll = new HashSet<>(onlineAgents);
		for (Agent _agent : coll) {
			if(!_agent.isReady()) continue;
			if(agentId.equals(_agent.getAgentId())) continue;
			if(_agent.getMaxServiceCount() <= 3){
				continue;
			}
			if(agent == null){
				agent = _agent;
				continue;
			}
			if(agent.getCurServiceCount() > _agent.getCurServiceCount()){
				agent = _agent;
				continue;
			}
			//坐席长时间未接入新用户，优先接入该坐席
			if(agent.getLastServiceTime() > _agent.getLastServiceTime()+2000){
				agent = _agent;
			}
		}

		if(agent != null){
			return agent;
		}

		//未匹配到空闲坐席，查找置闲状态服务上限小于等于3
		for (Agent _agent : coll) {
			if(!_agent.isReady()) continue;
			if(agentId.equals(_agent.getAgentId())) continue;
			if(_agent.getMaxServiceCount() > 3){
				continue;
			}
			if(agent == null){
				agent = _agent;
				continue;
			}
			if(agent.getCurServiceCount() > _agent.getCurServiceCount()){
				agent = _agent;
				continue;
			}
			//坐席长时间未接入新用户，优先接入该坐席
			if(agent.getLastServiceTime() > _agent.getLastServiceTime()+1000){
				agent = _agent;
			}
		}
		return agent;
	}
	
	public int getQueueSize(String level){
		int queueCount = 0;
		Set<Entry<String, LinkedBlockingQueue<UserSession>>> memberEntrySet = new HashSet<>(memberQueueMap.entrySet());
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> memberIterator = memberEntrySet.iterator();
		while (memberIterator.hasNext()) {
			Entry<String, LinkedBlockingQueue<UserSession>> next = memberIterator.next();
			queueCount += next.getValue().size();
			if(next.getKey().equals(level)) {
				break;
			}
		}
		Set<Entry<String, LinkedBlockingQueue<UserSession>>> entrySet = this.defQueueMap.entrySet();
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> iterator = entrySet.iterator();
		//叠加会员等级高于当前用户等级的排队用户数
		while (iterator.hasNext()) {
			Entry<String, LinkedBlockingQueue<UserSession>> next = iterator.next();
			queueCount += next.getValue().size();
			if(next.getKey().equals(level)) {
				break;
			}
		}
		return queueCount;
	}

	/**
	 * 访客加入排队队列
	 * @param userSession
	 * @return
	 */
	public synchronized int addQueue(UserSession userSession){
		//2.0#20200214-1 根据用户会员等级标识，加入到对应的队列
		String level = userSession.getLevel();
		String memberId = userSession.getMemberId();
		String sessionId = userSession.getSessionId();
		LinkedBlockingQueue<UserSession> linkedBlockingQueue = null;
		if(StringUtils.isEmpty(memberId)) {
			linkedBlockingQueue = this.memberQueueMap.get(memberId);
		}
		if(linkedBlockingQueue==null) {
			linkedBlockingQueue = this.defQueueMap.get(level);
		}
		if(linkedBlockingQueue==null) {
			linkedBlockingQueue = this.defQueueMap.get("level1");
		}
		if(linkedBlockingQueue.contains(userSession)) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] SkillGroup.addQueue("+this.skillGroupId+","+sessionId+") 用户已存在队列中，无法重新进入排队，level："+level+" ->"+userSession);
			return 0;
		}

		try {
			boolean offer = linkedBlockingQueue.offer(userSession,100,TimeUnit.MILLISECONDS);
			if(!offer) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] SkillGroup.addQueue("+this.skillGroupId+","+sessionId+") offer() return false ->"+userSession);
				return 0;
			}

			EntContext context = EntContext.getContext(this.entId);
			userSession.setQueueTime(System.currentTimeMillis());
			context.addQueueUser(userSession);
			//添加到渠道按键的队列
			userSession.getChannelKey().addQueue(userSession);

			userSession.setState(1);//2.0#20210518-1

			EasyCalendar cal = EasyCalendar.newInstance();
			EasyRecord record = new EasyRecord(EntContext.getContext(this.entId).getTableName("CC_MEDIA_QUEUE"), "SERIAL_ID");
			record.put("SERIAL_ID", userSession.getChatSessionId());
			record.put("DATE_ID", cal.getDateInt());
			record.put("ENT_ID",  this.getEntId());
			record.put("SESSION_ID", userSession.getSessionId());
			record.put("GROUP_ID",this.skillGroupId);
			record.put("CHANNEL_ID", userSession.getChannel().getChannelId());
			record.put("CHANNEL_KEY", userSession.getChannelKey().getChannelKeyId());
			record.put("QUEUE_TIME", cal.getDateTime("-"));
			QueryFactory.getWriteQuery(this.entId).save(record);
			//等待数据库提交事务才加入排队队列，防止添加事务还未提交就接入坐席更新排队信息
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] SkillGroup.addQueue("+this.skillGroupId+","+sessionId+"),userSession level<"+level+"> ->"+userSession);
			int queueNo = getQueueSize(level);
			//缓存实际排队号，每次失效的时候通知
			CacheUtil.put("queueNo_"+userSession.getSessionId(), queueNo+"",userSession.getChannel().getAutoConfig().getQueueNotifyTime());
			CacheUtil.put("userQueueNo_"+userSession.getSessionId(), queueNo+"",userSession.getChannel().getAutoConfig().getQueueNotifyTime());
			CacheUtil.put("userInQueueTime_"+userSession.getSessionId(), System.currentTimeMillis(),userSession.getChannel().getAutoConfig().getQueueNotifyTime());
			//缓存最后通知排队号，只有排队号减小的时候才更新，高级用户插队时不通知也不更新
			int t = (int) (userSession.getChannel().getAutoConfig().getQueueTimeout()/1000);
			CacheUtil.put("lastNotifyQueueNo_"+userSession.getSessionId(), queueNo+"",t);
			context.systemMonitor();

			return queueNo;
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			return 0;
		}
	}
	
	/**
	 * 结束排队
	 * @param userSession
	 * @param cause 结束原因 0 正常接入坐席，1排队超时 2用户主动退出
	 */
	public synchronized void stopQueue(UserSession userSession,int cause){
		String sessionId = userSession.getSessionId();
		try {
			//2.0#20200109-1
			if(userSession.getQueueTime()>0){
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"]  >> SkillGroup.stopQueue("+cause+","+sessionId+")-> sessionId: "+userSession);
				EasyCalendar cal = EasyCalendar.newInstance();
				EasyRecord record = new EasyRecord(EntContext.getContext(this.entId).getTableName("CC_MEDIA_QUEUE"), "SERIAL_ID");
				record.put("SERIAL_ID", userSession.getChatSessionId());
				record.put("END_TIME", cal.getDateTime("-"));
				record.put("QUEUE_STAY_TIME", System.currentTimeMillis() - userSession.getQueueTime());
				record.put("END_CAUSE", cause);
				QueryFactory.getWriteQuery(this.entId).update(record);
				
				//2.0#20201014-1 排队超时结束 更新接入记录的排队结束时间，结束原因：3.排队超时结束
				String dateTime = cal.getDateTime("-");
				JSONObject accessObj = new JSONObject();
				if(cause==1) {
					accessObj.put("CLEAR_CAUSE", 3);//结束原因：3.排队超时结束
				}else if(cause==2) {
					accessObj.put("CLEAR_CAUSE", 4);//结束原因：4.排队主动结束
				}
				accessObj.put("ENT_ID", this.entId);
				accessObj.put("CHAT_SESSION_ID", userSession.getChatSessionId());
				accessObj.put("QUEUE_END_TIME", dateTime);
				accessObj.put("END_TIME", dateTime);
				AccessRecord.getInstance().updateAccessRecord(accessObj);
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		CacheUtil.delete("userQueueNo_"+userSession.getSessionId());
		CacheUtil.delete("userInQueueTime_"+userSession.getSessionId());
		CacheUtil.delete("queueNo_"+userSession.getSessionId());
		CacheUtil.delete("lastNotifyQueueNo_"+userSession.getSessionId());
		//遍历每个等级队列，移除该session
		Collection<LinkedBlockingQueue<UserSession>> membervalues = this.memberQueueMap.values();
		for (LinkedBlockingQueue<UserSession> linkedBlockingQueue : membervalues) {
			Iterator<UserSession> iterator = linkedBlockingQueue.iterator();
			while (iterator.hasNext()) {
				UserSession next = iterator.next();
				if(StringUtils.equals(next.getSessionId(), userSession.getSessionId())) {
					iterator.remove();
				}
			}
		}

		Collection<LinkedBlockingQueue<UserSession>> values = this.defQueueMap.values();
		for (LinkedBlockingQueue<UserSession> linkedBlockingQueue : values) {
			Iterator<UserSession> iterator = linkedBlockingQueue.iterator();
			while (iterator.hasNext()) {
				UserSession next = iterator.next();
				if(StringUtils.equals(next.getSessionId(), userSession.getSessionId())) {
					iterator.remove();
				}
			}
		}
//		Set<Entry<String, LinkedBlockingQueue<UserSession>>> entrySet = this.queueMap.entrySet();
//		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> iterator = entrySet.iterator();
//		while (iterator.hasNext()) {
//			iterator.next().getValue().remove(userSession);
//		}
		userSession.getChannelKey().removeUser(userSession);
		EntContext context = EntContext.getContext(entId);
		context.removeUser(userSession.getSessionId());
		context.removeQueueUser(userSession.getSessionId());
		context.systemMonitor();
		notifyQueueNo(1);
	}
	
	/**
	 * 通知正在排队的用户，实时刷新排队数
	 * 1.访客出队列，通知“最后通知排队号”不为1，“实际排队号”为1的用户。
	 * 2.排队定时5秒触发，通知所有用户，每次通知间隔2分钟（间隔时间系统配置），“实际排队号”大于“当前显示排队号”时通知“当前显示排队号”，小于等于时通知“实际排队号”
	 * @param type 0 排队队列定时触发，1访客出队列触发
	 */
	public void notifyQueueNo(int type) {
		int queueNo = 0;//实际排队号，有更高级用户插队时会增大
		//会员身份策略
		Set<Entry<String, LinkedBlockingQueue<UserSession>>> memberEntrySet = this.memberQueueMap.entrySet();
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> memberIterator = memberEntrySet.iterator();
		while (memberIterator.hasNext()) {
			LinkedBlockingQueue<UserSession> queue = memberIterator.next().getValue();
			if(queue.isEmpty()) {
				continue;
			}
			for (UserSession userSession : queue) {
				queueNo++;
				notifyAllQueue(type,queueNo,userSession);
			}
		}

		//默认排队队列
		Set<Entry<String, LinkedBlockingQueue<UserSession>>> entrySet = this.defQueueMap.entrySet();
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> iterator = entrySet.iterator();
		while (iterator.hasNext()) {
			LinkedBlockingQueue<UserSession> queue = iterator.next().getValue();
			if(queue.isEmpty()) {
				continue;
			}
			for (UserSession userSession : queue) {
				queueNo++;
				notifyAllQueue(type,queueNo,userSession);
			}
 		}
	}

	private void notifyAllQueue(int type,int queueNo,UserSession userSession) {
		int lastNOtifyQueueNo = 0;//最后通知排队号，不会因为有更高级用户插队时而增大
		MessageModel messageModel  = userSession.getMessageModel();
		Channel channel = userSession.getChannel();
//				String msg = channel.getAutoConfig().getQueueMsg();
		String msg = channel.getAutoConfig().getQueueMsgByLevel(userSession.getUserInfo().getString("levelCode"));
		String sessionId = userSession.getSessionId();

		//访客出队列触发时
		if(type==1) {
			CacheUtil.put("userQueueNo_"+sessionId, queueNo+"",channel.getAutoConfig().getQueueNotifyTime());
			CacheUtil.put("userInQueueTime_"+userSession.getSessionId(), userSession.getQueueTime(),userSession.getChannel().getAutoConfig().getQueueNotifyTime());
			if(queueNo!=1) {//实际排队号
				return;
			}
			String object2 = CacheUtil.get("lastNotifyQueueNo_"+sessionId);
			if(StringUtils.isBlank(object2)) {
				return;
			}
			lastNOtifyQueueNo = Integer.parseInt(object2);
			if(lastNOtifyQueueNo==1) {
				return;
			}

			msg = StringUtils.replace(msg, "#sortPos#", queueNo+"");
			messageModel.setMsgContent(msg);
			messageModel.setEvent("queue");
			messageModel.setQueueNo(queueNo+"");
			messageModel.setSender("system");
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			CacheUtil.put("queueNo_"+sessionId, queueNo+"",channel.getAutoConfig().getQueueNotifyTime());//排队通知间隔时间
			int t = (int) (channel.getAutoConfig().getQueueTimeout()/1000);
			CacheUtil.put("lastNotifyQueueNo_"+sessionId, queueNo+"",t);
		}

		//排队定时5秒触发
		if(type==0) {

			Object object = CacheUtil.get("queueNo_"+sessionId);//缓存中存在“实际排队号”表示未到通知间隔时间，不发送通知消息
			if(object!=null) {
				return;
			}

			String object2 = CacheUtil.get("lastNotifyQueueNo_"+sessionId);
			if(StringUtils.isBlank(object2)) {
				return;
			}
			lastNOtifyQueueNo = Integer.parseInt(object2);

			//“实际排队号”大于“当前显示排队号”时通知“当前显示排队号”，小于等于时通知“实际排队号”
			if(queueNo>lastNOtifyQueueNo) {
				msg = StringUtils.replace(msg, "#sortPos#", lastNOtifyQueueNo+"");
				messageModel.setMsgContent(msg);
				messageModel.setEvent("queue");
				messageModel.setQueueNo(queueNo+"");
				messageModel.setSender("system");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				CacheUtil.put("queueNo_"+sessionId, lastNOtifyQueueNo+"",channel.getAutoConfig().getQueueNotifyTime());//排队通知间隔时间
				CacheUtil.put("userQueueNo_"+sessionId, lastNOtifyQueueNo+"",channel.getAutoConfig().getQueueNotifyTime());
				CacheUtil.put("userInQueueTime_"+userSession.getSessionId(), userSession.getQueueTime(),channel.getAutoConfig().getQueueNotifyTime());
				int t = (int) (channel.getAutoConfig().getQueueTimeout()/1000);
				CacheUtil.put("lastNotifyQueueNo_"+sessionId, lastNOtifyQueueNo+"",t);
			}else {
				msg = StringUtils.replace(msg, "#sortPos#", queueNo+"");
				messageModel.setMsgContent(msg);
				messageModel.setEvent("queue");
				messageModel.setSender("system");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				CacheUtil.put("userQueueNo_"+sessionId, queueNo+"",channel.getAutoConfig().getQueueNotifyTime());
				CacheUtil.put("userInQueueTime_"+userSession.getSessionId(), userSession.getQueueTime(),channel.getAutoConfig().getQueueNotifyTime());
				CacheUtil.put("queueNo_"+sessionId, queueNo+"",channel.getAutoConfig().getQueueNotifyTime());//排队通知间隔时间
				int t = (int) (channel.getAutoConfig().getQueueTimeout()/1000);
				CacheUtil.put("lastNotifyQueueNo_"+sessionId, queueNo+"",t);
			}

		}
	}

	/**
	 * 检查用户排队超时
	 */
	private void checkUserTimeout(){
		Set<Entry<String, LinkedBlockingQueue<UserSession>>> memberEntrySet = this.memberQueueMap.entrySet();
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> memberIterator = memberEntrySet.iterator();
		while (memberIterator.hasNext()) {
			LinkedBlockingQueue<UserSession> queue = memberIterator.next().getValue();
			for (UserSession userSession : queue) {
				if(!userSession.isQueueTimeout()){
					continue;
				}
				doUserTimeout(userSession);
			}
		}

		Set<Entry<String, LinkedBlockingQueue<UserSession>>> entrySet = this.defQueueMap.entrySet();
		Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> iterator = entrySet.iterator();
		while (iterator.hasNext()) {
			LinkedBlockingQueue<UserSession> queue = iterator.next().getValue();
			for (UserSession userSession : queue) {
				if(!userSession.isQueueTimeout()){
					continue;
				}
				doUserTimeout(userSession);
			}
		}
	}

	private void doUserTimeout(UserSession userSession){
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 用户排队超时，skillGroupId["+this.skillGroupId+"],sessionId["+userSession.getSessionId()+"]");
		this.stopQueue(userSession, 1);
//					entContext.systemMonitor();
		MessageModel messageModel  = userSession.getMessageModel();
		Channel channel = userSession.getChannel();
		messageModel.setMsgContent(channel.getAutoConfig().getQueueTimeoutMsg());
		messageModel.setClearCause("5");
		messageModel.setMsgType("text");
		messageModel.setEvent("close");
		messageModel.setSender("system");
		String msg = messageModel.toString(RandomKit.randomStr());
		ProducerBroker.sendUserMessage(messageModel.getSessionId(),msg);
		//系统回复语直接入库
		UserSession.getInstance(messageModel.getSessionId()).saveMessage(JSONObject.parseObject(msg),4);
		userSession.setState(0);
	}
	
	
	/**
	 * 实现排队
	 * <AUTHOR>
	 */
	private class QueueThread implements Runnable{
		@Override
		public void run() {
			int checkIndex = 0 ;
			while(GlobalContextListener.runState && runState){
				LinkedBlockingQueue<UserSession> queue = null;

				//1.优先检查会员身份策略队列中是否有排队用户
				Set<Entry<String, LinkedBlockingQueue<UserSession>>> memberEntrySet = new HashSet<>(memberQueueMap.entrySet());
				Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> memberIterator = memberEntrySet.iterator();
				while (memberIterator.hasNext()) {
					LinkedBlockingQueue<UserSession> _queue = memberIterator.next().getValue();
					if(!_queue.isEmpty()){
						queue = _queue;
						break;
					}
				}
				//2.再检查默认排队队列中是否有排队用户
				if(queue==null){
					Set<Entry<String, LinkedBlockingQueue<UserSession>>> entrySet = defQueueMap.entrySet();
					Iterator<Entry<String, LinkedBlockingQueue<UserSession>>> iterator = entrySet.iterator();
					while (iterator.hasNext()) {
						LinkedBlockingQueue<UserSession> _queue = iterator.next().getValue();
						if(!_queue.isEmpty()){
							queue = _queue;
							break;
						}
					}
				}

				try {
					if(queue == null){
						Thread.sleep(100);
						continue;
					}

					checkIndex++;
					if(checkIndex>50){//检查排队超时
						checkIndex = 0;
						checkUserTimeout();
						notifyQueueNo(0);
					}
					
					Agent agent = getAgent();
					if(agent == null){
						Thread.sleep(100);
						continue;
					}
					
					UserSession peek = queue.peek();
					if(peek==null) {
						continue;
					}
					
					if(!agent.addUser(peek,1)) {
						continue;
					}
					UserSession userSession = queue.poll(100,TimeUnit.MILLISECONDS);
					if(userSession==null) {
						agent.removeUser(peek.getSessionId());
						continue;
					}
					agent.updateLastServiceTime();
					userSession.setState(2);
					stopQueue(userSession, 0);
					//获取当前用户的会话ID。
					userSession.setAgentId(agent.getAgentId());
					userSession.setLoginType(1);
					userSession.login();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
					MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
					try {
						Thread.sleep(500);
					} catch (Exception e) {
					}
				}
			}
		}
	}
	
	
	public JSONObject getMonitorInfo(){
		JSONObject groupJson  = new JSONObject();
		groupJson.put("skillGroupId", skillGroupId);
		groupJson.put("skillGroupName", skillGroupName);
		groupJson.put("agentCount", agents.size());
		groupJson.put("onlineAgentCount", onlineAgents.size());
		groupJson.put("hasOnlineAgent", this.hasFeeAgent());
		JSONArray  onlines = new JSONArray();
		//v2.0#20191219-1
		Set<Agent> onlineAgentSet = new HashSet<>(onlineAgents);
		for(Agent agent :onlineAgentSet){
			JSONObject jsonObject  = new JSONObject();
			jsonObject.put("agentId", agent.getAgentId());
			jsonObject.put("agentName", agent.getAgentName());
			jsonObject.put("entId", agent.getEntId());
			jsonObject.put("loginTime", agent.getLoginTime());
			jsonObject.put("agentState", agent.getAgentState());
			jsonObject.put("onlineState", agent.getOnelinStateName());
			jsonObject.put("stateTime", agent.getAgentStateTime());
			jsonObject.put("ipaddr", agent.getIpaddr());
			jsonObject.put("isBusy", agent.isBusy());
			jsonObject.put("isReady", agent.isReady());
			jsonObject.put("totalServiceCount", agent.getTotalServiceCount());
			jsonObject.put("curServiceCount", agent.getCurServiceCount());
			jsonObject.put("maxServiceCount", agent.getMaxServiceCount());
			jsonObject.put("lastServiceTime", agent.getLastServiceTimeString());
			jsonObject.put("lastUserTime", agent.getLastUserMsgTimeString());
			jsonObject.put("lastAgentTime", agent.getLastAgentMsgTimeString());
			
			jsonObject.put("readyLen", agent.getReadyLen());
			jsonObject.put("notReadyLen",agent.getNotReadyLen());

			//检查会话的坐席响应时间
			Set<UserSession> users = new HashSet<>(agent.getUsers().values());
			int replyTimeout60 = 0;//超过60秒未响应的会话数
			int replyTimeout180 = 0;//超过180秒未响应用户的会话数
			for (UserSession u :users){
				if(u.checkAgentMsgTimeoutReal(180*1000)){
					replyTimeout60++;
					replyTimeout180++;
					continue;
				}
				if(u.checkAgentMsgTimeoutReal(60*1000)){
					replyTimeout60++;
				}
			}
			jsonObject.put("replyTimeout60",replyTimeout60);
			jsonObject.put("replyTimeout180",replyTimeout180);
			onlines.add(jsonObject);
		}
		groupJson.put("onlineAgents", onlines);
		
		JSONArray  alls  = new JSONArray();
		
		Set<Agent> agentSet = new HashSet<>(agents);
		for(Agent agent:agentSet){
			JSONObject jsonObject  = new JSONObject();
			jsonObject.put("agentId", agent.getAgentId());
			jsonObject.put("agentName", agent.getAgentName());
			jsonObject.put("entId", agent.getEntId());
			jsonObject.put("loginTime", agent.getLoginTime());
			jsonObject.put("agentState", agent.getLogoutTime());
			jsonObject.put("onlineState", agent.getOnelinStateName());
			jsonObject.put("stateTime", agent.getAgentStateTime());
			jsonObject.put("ipaddr", agent.getIpaddr());
			jsonObject.put("totalServiceCount", agent.getTotalServiceCount());
			jsonObject.put("curServiceCount", agent.getCurServiceCount());
			jsonObject.put("maxServiceCount", agent.getMaxServiceCount());
			jsonObject.put("lastServiceTime", agent.getLastServiceTimeString());
			jsonObject.put("lastUserTime", agent.getLastUserMsgTimeString());
			jsonObject.put("lastAgentTime", agent.getLastAgentMsgTimeString());
			
			jsonObject.put("readyLen", agent.getReadyLen());
			jsonObject.put("notReadyLen",agent.getNotReadyLen());
			alls.add(jsonObject);
		}
		groupJson.put("agents", alls);
		return groupJson;
		
	}
	
	
	/**
	 * 获得可提供服务的坐席
	 * @return
	 */
	public Agent getAgent(){
		Agent agent = null;
//		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] getAgent("+this.skillGroupId+") onlineAgents: -> "+onlineAgents);
		Set<Agent> agentSet = new HashSet<>(onlineAgents);
		for(Agent _agent :agentSet){
			if(!_agent.isReady()) continue;
			if(_agent.isBusy()) continue;
			
			if(!_agent.checkFirstGroup(this.skillGroupId)) {
				continue;
			}
			
			if(agent == null){
				agent = _agent;
				continue;
			}
			if(agent.getLastServiceTime() > _agent.getLastServiceTime()+5000){
				agent = _agent;
			}
		}
		if(agent != null) agent.updateLastServiceTime();
		return agent;
	}
	
	/**
	 * 获取指定在线坐席
	 * @param agentId
	 * @return
	 */
	//#v2.0#20191227-1 fix by lijianping 20191227
	public Agent getAgent(String agentId){
		if(StringUtils.isBlank(agentId)) {
			return null;
		}
		Set<Agent> agentSet = new HashSet<>(onlineAgents);
		for(Agent _agent :agentSet){
			if(agentId.equals(_agent.getAgentId())) {
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentInfo<"+agentId+"> by SkillGroup.getAgent("+agentId+") -> "+_agent.hashCode());
				return _agent;
			}
		}
		return null;
	}
	

	public String getKeyId() {
		return keyId;
	}

	public void setKeyId(String keyId) {
		this.keyId = keyId;
	}

	/**
	 * 往技能组中增加坐席
	 * @param agent
	 */
	public void addAgent(Agent agent){
		agent.addSkillGroup(this.skillGroupId);
		this.agents.add(agent);
	}

	public String getSkillGroupId() {
		return skillGroupId;
	}

	public void setSkillGroupId(String skillGroupId) {
		this.skillGroupId = skillGroupId;
	}

	public String getSkillGroupName() {
		return skillGroupName;
	}

	public void setSkillGroupName(String skillGroupName) {
		this.skillGroupName = skillGroupName;
	}

	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getPolicy() {
		return policy;
	}

	public void setPolicy(String policy) {
		this.policy = policy;
	}

	public String getLoadStatus() {
		return loadStatus;
	}

	public void setLoadStatus(String loadStatus) {
		this.loadStatus = loadStatus;
	}
	
	public Set<Agent> getAgents(){
		return agents;
	}
	
	public int getIdxOrder() {
		return idxOrder;
	}

	public void setIdxOrder(int idxOrder) {
		this.idxOrder = idxOrder;
	}

	public String getOnlineAgentInfo(){
		JSONArray _agents = new JSONArray();
		int readyCount = 0;
		for(Agent agent:onlineAgents){
			if(agent.isReady()) readyCount++;
			_agents.add(agent.getAgentMonitorString());
		}
		return "readyCount:"+readyCount+","+_agents.toJSONString();
	}
	
	public String toString(){
		JSONObject jsonObject  = new JSONObject();
		jsonObject.put("skillGroupId", skillGroupId);
		jsonObject.put("skillGroupName", skillGroupName);
		jsonObject.put("agentCount", agents.size());
		jsonObject.put("onlineAgentCount", this.getOnlineAgentSize());
		jsonObject.put("hasFeeAgent", this.hasFeeAgent());
		JSONArray _agents = new JSONArray();
		for(Agent agent:onlineAgents){
			_agents.add(agent.getAgentMonitorString());
		}
		jsonObject.put("onlineAgents", _agents);
		return jsonObject.toJSONString();
	}
	
	/**
	 * 加载坐席
	 */
	public void reload(){
		
		EntContext  entContext = EntContext.getContext(this.entId);
		
		String sql = "select t1.SKILL_GROUP_ID,t2.USER_ACCT from "+entContext.getTableName( "CC_SKILL_GROUP_USER")+" t1 , CC_USER t2 "
				+ " where t1.USER_ID = t2.USER_ID  and t1.ENT_ID = ?   and t1.BUSI_ORDER_ID = ?  and t1.SKILL_GROUP_ID  = ? ";
		
		try {
			List<JSONObject> rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId,entContext.getBusiOrderId(),this.skillGroupId},new JSONMapperImpl());
			Set<Agent>  dbAgentSet = new HashSet<Agent>();//数据库中有的坐席
			for (JSONObject row : rows) {
				String agentId = row.getString("USER_ACCT");
				Agent agent = entContext.getAgentInfo(agentId);//从企业坐席列表中获取
				if(agent == null) continue;
				dbAgentSet.add(agent);
			}
			
			Set<Agent> _agents = new HashSet<Agent>();
			
			//优先计算出技能组坐席列表中有，而数据库没有的坐席
			for(Agent agent:agents){
				if(!dbAgentSet.contains(agent)){
					_agents.add(agent);
				}
			}
			
			//遍历这些在数据库中没有的坐席并从该技能组坐席列表删除
			for(Agent agent:_agents){
				agent.removeSkillGroup(this.skillGroupId);
				this.removeAgent(agent);
				this.removeOnlineAgent(agent);
			}
			
			//遍历数据库中的坐席，如果技能组列表中不存在这些坐席，则把这些坐席加到技能组列表中
			for(Agent agent:dbAgentSet){
				if(!agents.contains(agent)){
					this.addAgent(agent);
					if(agent.online()) {
						this.addOnlineAgent(agent);
					}
				}
			}
			
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
	}

	/**
	 * 重新加载会员身份策略队列
	 * @param memberConfig 会员身份策略配置列表
	 */
	public void reloadMemberQueue(List<JSONObject> memberConfig){
		if(memberConfig.isEmpty()){
			return;
		}
		LinkedHashMap<String, LinkedBlockingQueue<UserSession>> oldMemberQueueMap = new LinkedHashMap<>(memberQueueMap);
		for (int i = 0; i < memberConfig.size(); i++) {
			String memberId = memberConfig.get(i).getString("MEMBER_ID");
			LinkedBlockingQueue<UserSession> queue = oldMemberQueueMap.get(memberId);
			if(queue == null){
				queue = new LinkedBlockingQueue<>(10000);
			}
			memberQueueMap.remove(memberId);
			memberQueueMap.put(memberId, queue);
		}
		MediaCenterDeamonLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] SkillGroup["+getSkillGroupId()+"] 加载会员身份策略队列完成！策略数量："+memberConfig);
	}
	
}

