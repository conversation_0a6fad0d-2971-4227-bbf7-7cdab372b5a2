<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="robotgw" name="机器人对接网关" package-by="ljp46" package-time="2025-07-14 17:44:25" version="1.0#20250814-1">
    <datasources>
        <datasource description="业务数据源" isnull="true" name="yw-ds"/> 
        <datasource description="Mars数据源" isnull="true" name="mars-ds"/> 
    </datasources>
    <description>
        1.0#20250814-1
            1.对接AIGC人机辅助接口。
        1.0#20250402-1
            1.新增AIGC机器人能力对接。
            2.移除com.yq.busi.common 的引用。
            3.对接云问机器人V5-微信类型渠道增加支持cssData数据。
            4.AIGC接口签名秘钥增加配置项。
        1.0#20250116-1
            1.优化：引导胶囊明细接口返回结果处理逻辑，保持和问答接口返回结果的处理逻辑一致，解决工单卡片上屏的问题。
        1.0#20241205-1
            1.增加文本机器人自助服务及受理需求-CSS工单逻辑：初始化上线自动推送工单，FAQ触发工单，工单操作事件。
            2.优化：缓存访客初始化上线时带的channelKey，为防止问答交互时切换了机器人渠道。
        1.0#20240314-1
            1.新增：人机辅助接口sourceId参数优先读取渠道关联的人机辅助渠道。
    </description>
    <versionHistory>
        1.0#20231108-1
        1.增加云问机器人V5-问题解决率接口，用于提供数据给客服系统-监控-大屏监控。
        1.0#20230925-1
        1.增加特殊访客角色信息同步接口。
        1.0#20221027-1
        1.优化胶囊明细返回结果处理逻辑。
        1.0#20220817-1
        1.优化机器人消息-引导问题 不写入数据库的问题。
        1.0#20220519-1
        1.优化机器人接口返回数据处理，兼容美居APP渠道使用云问V5机器人时，引导问和相似问不显示的问题。富文本内容没有全部展示的问题。
        2.优化机器人接口返回token不合法时，需要主动刷新token，然后重新调用机器人接口。
        1.0#20220414-1 新增猜你想问埋点接口。
        1.0#20220310-1 请求云问机器人接口超时时间由10秒钟改为3秒钟。
        1.0#20220307-1 解决APP和微信渠道接入V5机器人接口，获取的答案渲染问题。
        1.0#20220105-1 解决对接V5机器人的渠道接入的会话，坐席端右键搜索机器人无答案展示的问题
    </versionHistory>
</application>
