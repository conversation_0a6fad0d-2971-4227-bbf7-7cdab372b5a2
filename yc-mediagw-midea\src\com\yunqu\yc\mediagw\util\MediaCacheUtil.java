package com.yunqu.yc.mediagw.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.Constants;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.HashSet;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.locks.ReentrantLock;

/**
 * 全媒体缓存工具类
 * 用于处理缓存时间超过4小时的数据，提供数据库持久化支持
 * <AUTHOR>
 */
public class MediaCacheUtil {

    private static final EasyCache memCache = CacheManager.getMemcache();
    private static final int LONG_CACHE_THRESHOLD = 4 * 3600; // 4小时阈值（秒）
    private static final int LONG_CACHE_MAX_THRESHOLD = 24 * 3600; // 最大缓存时间24小时阈值（秒）

    // 用于同步锁的Map，每个key对应一个锁
    private static final ConcurrentHashMap<String, ReentrantLock> keyLocks = new ConcurrentHashMap<>();

    // 锁使用时间记录，用于清理长时间未使用的锁
    private static final ConcurrentHashMap<String, Long> lockLastUsedTime = new ConcurrentHashMap<>();

    // 锁的最大空闲时间（毫秒），默认10分钟
    private static final long LOCK_MAX_IDLE_TIME = 10 * 60 * 1000L;

    private static Logger logger = MediagwLogger.getCacheDataLogger();

    // 静态初始化块，启动锁清理任务
    static {
        startLockCleanupTask();
    }

    /**
     * 写入缓存
     * 如果失效时间大于4小时，则同时写入数据库
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param expireSeconds 失效时间（秒）
     */
    public static void put(String key, Object value, int expireSeconds) {
        if (StringUtils.isBlank(key) || value == null) {
            logger.warn("MediaCacheUtil.put() - key or value is null");
            return;
        }

        try {
            // 写入内存缓存，不能超过最大缓存时间24小时，防止缓存莫名其妙失效
            memCache.put(key, value, Math.min(expireSeconds, LONG_CACHE_MAX_THRESHOLD));

            // 如果失效时间大于4小时，则写入数据库
            if (expireSeconds > LONG_CACHE_THRESHOLD) {
                writeToDB(key, value, expireSeconds);
            }

        } catch (Exception e) {
            logger.error("MediaCacheUtil.put() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 读取缓存
     * 如果内存缓存不存在，则从数据库查询并重新写入缓存
     *
     * @param key 缓存键
     * @return 缓存值
     */
    public static String get(String key) {
        if (StringUtils.isBlank(key)) {
            return null;
        }

        try {
            // 先从内存缓存获取
            String value = memCache.get(key);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }

            // 内存缓存不存在，从数据库查询
            return readFromDB(key);

        } catch (Exception e) {
            logger.error("MediaCacheUtil.get() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return null;
        }
    }

    /**
     * 删除缓存
     * 同时删除内存缓存和数据库记录
     *
     * @param key 缓存键
     */
    public static void deleteCache(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }
        try {
            // 删除内存缓存
            memCache.delete(key);
        } catch (Exception e) {
            logger.error("MediaCacheUtil.delete() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 删除缓存
     * 同时删除内存缓存和数据库记录
     *
     * @param key 缓存键
     */
    public static void delete(String key) {
        if (StringUtils.isBlank(key)) {
            return;
        }

        ReentrantLock lock = getKeyLock(key);
        lock.lock();
        try {
            // 删除内存缓存
            memCache.delete(key);

            // 删除数据库记录
            deleteFromDB(key);

        } catch (Exception e) {
            logger.error("MediaCacheUtil.delete() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
        } finally {
            lock.unlock();
            // 清理锁对象
            keyLocks.remove(key);
            lockLastUsedTime.remove(key);
        }
    }

    /**
     * 写入数据库
     *
     * @param key 缓存键
     * @param value 缓存值
     * @param expireSeconds 失效时间（秒）
     */
    private static void writeToDB(String key, Object value, int expireSeconds) {
        ReentrantLock lock = getKeyLock(key);
        lock.lock();
        try {
            EasyCalendar cal = EasyCalendar.newInstance();
            String currentTime = cal.getDateTime("-");

            // 计算失效时间
            long currentTimeMillis = System.currentTimeMillis();
            long lostTimeMillis = currentTimeMillis + (expireSeconds * 1000L);
            EasyCalendar lostCal = EasyCalendar.newInstance(new java.util.Date(lostTimeMillis));
            String lostTime = lostCal.getDateTime("-");

            String valueStr = value.toString();

            //内容超过数据库字段最大长度4000，则不写入或更新数据库
            int length = valueStr.getBytes(StandardCharsets.UTF_8).length;
            if(length > 4000){
                logger.warn("MediaCacheUtil.writeToDB() - value.length > 4000 , un writeToDB ! cache key[" + key + "]");
                return;
            }

            // 检查数据库中是否已存在该key
            EasyQuery query = QueryFactory.getQuery();
            JSONObject dbObj = QueryFactory.getQuery().queryForRow("SELECT CACHE_VALUE,LOST_TIME,UPDATE_TIME FROM YCBUSI.CC_MEDIA_CACHE_DATA WHERE CACHE_KEY = ?", new Object[]{key}, new JSONMapperImpl());
            if(dbObj == null || dbObj.isEmpty()){
                // 记录不存在，插入新记录
                EasyRecord insertRecord = new EasyRecord("YCBUSI.CC_MEDIA_CACHE_DATA", "CACHE_KEY");
                insertRecord.setPrimaryValues(key);
                insertRecord.set("CREATE_TIME", currentTime);
                insertRecord.set("UPDATE_TIME", currentTime);
                insertRecord.set("CACHE_VALUE", valueStr);
                insertRecord.set("LOST_TIME", lostTime);
                query.save(insertRecord);
                logger.info("MediaCacheUtil.writeToDB() - inserted key[" + key + "]");
                return;
            }

            // 记录存在，内容不相同 或者 最后更新时间超过10分钟
            String existingValue = dbObj.getString("CACHE_VALUE");
            String lastUpdateTime1 = dbObj.getString("UPDATE_TIME");
            EasyCalendar lastUpdateCal = EasyCalendar.newInstance(lastUpdateTime1, "yyyy-MM-dd HH:mm:ss");
            if (!valueStr.equals(existingValue) || System.currentTimeMillis() > lastUpdateCal.getTimeInMillis()+10*60*1000) {
                EasyRecord updateRecord = new EasyRecord("YCBUSI.CC_MEDIA_CACHE_DATA", "CACHE_KEY");
                updateRecord.setPrimaryValues(key);
                updateRecord.set("UPDATE_TIME", currentTime);
                updateRecord.set("CACHE_VALUE", valueStr);
                updateRecord.set("LOST_TIME", lostTime);
                query.update(updateRecord);
                logger.info("MediaCacheUtil.writeToDB() - updated key[" + key + "]");
            }

        } catch (Exception e) {
            logger.error("MediaCacheUtil.writeToDB() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 从数据库读取
     *
     * @param key 缓存键
     * @return 缓存值
     */
    private static String readFromDB(String key) {
        ReentrantLock lock = getKeyLock(key);
        lock.lock();
        try {
            String value = memCache.get(key);
            if (StringUtils.isNotBlank(value)) {
                return value;
            }

            JSONObject dbObj = QueryFactory.getQuery().queryForRow("SELECT CACHE_VALUE,LOST_TIME FROM YCBUSI.CC_MEDIA_CACHE_DATA WHERE CACHE_KEY = ?", new Object[]{key}, new JSONMapperImpl());

            if (dbObj == null) {
                logger.warn("MediaCacheUtil.readFromDB() - restored key[" + key + "] not found by DB!");
                return null;
            }

            String cacheValue = dbObj.getString("CACHE_VALUE");
            String lostTime = dbObj.getString("LOST_TIME");

            if (StringUtils.isNotBlank(cacheValue) && StringUtils.isNotBlank(lostTime)) {
                // 检查是否过期
                EasyCalendar currentCal = EasyCalendar.newInstance();
                EasyCalendar lostCal = EasyCalendar.newInstance(lostTime, "yyyy-MM-dd HH:mm:ss");

                if (currentCal.getTimeInMillis() < lostCal.getTimeInMillis()) {
                    // 未过期，重新写入内存缓存
                    memCache.put(key, cacheValue, LONG_CACHE_THRESHOLD);
                    logger.info("MediaCacheUtil.readFromDB() - restored key[" + key + "] get from DB,to memory cache");
                    return cacheValue;
                } else {
                    // 已过期，删除数据库记录
                    deleteFromDB(key);
                    logger.info("MediaCacheUtil.readFromDB() - expired key[" + key + "] deleted from DB");
                }
            }

            return null;

        } catch (Exception e) {
            logger.error("MediaCacheUtil.readFromDB() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return null;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 从数据库删除记录
     *
     * @param key 缓存键
     */
    private static void deleteFromDB(String key) {
        try {
            // 直接执行删除SQL
            int i = QueryFactory.getQuery().executeUpdate("DELETE FROM YCBUSI.CC_MEDIA_CACHE_DATA WHERE CACHE_KEY = ?", new Object[]{key});
            logger.info("MediaCacheUtil.deleteFromDB() - deleted key[" + key + "] result："+i);
        } catch (Exception e) {
            logger.error("MediaCacheUtil.deleteFromDB() error for key[" + key + "]: " + e.getMessage());
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 获取指定key的锁对象
     * @param key 缓存键
     * @return 锁对象
     */
    private static ReentrantLock getKeyLock(String key) {
        // 更新锁的最后使用时间
        lockLastUsedTime.put(key, System.currentTimeMillis());
        return keyLocks.computeIfAbsent(key, k -> new ReentrantLock());
    }

    /**
     * 启动锁清理任务，10分钟清理一次
     */
    private static void startLockCleanupTask() {
        Thread cleanupThread = new Thread(() -> {
            int count = 0;
            while(Constants.isRun){
                try {
                    Thread.sleep(3000);
                    count++;
                    if(count<200){
                        continue;
                    }
                    count = 0;
                    cleanupIdleLocks();
                    logger.info(getLockStats());
                    cleanExpiredRecords();
                } catch (InterruptedException e) {
                    logger.warn("Lock cleanup thread interrupted");
                    Thread.currentThread().interrupt();
                    break;
                } catch (Exception e) {
                    logger.error("Lock cleanup task error: " + e.getMessage());
                    logger.error(e.getMessage(), e);
                }
            }
        });
        cleanupThread.setDaemon(true);
        cleanupThread.setName(Constants.APP_NAME+"_MediaCacheUtil-LockCleanup");
        cleanupThread.start();

        logger.info("MediaCacheUtil lock cleanup task started");
    }

    /**
     * 清理长时间未使用的锁
     */
    private static void cleanupIdleLocks() {
        long currentTime = System.currentTimeMillis();
        int cleanedCount = 0;

        // 遍历锁使用时间记录
        HashSet<String> lockKeySet = new HashSet<>(lockLastUsedTime.keySet());
        for (String key : lockKeySet) {
            Long lastUsedTime = lockLastUsedTime.get(key);
            if (lastUsedTime != null && (currentTime - lastUsedTime) > LOCK_MAX_IDLE_TIME) {
                ReentrantLock lock = keyLocks.get(key);
                if(lock==null){
                    lockLastUsedTime.remove(key);
                    continue;
                }
                if (!lock.isLocked()) {
                    // 锁未被使用且超过最大空闲时间，清理它
                    keyLocks.remove(key);
                    lockLastUsedTime.remove(key);
                    cleanedCount++;
                }
            }
        }

        if (cleanedCount > 0) {
            logger.info("MediaCacheUtil cleaned " + cleanedCount + " idle locks, remaining: " + keyLocks.size());
        }
    }

    /**
     * 清理过期的数据库记录
     * 建议定时调用此方法
     */
    public static void cleanExpiredRecords() {
        try {
            EasyQuery query = QueryFactory.getQuery();
            EasyCalendar currentCal = EasyCalendar.newInstance();
            String currentTime = currentCal.getDateTime("-");

            int deletedCount = query.executeUpdate("DELETE FROM YCBUSI.CC_MEDIA_CACHE_DATA WHERE LOST_TIME < ?", new Object[]{currentTime});
            if (deletedCount > 0) {
                logger.info("MediaCacheUtil.cleanExpiredRecords() - cleaned " + deletedCount + " expired records");
            }
        } catch (Exception e) {
            logger.error("MediaCacheUtil.cleanExpiredRecords() error: " + e.getMessage());
            logger.error(e.getMessage(), e);
        }
    }

    /**
     * 获取当前锁的统计信息
     * @return 锁统计信息字符串
     */
    public static String getLockStats() {
        return String.format("Active locks: %d, Lock usage records: %d",
                keyLocks.size(), lockLastUsedTime.size());
    }
}
