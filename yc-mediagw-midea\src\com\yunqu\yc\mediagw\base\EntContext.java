package com.yunqu.yc.mediagw.base;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.log.MediagwIntefaceLogger;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.log.VisitorInfosLogger;
import com.yunqu.yc.mediagw.model.VisitorModel;
import com.yunqu.yc.mediagw.util.SyncLockUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;


public class EntContext {

    private String channelId;
    private String channelName;
    private String channelKey;//渠道key
    private String entId;
    private String resEntId;
    private String channelType;
    private String schemaId;
    private String channelState;//CHANNEL_STATE 状态， 0 正常 1 停用
    private JSONObject channelConf;//渠道配置
    private JSONObject channelAutoConf;//渠道自动回复配置
    private JSONObject ccData;//机器人配置
    private JSONObject styleConf;//渠道风格配置
    private JSONObject mediaVideoConf = new JSONObject();//视频客服相关配置
    private long lastRefreshTime;//最后刷新时间
    private List<JSONObject> channelKeys;//渠道按键集合

    private List<JSONObject> satisf6Data;//满意度配置（satisfy6-卡片）
    private final JSONObject simpleSatisf7Data = new JSONObject();//满意度配置（satisfy7-卡片）简单数据

    private static String defEntSchemaId;
    private long timer = System.currentTimeMillis();

    private static final Logger logger = VisitorInfosLogger.getLogger();
    private static final Map<String, EntContext> contexts = new HashMap<String, EntContext>();

    public static EntContext getContext(String channelKey) {
        if (StringUtils.isBlank(channelKey)) {
            logger.error("EntContext.getContext() error >> channelKey is blank !!");
            return new EntContext("");
        }
        EntContext context = contexts.get(channelKey);
        if (context != null) {
            context.reload();
            return context;
        }
        Object channelLock = SyncLockUtil.getChannelLock(channelKey);
        synchronized (channelLock) {
            context = contexts.get(channelKey);
            if (context != null) {
                context.reload();
            }
            context = new EntContext(channelKey);
            contexts.put(channelKey, context);
            context.init();
        }

        return context;
    }


    public EntContext(String channelKey) {
        this.channelKey = channelKey;
    }


    public void init() {
        try {
            String sql = "select * from CC_CHANNEL   where  CHANNEL_KEY = ?  and CHANNEL_STATE = 0 ";
            JSONObject row = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{this.getChannelKey()}, new JSONMapperImpl());
            if (row == null) {
                logger.error("EntContext.init() error >>cause: channel is not found!");
                return;
            }
            this.entId = row.getString("ENT_ID");
            this.channelId = row.getString("CHANNEL_ID");
            this.channelName = row.getString("CHANNEL_NAME");
            this.channelType = row.getString("CHANNEL_TYPE");
            this.channelConf = JSONObject.parseObject(row.getString("CHANNEL_CONF"));
            this.channelAutoConf = JSONObject.parseObject(row.getString("CHANNEL_AUTO_CONF"));
            this.styleConf = JSONObject.parseObject(row.getString("STYLE_CONF"));
            this.channelState = row.getString("CHANNEL_STATE");
            this.ccData = row.getJSONObject("CC_DATA");

            //查询企业信息
            sql = "select P_ENT_ID,ENT_TYPE,ENT_NAME,ENT_CODE,FEE_CODE,ENT_STATE from CC_ENT where ENT_ID = ?";
            JSONObject entInfo = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{this.entId}, new JSONMapperImpl());
            if ("3".equals(entInfo.getString("ENT_TYPE"))) {
                this.resEntId = entInfo.getString("P_ENT_ID");
            } else {
                this.resEntId = this.entId;
            }
            //企业数据库资源
            sql = "select t1.SCHEMA_ID  from CC_ENT_RES  t1   where t1.ENT_ID = ?";
            this.schemaId = QueryFactory.getQuery().queryForString(sql, new Object[]{this.resEntId});
            if (StringUtils.isBlank(defEntSchemaId)) {
                defEntSchemaId = this.schemaId;

            }
            //渠道按键
            sql = "select *  from CC_CHANNEL_KEY t1  where t1.CHANNEL_ID = ? order by t1.SORT_IDX,t1.KEY_CODE";
            List<JSONObject> keys = QueryFactory.getReadQuery().queryForList(sql, new Object[]{this.getChannelId()}, new JSONMapperImpl());
            this.setChannelKeys(keys);
            this.getSatisf6Data();
        } catch (Exception ex) {
            logger.error("EntContext.init() error >>cause:" + ex.getMessage(), ex);
        }
    }

    /**
     * 检查是否重新加载渠道信息
     * 根据缓存里的"RELOAD_CHANNEL_GW渠道id"判断是否需要重新加载渠道信息，在负载均衡模式下会导致其中一台机器无法实时加载，每十分钟强制重新加载
     */
    public void reload() {

        if (System.currentTimeMillis() - timer > 10 * 60 * 1000) {
            this.init();
            timer = System.currentTimeMillis();
            return;
        }

        String reloadKey = "RELOAD_CHANNEL_GW" + this.channelId;
        String channelReload = CacheManager.getMemcache().get(reloadKey);//渠道配置是否有更改
        if (StringUtils.isBlank(channelReload)) {
            return;
        }
        logger.info("重新加载渠道[" + this.getChannelName() + "]->");
        this.init();
        CacheManager.getMemcache().delete(reloadKey);
    }

    public static String getDefEntTableName(String tableName) throws Exception {
        if (StringUtils.isNotBlank(defEntSchemaId)) {
            return defEntSchemaId + "." + tableName;
        }
        String defEntId = Constants.getContext().getProperty("DEF_ENT_ID", "1000");
        String sql = "select t1.SCHEMA_ID  from CC_ENT_RES  t1   where t1.ENT_ID = ?";
        defEntSchemaId = QueryFactory.getQuery().queryForString(sql, new Object[]{defEntId});
        return defEntSchemaId + "." + tableName;
    }

    public String getChannelId() {
        return channelId;
    }

    public void setChannelId(String channelId) {
        this.channelId = channelId;
    }

    public String getChannelKey() {
        return channelKey;
    }

    public void setChannelKey(String channelKey) {
        this.channelKey = channelKey;
    }

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }


    public String getResEntId() {
        return resEntId;
    }

    public void setResEntId(String resEntId) {
        this.resEntId = resEntId;
    }

    public String getSchemaId() {
        return schemaId;
    }

    public void setSchemaId(String schemaId) {
        this.schemaId = schemaId;
    }

    public String getChannelType() {
        return channelType;
    }

    public void setChannelType(String channelType) {
        this.channelType = channelType;
    }

    public JSONObject getChannelConf() {
        return channelConf;
    }

    public void setChannelConf(JSONObject channelConf) {
        this.channelConf = channelConf;
    }

    public JSONObject getChannelAutoConf() {
        if (channelAutoConf == null) {
            channelAutoConf = new JSONObject();
        }
        return channelAutoConf;
    }

    public void setChannelAutoConf(JSONObject channelAutoConf) {
        this.channelAutoConf = channelAutoConf;
    }

    public String getChannelName() {
        return channelName;
    }

    public void setChannelName(String channelName) {
        this.channelName = channelName;
    }


    public long getLastRefreshTime() {
        return lastRefreshTime;
    }

    public void setLastRefreshTime(long lastRefreshTime) {
        this.lastRefreshTime = lastRefreshTime;
    }


    public List<JSONObject> getChannelKeys() {
        return channelKeys;
    }

    public void setChannelKeys(List<JSONObject> channelKeys) {
        this.channelKeys = channelKeys;
    }


    public String getChannelState() {
        return channelState;
    }


    public void setChannelState(String channelState) {
        this.channelState = channelState;
    }


    public JSONObject getStyleConf() {
        if (styleConf == null) {
            styleConf = new JSONObject();
        }
        return styleConf;
    }


    public void setStyleConf(JSONObject styleConf) {
        this.styleConf = styleConf;
    }

    public JSONObject getCcData() {
        if (ccData == null) {
            ccData = new JSONObject();
        }
        return ccData;
    }

    public JSONObject getMediaVideoConf() {
        //视频客服相关配置
        String sql = "select CONFIG from ycbusi.CC_MEDIA_CONFIG where ENT_ID = ? ";
        try {
            String configStr = QueryFactory.getReadQuery().queryForString(sql, new Object[]{this.getEntId()});
            if (StringUtils.isNotBlank(configStr)) {
                mediaVideoConf = JSONObject.parseObject(configStr);
            }
        } catch (SQLException e) {
        }
        return mediaVideoConf;
    }


    public void setMediaVideoConf(JSONObject mediaVideoConf) {
        this.mediaVideoConf = mediaVideoConf;
    }


    /**
     * 获取渠道按键
     *
     * @param keyCode
     * @return
     */
    public JSONObject getCKey(String keyCode) {
        if (StringUtils.isBlank(keyCode)) {
            return null;
        }
        if (channelKeys == null) {
            return null;
        }
        for (int i = 0; i < channelKeys.size(); i++) {
            if (keyCode.equals(channelKeys.get(i).getString("KEY_CODE"))) {
                return channelKeys.get(i);
            }
        }
        return null;
    }

    public String getTableName(String tableName) {
        return schemaId + "." + tableName;
    }

    /**
     * 替换文件服务器地址
     *
     * @param msgContent 目标文本
     * @param oper       替换类型，in：请求来源，out：回调出去
     * @return
     */
    public String replaceFileServerUrl(String msgType,String msgContent, String oper) {

        String fileTypeStr = "image,video,file,voice";
        if (StringUtils.isAnyBlank(msgType,msgContent) || !fileTypeStr.contains(msgType) || !msgContent.contains("/attachment/out/attachment")) {
            return msgContent;
        }

        JSONObject channelConf = this.getChannelConf();

        if (channelConf == null) {
            MediagwLogger.getLogger().warn("<replaceFileServerUrl> not found channelConf  >> msgContent:" + msgContent + ",  oper:" + oper);
            return msgContent;
        }

        int isReplace = channelConf.getIntValue("IS_REPLACE");
        String defUrl = channelConf.getString("DEF_FILE_SERVER_ADDR");
        String newUrl = channelConf.getString("NEW_FILE_SERVER_ADDR");


        if (isReplace != 1 || StringUtils.isAnyBlank(msgContent, defUrl, newUrl, oper)) {
            return msgContent;
        }
        MediagwLogger.getLogger().info("<replaceFileServerUrl> channelKey(" + channelKey + ")  >> defaultUrl:" + defUrl + ",  newUrl:" + newUrl + ",  oper:" + oper + ",isReplace:" + isReplace);

        msgContent = msgContent.substring(msgContent.indexOf("/attachment/out/attachment"));
        if ("in".equals(oper) && !"/".equals(defUrl)) {
            return defUrl + msgContent;//将请求来源的外网地址替换成默认地址
        }
        return newUrl + msgContent;//将回调出去的消息中的的默认地址替换成外网地址
    }

    /**
     * 检查是否在白名单
     *
     * @param sessionId
     * @return
     */
    public boolean checkInWhite(String sessionId) {
        String inAgentFlag = this.getChannelAutoConf().getString("WHITE_LIST_IN_AGENT_FLAG");//白名单自动转人工，开启：["on"]，关闭：为空或["off"]
        if (!"[\"on\"]".equals(inAgentFlag)) {
            return false;
        }
        try {
            int count = QueryFactory.getQuery().queryForInt("SELECT COUNT(1) FROM CC_WHITE_LIST WHERE PHONENUM=? AND DEAD_LINE>=?", new Object[]{sessionId, EasyCalendar.newInstance().getDateString("-")});
            return count == 1;
        } catch (SQLException e) {
            logger.error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 检查是否开启了机器人
     *
     * @return
     */
    public boolean isOpenRobot() {
        return "1".equals(this.getStyleConf().getString("H5_ROBOT_SWITCH"));
    }

    /**
     * 检查是否显示事项待办
     *
     * @return
     */
    public boolean isShowTodo() {
        return "1".equals(this.getStyleConf().getString("H5_SHOW_TODO"));
    }

    /**
     * 检查是否显示服务工单
     *
     * @return
     */
    public boolean isShowOrders() {
        return "1".equals(this.getStyleConf().getString("H5_SHOW_ORDERS"));
    }

    /**
     * 留言超时时间
     * @return
     */
    public long wordTimeout() {
        return this.getChannelAutoConf().getLongValue("WORD_TIME_OUT_TIME");
    }


    /**
     * 获取机器人欢迎语 VISITOR_ACCESS_ROBOT_MSG
     *
     * @return
     */
//	默认欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG
//	早上（05:00-08:00）欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG_1
//	上午（08:00-11:59）欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG_2
//	中午（12:00-14:00）欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG_3
//	下午（14:00-17:59）欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG_4
//	晚上（18:00-05:00）欢迎语
//	CC_CHANNEL.CHANNEL_AUTO_CONF.VISITOR_ACCESS_ROBOT_MSG_5
    public String getInRobotMsg() {
        EasyCalendar cal = EasyCalendar.newInstance();
        int hour = cal.getCalendar().get(Calendar.HOUR_OF_DAY);
        System.out.println(hour);
        String num = "";
        if (hour >= 5 && hour < 8) {
            num = "1";
        } else if (hour >= 8 && hour < 12) {
            num = "2";
        } else if (hour >= 12 && hour < 14) {
            num = "3";
        } else if (hour >= 14 && hour < 18) {
            num = "4";
        } else if ((hour >= 18 && hour < 23) || hour >= 0 && hour < 5) {
            num = "5";
        }
        String keyName = "VISITOR_ACCESS_ROBOT_MSG_" + num;
        String msg = this.getChannelAutoConf().getString(keyName);
        if (StringUtils.isBlank(msg)) {
            msg = this.getChannelAutoConf().getString("VISITOR_ACCESS_ROBOT_MSG");
        }
        if (StringUtils.isBlank(msg)) {
            msg = "请一句话描述您的问题，我们来帮您解决并转到合适的人工服务。";
        }
        return msg;
    }

    /**
     * 排队过程中，用户发送消息，系统自动回复内容
     * VISITOR_QUEUE_IN_MSG
     *
     * @return
     */
    public String getInQueueMsg() {
        String msg = this.getChannelAutoConf().getString("VISITOR_QUEUE_IN_MSG");
        if (StringUtils.isBlank(msg)) {
            msg = "您当前正在排队，排队号为#sortPos#，请耐心等待，输入88可取消当前排队。";
        }
        return msg;
    }

    /**
     * 人工排队中点击机器消息提示语
     * 进入排队后，点击"猜你想问专区"和"机器人消息"中的按钮，链接，点赞/点踩，自动发送系统消息-"若需要与智能客服交互，您可点击"结束排队"退出人工排队"
     * QUEUE_OPER_MSG
     *
     * @return
     */
    public String getQueueOperMsg() {
        return this.getChannelAutoConf().getString("QUEUE_OPER_MSG");
    }

    /**
     * 获取满意度配置
     * @param sessionId 客户id  openId
     * @param chatSessionId 会话id
     * @param agentPhone 坐席工号
     * @return
     */
    public JSONObject getSatisfTempConfig(String sessionId, String chatSessionId, String agentPhone,String busiType) {
        //satisfy7-表情卡片,satisfy6-星星卡片
        String satisfyType = getChannelConf().getString("SATISFY_TYPE");
        JSONObject tempConfig = new JSONObject();
        tempConfig.put("dataId", getChannelKey()+"_"+satisfyType+"_"+EasyCalendar.newInstance().getDateInt());//渠道+satisfy6+日期id
        tempConfig.put("tempId", satisfyType);
        tempConfig.put("tempType", "2");
        if ("satisfy6".equals(satisfyType)) {
            tempConfig.put("tempData", getSatisf6Data());
        } else if ("satisfy7".equals(satisfyType)) {
            tempConfig.putAll(getSatisf7Data(sessionId,chatSessionId,agentPhone,busiType));
        }
        return tempConfig;
    }

    /**
     * 获取满意度配置，满意度展示方式：satisfy7-表情卡片
     * @return
     */
    public JSONObject getSatisf7Data(String sessionId, String chatSessionId, String agentPhone,String busiType) {
        try {
            //1.Command:saveSatisfy
            JSONObject param = new JSONObject();
            param.put("serviceId", "NEW-SATISFY-MEDIA-SERVICE");
            param.put("command", "saveSatisfy");
            param.put("sessionId", chatSessionId);//会话id
            param.put("workNo", agentPhone);//接待坐席工号
            param.put("channelKey", getChannelKey());//渠道id
            param.put("busiType", busiType);//1热线-短信 2在线-会话中 3服务号 4视频 5-热线服务号(微信) 6-在线会话后
            param.put("custSessionId", sessionId);//用户唯一标识
            IService service = ServiceContext.getService("NEW-SATISFY-MEDIA-SERVICE");
            JSONObject result = service.invoke(param);

            if (result == null || StringUtils.isBlank(result.getString("id"))) {
                MediagwIntefaceLogger.getLogger().error("channelKey[" + this.getChannelKey() + "]获取[satisfy7-表情卡片]满意度模板配置失败，saveSatisfy接口返回结果为空！");
                return new JSONObject();
            }
            //2.command:checkSatisfy
            String id = result.getString("id");
            param.put("command", "checkSatisfy");
            param.put("id", id);
            JSONObject result2 = service.invoke(param);
            if (result2 == null) {
                MediagwIntefaceLogger.getLogger().error("channelKey[" + this.getChannelKey() + "]获取[satisfy7-表情卡片]满意度模板配置失败，checkSatisfy接口返回结果为空！");
                return new JSONObject();
            }
            this.setSimpleSatisf7Data(result2.clone());
            return result2;
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(),e);
        }
        return new JSONObject();
    }

    /**
     * 缓存简单的satisf7Data
     * @param fullData
     */
    private void setSimpleSatisf7Data(JSONObject fullData){
        if (System.currentTimeMillis() - timer > 10 * 60 * 1000) {
            this.simpleSatisf7Data.clear();
        }
        if(!simpleSatisf7Data.isEmpty()){
            return;
        }

        synchronized (SyncLockUtil.getLock(channelKey+".simpleSatisf7Data")) {
            if (!simpleSatisf7Data.isEmpty()) {
                return;
            }
            JSONObject jsonObject = fullData.getJSONObject("data");
            //一级列表
            JSONArray firstData = jsonObject.getJSONArray("child");
            if(firstData==null){
                return;
            }
            JSONArray newFirstData = new JSONArray();
            simpleSatisf7Data.put("validTime",jsonObject.getString("VALID_TIME"));//满意度配置的超时时间
            simpleSatisf7Data.put("title",jsonObject.getString("CHILD_TAG_DESC"));
            simpleSatisf7Data.put("icon",jsonObject.getString("FILE_PATH"));
            simpleSatisf7Data.put("child1",newFirstData);

            for (int i = 0; i < firstData.size(); i++) {
                //一级对象
                JSONObject firstObj = firstData.getJSONObject(i);
                JSONObject newFirstObj = new JSONObject();
                JSONArray newSecondData = new JSONArray();
                newFirstObj.put("id",firstObj.getString("ID"));
                newFirstObj.put("code",firstObj.getString("FIRST_SATISFY_CODE"));
                newFirstObj.put("name",firstObj.getString("MEME_DESC"));
                newFirstObj.put("icon_n",firstObj.getString("UNCHOOSE_EMOJ_PATH"));
                newFirstObj.put("icon_y",firstObj.getString("CHOOSE_EMOJ_PATH"));
                newFirstObj.put("c_title2",firstObj.getString("CHILD_TAG_DESC"));
                newFirstObj.put("child2",newSecondData);
                newFirstData.add(newFirstObj);

                //二级列表
                JSONArray secondData = firstObj.getJSONArray("child");
                if(secondData==null){
                    continue;
                }

                for (int j = 0; j < secondData.size(); j++) {
                    //二级对象
                    JSONObject secondObj = secondData.getJSONObject(j);
                    JSONObject newSecondObj = new JSONObject();
                    JSONArray newThildData = new JSONArray();
                    newSecondObj.put("id",secondObj.getString("ID"));
                    newSecondObj.put("code",secondObj.getString("SECOND_SATISFY_CODE"));
                    newSecondObj.put("name",secondObj.getString("TAG_NAME"));
                    newSecondObj.put("c_title3",secondObj.getString("CHILD_TAG_DESC"));
                    newSecondObj.put("child3",newThildData);
                    newSecondData.add(newSecondObj);

                    //三级列表
                    JSONArray thildData = secondObj.getJSONArray("child");
                    if(thildData==null){
                        continue;
                    }

                    for (int m = 0; m < thildData.size(); m++) {
                        //三级对象
                        JSONObject thildObj = thildData.getJSONObject(m);
                        JSONObject newThildObj = new JSONObject();
                        newThildObj.put("id",thildObj.getString("ID"));
                        newThildObj.put("code",thildObj.getString("THIRD_SATISFY_CODE"));
                        newThildObj.put("name",thildObj.getString("CHILD_TAG_NAME"));
                        newThildData.add(newThildObj);
                    }
                }
            }
        }
    }

    public JSONObject getSimpleSatisf7Data(){
        return simpleSatisf7Data;
    }

    /**
     * 获取满意度配置，满意度展示方式：satisfy6-星星卡片
     * @return
     */
    public List<JSONObject> getSatisf6Data() {
        if (System.currentTimeMillis() - timer > 10 * 60 * 1000) {
            this.satisf6Data.clear();
        }
        if(satisf6Data !=null && !satisf6Data.isEmpty()){
            return satisf6Data;
        }

        synchronized (SyncLockUtil.getLock(channelKey+".satisf6Data")){
            if(satisf6Data !=null && !satisf6Data.isEmpty()){
                return satisf6Data;
            }
            satisf6Data = new ArrayList<>();
            try {
                //获取满意度
                String sql = "select ID,NAME,CODE from ywdb.C_STF_SATISF_QUOTA where 1=1 and CHANNEL_NO = ? and ENABLE_STATUS = ? and TYPE= ? ";
                if(!"000000".equals(channelKey)){
                    sql+="order by CODE desc";//非专家咨询渠道，要返回降序指标项
                }
                satisf6Data.addAll(QueryFactory.getQuery().queryForList(sql, new Object[]{channelKey, "Y", "03"}, new JSONMapperImpl()));

                //查询满意度细项指标
                sql = "select t1.*,t2.NAME,t2.CODE from CC_MEDIA_SATISF_EXT_QUOTA t1,CC_MEDIA_SATISF_EXT_ITEM t2 "
                        + "where t1.ITEM_ID = t2.ID and t1.CHANNEL_NO = ? and t2.ENABLE_STATUS = ? order by t1.SATISF_ID,t2.CODE desc";

                List<JSONObject> itemList = QueryFactory.getQuery().queryForList(sql, new Object[]{channelKey, "01"}, new JSONMapperImpl());

                for (JSONObject satisf : satisf6Data) {
                    JSONArray arr = new JSONArray();
                    for (JSONObject item : itemList) {
                        if (satisf.getString("ID").equals(item.getString("SATISF_ID"))) {
                            arr.add(item);
                        }
                    }
                    satisf.put("itemList", arr);
                }
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            }
            return satisf6Data;
        }
    }

    @Deprecated
    public JSONObject getSasisf(String code) {

        String sql = "select ID,NAME,CODE from ywdb.C_STF_SATISF_QUOTA where 1=1 and CHANNEL_NO = ? and ENABLE_STATUS = ? and CODE = ? order by SORT_NUM";
        try {
            JSONObject obj = QueryFactory.getQuery().queryForRow(sql, new Object[]{channelKey, "Y", code}, new JSONMapperImpl());
            if (obj == null) {
                obj = new JSONObject();
            }
            return obj;
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return new JSONObject();
    }

    /**
     * 是否websocket对接
     *
     * @Description :客户端使用websocket对接
     * <AUTHOR>
     * @Datetime 2021/9/27 14:37
     * @return: boolean
     */
    public boolean isWS() {
        return this.getChannelConf() != null && "OUT".equals(this.getChannelConf().getString("OFFLINE_CHAT_TYPE"));
    }

    /**
     * 是否显示人工会话满意度胶囊
     * @Description :是否显示人工会话满意度胶囊，agentEvaluation，Y：显示主动评价胶囊，N/其他：不显示
     * <AUTHOR>
     * @Datetime 2021/10/8 12:13
     * @Param sessionId: 用户唯一标识
     * @return: boolean
     */
    public String showAgentEvaluation(String sessionId) {
        VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
        if (visitorModel == null) {
            return "N";
        }
        if (!"zxkf".equals(visitorModel.getBizType())) {
            return "N";
        }
        if (visitorModel.isSatisfy()) {
            return "N";
        }
        JSONObject styleConf = getStyleConf();
        int configCount = styleConf.getIntValue("H5_SATISFY_SHOW_COUNT");
        int agentChatCount = visitorModel.getAgentChatCount();
        if (agentChatCount == 0 || configCount == 0 || agentChatCount < configCount) {
            return "N";
        }
        return "Y";
    }

    /**
     * 是否显示机器人会话满意度胶囊
     * @Description :是否显示机器人会话满意度胶囊，robotEvaluation，0 不显示，1 显示"去评价"， 2 显示 "已评价"
     * <AUTHOR>
     * @Datetime 2021/10/8 12:13
     * @Param sessionId: 用户唯一标识
     * @return: boolean   ROBOT_SATISFY_SHOW_COUNT
     */
    public int showRobotEvaluation(String sessionId) {
        VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
        if (visitorModel == null) {
            return 0;
        }
        //不在机器人会话状态
        if (!"robot".equals(visitorModel.getBizType())) {
            return 0;
        }
        //已评价
        if (visitorModel.isSatisfyRobot()) {
            return 2;
        }
        //校验交互轮次
        JSONObject styleConf = getStyleConf();
        int configCount = styleConf.getIntValue("ROBOT_SATISFY_SHOW_COUNT");

        int robotChatCount = visitorModel.getRobotChatCount();
        if (robotChatCount == 0 || configCount == 0 || robotChatCount < configCount) {
            return 0;
        }
        return 1;
    }

    /**
     * 是否显示结束会话胶囊
     *
     * @Description :是否显示人工会话满意度胶囊，closeChat，Y：显示结束会话胶囊，N/其他：不显示
     * <AUTHOR>
     * @Datetime 2021/10/8 14:23
     * @Param sessionId: 用户唯一标识
     * @return: java.lang.String
     */
    public String showCloseChat(String sessionId) {
        VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
        if (visitorModel == null) {
            return "N";
        }
        if (!"zxkf".equals(visitorModel.getBizType())) {
            return "N";
        }
        JSONObject styleConf = getStyleConf();
        int configCount = styleConf.getIntValue("H5_END_SHOW_COUNT");
        int agentChatCount = visitorModel.getAgentChatCount();
        if (agentChatCount == 0 || configCount == 0 || agentChatCount < configCount) {
            return "N";
        }
        return "Y";
    }

    /**
     * 微信网页授权地址
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2021/11/9 14:59
     * @return: java.lang.String
     */
    public String getWXpiAuthUrl() {
        String url = this.getStyleConf().getString("WX_AUTH_URL");
        url = StringUtils.isNotBlank(url) ? url : Constants.getDefWXApiAuthUrl();
        return url;
    }

    /**
     * 微信网页查询用户信息
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2021/11/9 14:59
     * @return: java.lang.String
     */
    public String getWXApiUserInfoUrl() {
        String utl = this.getStyleConf().getString("WX_USER_INFO_URL");
        utl = StringUtils.isNotBlank(utl) ? utl : Constants.getDefWXApiUserInfoUrl();
        return utl;
    }

    /**
     * 微信网页获得Jsapi签名
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2021/11/9 14:59
     * @return: java.lang.String
     */
    public String getWXApiJsapiUrl() {
        String utl = this.getStyleConf().getString("WX_JSAPI_URL");
        utl = StringUtils.isNotBlank(utl) ? utl : Constants.getDefWXApiJsapiUrl();
        return utl;
    }

    /**
     * 生成小程序链接接口地址
     *
     * @Description :生成小程序链接接口地址 https://weixincs.midea.com/wcp/sic/api/WCP_MA_GENERATE_SCHEME/d3cd5a215b7447f986eaef2364b9ef40/meimeics
     * <AUTHOR>
     * @Datetime 2022/3/4 18:20
     * @return: java.lang.String
     */
    public String getWXGenerateSchemeUrl() {
        String utl = this.getStyleConf().getString("WX_GENERATE_SCHEME_URL");
        utl = StringUtils.isNotBlank(utl) ? utl : Constants.getDefWXGenerateSchemeUrl();
        return utl;
    }

    /**
     * 满意度超时时间配置，单位：分钟
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2022/5/25 11:49
     * @return: int
     */
    public int getSatisfyTimeout() {
        int timeOutTime = this.getChannelAutoConf().getIntValue("SATISFY_TIME_OUT_TIME");
        timeOutTime = timeOutTime > 0 ? timeOutTime : 5;//默认五分钟
        return timeOutTime;
    }


    /**
     * 检查用户标签是否满足渠道转人工标签
     *
     * @return
     */
    public boolean checkUserTag(VisitorModel visitorInfo) {
        JSONObject userInfo = visitorInfo.getUserInfo();
        String channelKey = visitorInfo.getChannelKey();
        String sessionId = visitorInfo.getSessionId();

        try {
            JSONArray custTag = userInfo.getJSONArray("tags");
            if (custTag == null) {
                MediagwLogger.getLogger().warn("<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 检查用户标签是否满足渠道转人工标签，未查到用户标签信息");
                return false;
            }

            String configTag = this.getChannelConf().getString("AGENT_TAG_ARR");
            if (StringUtils.isBlank(configTag)) {
                MediagwLogger.getLogger().warn("<CommandService> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 检查用户标签是否满足渠道转人工标签，渠道未配置转人工标签");
                return false;
            }
            for (Object tagObj : custTag) {
                JSONObject obj = (JSONObject) tagObj;
                if (configTag.contains(obj.getString("name"))) {
                    return true;
                }
            }

        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
        return false;
    }

    public boolean isWorkTime() {
        JSONObject conf = getChannelConf();
        String time1 = conf.getString("BEGIN_WORK_TIME1");
        String time2 = conf.getString("END_WORK_TIME1");
        String time3 = conf.getString("BEGIN_WORK_TIME2");
        String time4 = conf.getString("END_WORK_TIME2");
        return TimeChecker.isWorktime(time1, time2, time3, time4);
    }

    /**
     * 非工作时间回复语
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2022/12/12 13:44
     * @return: java.lang.String
     */
    public String getNotServieMsg() {
        JSONObject conf = getChannelConf();
        String time1 = conf.getString("BEGIN_WORK_TIME1");
        String time2 = conf.getString("END_WORK_TIME1");
        String time3 = conf.getString("BEGIN_WORK_TIME2");
        String time4 = conf.getString("END_WORK_TIME2");
        String notServieMsg = getChannelAutoConf().getString("SYSTEM_NOTINSERVICE_MSG");
        if (StringUtils.isBlank(notServieMsg)) {
            notServieMsg = "您好，当前为系统非工作时间，服务时间为#serviceTime#";
        }
        String timeStr = "";
        if (StringUtils.isNotBlank(time1)) {
            timeStr += time1;
        }
        if (StringUtils.isNotBlank(time2)) {
            timeStr += "-" + time2;
        }
        if (StringUtils.isNotBlank(time3) && StringUtils.isNotBlank(time4)) {
            timeStr += "，" + time3 + "-" + time4;
        }
        return notServieMsg.replace("#serviceTime#", timeStr);
    }

    /**
     * 是否开启了AIGC图片识别功能
     * @return
     */
    public boolean openAigcOcr(){
        String aigcOcrUrl = Constants.getAIGCOcrUrl();
        String aigcOcrFlag = getStyleConf().getString("AIGC_OCR_FLAG");
        return "1".equals(aigcOcrFlag) && StringUtils.isNotBlank(aigcOcrUrl);
    }

    /**
     * 获取按键列表导航标题
     * @return
     */
    public String getH5KeyListTitle(){
        return (String) getStyleConf().getOrDefault("H5_KEY_LIST_TITLE","请选择所需要的服务");
    }

    /**
     * 获取满意度开关配置
     * @return 满意度开关状态，true-开启，false-关闭，未配置时默认开启
     */
    public boolean getSatisfySwitch() {
        String value = this.getChannelConf().getString("SATISFY_SWITCH");
        //为空或值为"1"时返回true，值为"0"时返回false，其他情况默认返回true
        return StringUtils.isBlank(value) || !"0".equals(value);
    }

    /**
     * 获取满意度推送提示语配置
     * @return 满意度推送提示语，未配置时返回默认值
     */
    public String getSatisfyPushMsg() {
        String msg = this.getChannelAutoConf().getString("SATISFY_PUSH_MSG");
        if(StringUtils.isBlank(msg)) {
            msg = "为了我们更好服务，请您对我们的服务进行评价";
        }
        return msg;
    }

    /**
     * 获取非工作时间是否进入留言的开关配置
     * @return true-开启(默认), false-关闭
     */
    public boolean isUnWork2WordFlag() {
        String isWordFlag = this.getChannelAutoConf().getString("IS_WORD_FLAG");
        //为空兼容旧版
        return StringUtils.isBlank(isWordFlag) || "1".equals(isWordFlag);
    }

    /**
     * 获取访客排队超时是否自动进入留言的开关配置
     * @return true-开启(默认), false-关闭
     */
    public boolean isQueueTimeout2WordFlag() {
        String isWordFlag = this.getChannelAutoConf().getString("TIMEOUT_INTO_WORD_FLAG");
        //为空兼容旧版
        return StringUtils.isBlank(isWordFlag) || "1".equals(isWordFlag);
    }

    /**
     * “数字人客服”开关
     * @return
     */
    public boolean aigcAgentSwitch() {
        return 1 == this.getStyleConf().getIntValue("AIGC_AGENT_SWITCH") && StringUtils.isNotBlank(getAigcAgentUrl());
    }

    /**
     * “数字人客服”地址
     * @return
     */
    public String getAigcAgentUrl(){
        return this.getStyleConf().getString("AIGC_AGENT_URL");
    }

    /**
     * 过滤“数字人客服”胶囊
     * @param bottomNavList
     * @return
     */
    public Object filterBottomNavList(Object bottomNavList){

        if(bottomNavList instanceof JSONArray){
            JSONArray newList = new JSONArray();
            JSONArray newbottomNavList = (JSONArray) bottomNavList;
            for (int i = 0; i < newbottomNavList.size(); i++) {
                JSONObject navObj = newbottomNavList.getJSONObject(i);
                String name = navObj.getString("name");
                if(!"数字人客服".equals(name)){
                    newList.add(navObj);
                    continue;
                }
                if(aigcAgentSwitch()){
                    newList.add(navObj);
                }
            }
            return newList;
        }
        return bottomNavList;
    }
}
