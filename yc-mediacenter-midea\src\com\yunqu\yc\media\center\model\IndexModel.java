package com.yunqu.yc.media.center.model;

import java.sql.SQLException;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
//import com.yunqu.yc.agent.base.Constants;
//import com.yunqu.yc.agent.base.QueryFactory;
//import com.yunqu.yc.agent.log.CcbarLogger;
//import com.yunqu.yc.agent.log.IndexLogger;
import com.yunqu.yc.media.center.base.Constants;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.log.MediaCenterLogger;

public class IndexModel {

		private static EasyCache cache = CacheManager.getMemcache();

		private static Map<String,IndexModel> agentIndexs = new HashMap<String,IndexModel>();

		/**
		 * 首次登录次数 ，单位：时间戳
		 */
		private  long firstLoginTime;

		/**
		 * 首次服务开始时间，单位：时间戳
		 */
		private  long  firstServiceTime;
		/**
		 * 末次服务结束时间，单位：时间戳
		 */
		private  long  lastServiceEndTime;
		/**
		 * 最后登录时间,单位：时间戳
		 */
		private  long  lastLoginTime;
		/**
		 * 最后签入时间，单位：时间戳
		 */
		private  long  lastWorkLoginTime;


		/**
		 * 登录次数
		 */
		private  long loginCount;
		/**
		 * 在线时长
		 */
		private  long onlineTime;
		/**
		 * 服务时长
		 */
		private  long serviceTime;
		/**
		 * 最大服务时间
		 */
		private  long maxServiceTime;
		/**
		 * 5秒有效服务次数
		 */
		private  long service5Count;
		/**
		 * 服务次数
		 */
		private  long serviceCount;
		/**
		 * 30秒服务次数
		 */
		private  long service30Count;

		/**
		 * 进入acw时间
		 */
		private  long lastActTime;
		/**
		 * 总acw时间
		 */
		private  long acwTime;
		/**
		 * 总act次数
		 */
		private  long acwCount;
		/**
		 * 示忙次数
		 */
		private  long notReadyCount;
		/**
		 * 示忙时长
		 */
		private  long notReadyTime;
		/**
		 * 最后一次置忙时间
		 */
		private  long lastNotReadyTime;


		/**
		 * 小休次数
		 */
		private long  notReadyCount1;
		/**
		 * 培训次数
		 */
		private long  notReadyCount2;
		/**
		 * 会议次数
		 */
		private long  notReadyCount3;
		/**
		 *小休时长
		 */

		private long notReadTime1;
		/**
		 * 最后一次小休时间
		 */
		private long lastNotReadyTime1;
		/**
		 * 培训次数
		 */
		private long notReadTime2;

		/**
		 * 会议次数
		 */
		private long notReadTime3;

		/**
		 * 坐席工号
		 */
		private  String agentId;


		private String entId;

		private int dateId;

		private int inboundCount = 0;

		private int outboundCount = 0;

		private String notReadyCause = "1";
		//呼入时长(秒)
		private long inboundTime = 0;
		//呼出时长(秒)
		private long outboundTime = 0;
		//呼入状态时长
		private long inboundStateTime = 0;
		//
		private long outboundStateTime = 0;
		//最后置闲时间
		private long lastReadyTime = 0;
		//首次置闲时间
		private long firstReadyTime = 0;

		/**
		 * 呼入状态置忙时长
		 */
		private long inboundNotReadyTime  = 0;
		/**
		 * 呼入状态置闲时长
		 */
		private long inboundReadyTime  = 0;
		/**
		 * 呼出状态置忙时时长
		 */
		private long outboundNotReadyTime  = 0;
		/**
		 * 呼出状态置闲时长
		 */
		private long outboundReadyTime  = 0;

		//最后的呼入状态时间
		private long lastInboundStateTime = 0 ;

		//最后的外呼状态时间
		private long lastOutboundStateTime = 0 ;

		private String workMode = "inbound";

		//最后呼叫时间
		private long lastServiceTime = 0;


		private String  command = "";


		private String updateTime = "";



		/**
		 * 获得企业所在的schema
		 * @return
		 */
		public String getDbSchemaId() {
			try {
				return QueryFactory.getReadQuery().queryForString("select SCHEMA_ID from CC_ENT_RES where ENT_ID=?", new Object[]{entId});
			} catch (SQLException e) {
			}
			return null;
		}

	@Deprecated
	public long getLastServiceEndTime() {
		return lastServiceEndTime;
	}

	@Deprecated
	public void setLastServiceEndTime(long lastServiceEndTime) {
		this.lastServiceEndTime = lastServiceEndTime;
	}

	@Deprecated
	public long getLastNotReadyTime1() {
		return lastNotReadyTime1;
	}

	@Deprecated
	public void setLastNotReadyTime1(long lastNotReadyTime1) {
		this.lastNotReadyTime1 = lastNotReadyTime1;
	}

	@Deprecated
	public long getFirstReadyTime() {
		return firstReadyTime;
	}

	@Deprecated
	public void setFirstReadyTime(long firstReadyTime) {
		this.firstReadyTime = firstReadyTime;
	}

	public String getTableName(String tableName){
		return this.getDbSchemaId() + "." + tableName;
	}
	@Deprecated
	public String getUpdateTime() {
		return updateTime;
	}
	public void setUpdateTime(String updateTime) {
		this.updateTime = updateTime;
	}
	@Deprecated
		public String getCommand() {
			return command;
		}
	@Deprecated
		public void setCommand(String command) {
			this.command = command;
		}

		public  IndexModel(){
			EasyCalendar  cal = EasyCalendar.newInstance();
			this.dateId = cal.getDateInt();
		}
	@Deprecated
		public void startAcw(){
			this.lastActTime = System.currentTimeMillis();
			this.acwCount++;
			this.saveToCache("startAcw");
		}

		public void startLogin(){
			this.lastLoginTime = System.currentTimeMillis();
			if(this.firstLoginTime == 0) 	this.firstLoginTime = this.lastLoginTime;
			this.loginCount++;
			this.setLastInboundStateTime(0);
			this.setLastOutboundStateTime(0);
			this.lastNotReadyTime = 0;
			this.lastReadyTime = System.currentTimeMillis();
			this.lastActTime = 0;
			this.workMode = "";  //登录成功后，后续会设置inbound 或 outbound状态。
			this.lastWorkLoginTime=System.currentTimeMillis();
			this.saveToCache("startLogin");
		}

	@Deprecated
		public void startWorkMode(String _workMode){
			if(StringUtils.isBlank(_workMode)) return;
			this.calStateTime(System.currentTimeMillis());
			if(_workMode.equalsIgnoreCase("outbound")){
				this.setLastOutboundStateTime(System.currentTimeMillis());
				this.setLastInboundStateTime(0);
			}else{
				this.setLastOutboundStateTime(0);
				this.setLastInboundStateTime(System.currentTimeMillis());
			}

			this.workMode = _workMode;
			this.saveToCache("startWorkMode");
		}

		/**
		 * 计算当前的状态时间
		 */
		private void calStateTime(long timeMillis) {

			if (this.lastLoginTime == 0){
				this.lastLoginTime = timeMillis;
			}
			this.onlineTime += timeMillis - this.lastLoginTime;
			this.lastLoginTime = timeMillis;

			// 在置忙状态
			if (this.lastNotReadyTime > 0) {
				this.notReadyTime += timeMillis - this.lastNotReadyTime;
				if ("1".equals(notReadyCause)) {
					this.notReadTime1 += timeMillis - this.lastNotReadyTime;
				}
				if ("2".equals(notReadyCause)) {
					this.notReadTime2 += timeMillis - this.lastNotReadyTime;
				}
				if ("3".equals(notReadyCause)) {
					this.notReadTime3 += timeMillis - this.lastNotReadyTime;
				}
			}

			// 通话结束，重置置忙、置闲时间。
			if (this.lastReadyTime > 0) {
				this.lastReadyTime = timeMillis;
			}

			if (this.lastNotReadyTime > 0) {
				this.lastNotReadyTime = timeMillis;
			}
		}

		/**
		 * 处理置闲
		 */
		public void startReady(){
			//首次置闲时间
			if(this.firstReadyTime <=0 ) this.firstReadyTime = System.currentTimeMillis();
			this.calStateTime(System.currentTimeMillis());
			this.lastReadyTime = System.currentTimeMillis();
			this.lastNotReadyTime = 0;
			this.saveToCache("startReady");
		}

		/**
		 * 置忙原因
		 * @param notReadyCause
		 */
		public void startNotReady(String notReadyCause){

			this.notReadyCount++;
			this.notReadyCause = notReadyCause;
			if("1".equals(notReadyCause)){
				this.notReadyCount1++;
				this.lastNotReadyTime1 = System.currentTimeMillis();
			}
			if("2".equals(notReadyCause)){
				this.notReadyCount2++;
			}
			if("3".equals(notReadyCause)){
				this.notReadyCount3++;
			}

			//非通话中，不计算置忙、置闲时间
			if(this.lastServiceTime ==0){
				this.calStateTime(System.currentTimeMillis());
			}
			this.lastReadyTime = 0;
			this.lastNotReadyTime = System.currentTimeMillis();
			this.saveToCache("startNotReady");
		}

		/**
		 * 开始通话
		 * @param createCause
		 */
		public void startService(String createCause){
			this.lastServiceTime = System.currentTimeMillis();
			//记录首次发起呼叫时间
			if(this.firstServiceTime <=0) 	this.firstServiceTime = this.lastServiceTime;
			//this.calStateTime(System.currentTimeMillis());
			this.saveToCache("startService");
		}

		/**
		 * 记录末次服务结束时间
		 */
		public void finishService() {
			this.lastServiceEndTime = System.currentTimeMillis();
			this.saveToCache("finishService");
		}


		/**
		 * 结束通话
		 * @param createCause
		 * @param callTime
		 */
		public void finishService(String createCause,int callTime){

			if(callTime <=0) callTime = 0;

//			//算出当前的内存的通话时间（秒）
//			long thisServiceTime = (System.currentTimeMillis() - lastServiceTime) / 1000;
//
//			//如果话单的通话时长 比内存的通话时长还大300秒，则取内存的时间，放在超长话单的出现。
//			if(callTime> thisServiceTime + 300){
//				callTime = (int)thisServiceTime;
//			}

			this.serviceTime = this.serviceTime + callTime;
			//fix by tzc ,20180904 ,只有通话时长大于0的才算接通。
			if(callTime>0){
				this.serviceCount++;
				if(callTime>5) this.service5Count++;
				if(callTime>30) this.service30Count++;
				//6呼出 8预拨号呼出
				if("6".equals(createCause) || "8".equals(createCause)){
					this.outboundCount++;
				}else{
					this.inboundCount++;
				}
			}

			if(this.maxServiceTime < callTime) this.maxServiceTime = callTime;

			if("6".equals(createCause) || "8".equals(createCause)){
				this.outboundTime = this.outboundTime + callTime;
			}else{
				this.inboundTime = this.inboundTime + callTime;
			}

			//通话结束，重置置忙、置闲时间。
			if (this.lastReadyTime > 0) {
				this.lastReadyTime = System.currentTimeMillis();
			}

			if (this.lastNotReadyTime > 0) {
				this.lastNotReadyTime = System.currentTimeMillis();
			}

			this.lastServiceTime = 0;
			this.lastServiceEndTime = System.currentTimeMillis();

			if(lastLoginTime>0){
				this.onlineTime += System.currentTimeMillis() - this.lastLoginTime;
				this.lastLoginTime = System.currentTimeMillis();
			}

			this.saveToCache("finishService");
		}


		/**
		 * 处理跨天的问题,完成当天的指标。
		 */
		public void finishDayLogin(){
			//如果最后登录时间为0则为已经登出状态，不做任何的处理。
			long zeroTime = getZeroTime();
			this.calStateTime(zeroTime);
			this.saveToCache("finishDayLogin");

		}

		/**
		 * 完成acw 话后处理。
		 */
		public  void  finishAcw(){
			if(this.lastActTime == 0 ) return;
			this.acwTime += System.currentTimeMillis()-this.lastActTime;
			this.lastActTime = 0;
			this.saveToCache("finishAcw");
		}

		/**
		 * 处理签出
		 */
		public void finishLogin(){

			if(this.lastLoginTime > 0){
				this.calStateTime(System.currentTimeMillis());
			}

			this.setLastInboundStateTime(0);
			this.setLastOutboundStateTime(0);
			this.lastActTime = 0;
			this.lastNotReadyTime =0;
			this.lastReadyTime = 0;
			this.lastLoginTime = 0;
			this.workMode = "";
			this.lastServiceTime = 0 ;
			this.serviceTime = statMediaChatTime();//设置服务时间
			this.saveToCache("finishLogin");
		}

		/**
		 * 统计当天所有会话时间
		 * @return
		 */
		public long statMediaChatTime() {
			EasySQL sql = new EasySQL("select ENT_ID,AGENT_ID,DATE_ID, sum(SERVER_TIME) SERVER_TIME ");
			sql.append(" from "+this.getTableName("CC_MEDIA_RECORD"));
			sql.append(" where 1=1 ");
			sql.append(this.entId," and ENT_ID = ?");
			sql.append(this.agentId," and AGENT_ID = ?");
			sql.append(this.dateId," and DATE_ID = ?");
			sql.append(" group by ENT_ID,AGENT_ID,DATE_ID");
			try {
				JSONObject record = QueryFactory.getReadQuery().queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
				if(record!=null) {
					return record.getLongValue("SERVER_TIME");
				}
			} catch (SQLException e) {
			}
			return 0;
		}


		public String getWorkMode() {
			return workMode;
		}

		public void setWorkMode(String workMode) {
			this.workMode = workMode;
		}

		public long getLastInboundStateTime() {
			return lastInboundStateTime;
		}

		public void setLastInboundStateTime(long lastInboundStateTime) {
			this.lastInboundStateTime = lastInboundStateTime;
		}

		public long getLastOutboundStateTime() {
			return lastOutboundStateTime;
		}

		public void setLastOutboundStateTime(long lastOutboundStateTime) {
			this.lastOutboundStateTime = lastOutboundStateTime;
		}



		public int getDateId() {
			return dateId;
		}

		public void setDateId(int dateId) {
			this.dateId = dateId;
		}

		public int getInboundCount() {
			return inboundCount;
		}

		public void setInboundCount(int inboundCount) {
			this.inboundCount = inboundCount;
		}

		public int getOutboundCount() {
			return outboundCount;
		}

		public void setOutboundCount(int outboundCount) {
			this.outboundCount = outboundCount;
		}


		public long getLastReadyTime() {
			return lastReadyTime;
		}

		public void setLastReadyTime(long lastReadyTime) {
			this.lastReadyTime = lastReadyTime;
		}



		public long getLastWorkLoginTime() {
			return lastWorkLoginTime;
		}


		public void setLastWorkLoginTime(long lastWorkLoginTime) {
			this.lastWorkLoginTime = lastWorkLoginTime;
		}


		public long getInboundTime() {
			return inboundTime;
		}

		public void setInboundTime(long inboundTime) {
			this.inboundTime = inboundTime;
		}

		public long getOutboundTime() {
			return outboundTime;
		}

		public void setOutboundTime(long outboundTime) {
			this.outboundTime = outboundTime;
		}

		public long getInboundStateTime() {
			return inboundStateTime;
		}

		public void setInboundStateTime(long inboundStateTime) {
			this.inboundStateTime = inboundStateTime;
		}

		public long getOutboundStateTime() {
			return outboundStateTime;
		}

		public void setOutboundStateTime(long outboundStateTime) {
			this.outboundStateTime = outboundStateTime;
		}

		public long getInboundNotReadyTime() {
			return inboundNotReadyTime;
		}

		public void setInboundNotReadyTime(long inboundNotReadyTime) {
			this.inboundNotReadyTime = inboundNotReadyTime;
		}

		public long getInboundReadyTime() {
			return inboundReadyTime;
		}

		public void setInboundReadyTime(long inboundReadyTime) {
			this.inboundReadyTime = inboundReadyTime;
		}

		public long getOutboundNotReadyTime() {
			return outboundNotReadyTime;
		}

		public void setOutboundNotReadyTime(long outboundNotReadyTime) {
			this.outboundNotReadyTime = outboundNotReadyTime;
		}

		public long getOutboundReadyTime() {
			return outboundReadyTime;
		}

		public void setOutboundReadyTime(long outboundReadyTime) {
			this.outboundReadyTime = outboundReadyTime;
		}

		public String getNotReadyCause() {
			return notReadyCause;
		}

		public void setNotReadyCause(String notReadyCause) {
			this.notReadyCause = notReadyCause;
		}

		public long getNotReadyCount1() {
			return notReadyCount1;
		}

		public void setNotReadyCount1(long notReadyCount1) {
			this.notReadyCount1 = notReadyCount1;
		}

		public long getNotReadyCount2() {
			return notReadyCount2;
		}

		public void setNotReadyCount2(long notReadyCount2) {
			this.notReadyCount2 = notReadyCount2;
		}

		public long getNotReadyCount3() {
			return notReadyCount3;
		}

		public void setNotReadyCount3(long notReadyCount3) {
			this.notReadyCount3 = notReadyCount3;
		}

		public long getNotReadTime1() {
			return notReadTime1;
		}

		public void setNotReadTime1(long notReadTime1) {
			this.notReadTime1 = notReadTime1;
		}

		public long getNotReadTime2() {
			return notReadTime2;
		}

		public void setNotReadTime2(long notReadTime2) {
			this.notReadTime2 = notReadTime2;
		}

		public long getNotReadTime3() {
			return notReadTime3;
		}

		public void setNotReadTime3(long notReadTime3) {
			this.notReadTime3 = notReadTime3;
		}

		public long getLastServiceTime() {
			return lastServiceTime;
		}

		public void setLastServiceTime(long lastServiceTime) {
			this.lastServiceTime = lastServiceTime;
		}


		public long getFirstLoginTime() {
			return firstLoginTime;
		}


		public void setFirstLoginTime(long firstLoginTime) {
			this.firstLoginTime = firstLoginTime;
		}


		public long getFirstServiceTime() {
			return firstServiceTime;
		}


		public void setFirstServiceTime(long firstServiceTime) {
			this.firstServiceTime = firstServiceTime;
		}


		public long getLastLoginTime() {
			return lastLoginTime;
		}


		public void setLastLoginTime(long lastLoginTime) {
			this.lastLoginTime = lastLoginTime;
		}



		public String getAgentId() {
			return agentId;
		}

		public void setAgentId(String agentId) {
			this.agentId = agentId;
		}

		public long getLoginCount() {
			return loginCount;
		}


		public void setLoginCount(long loginCount) {
			this.loginCount = loginCount;
		}


		public long getOnlineTime() {
			return onlineTime;
		}


		public void setOnlineTime(long onlineTime) {
			this.onlineTime = onlineTime;
		}


		public long getServiceTime() {
			return serviceTime;
		}


		public void setServiceTime(long serviceTime) {
			this.serviceTime = serviceTime;
		}


		public long getMaxServiceTime() {
			return maxServiceTime;
		}


		public void setMaxServiceTime(long maxServiceTime) {
			this.maxServiceTime = maxServiceTime;
		}


		public long getService5Count() {
			return service5Count;
		}


		public void setService5Count(long service5Count) {
			this.service5Count = service5Count;
		}


		public long getServiceCount() {
			return serviceCount;
		}


		public void setServiceCount(long serviceCount) {
			this.serviceCount = serviceCount;
		}


		public long getService30Count() {
			return service30Count;
		}


		public void setService30Count(long service30Count) {
			this.service30Count = service30Count;
		}


		public long getLastActTime() {
			return lastActTime;
		}


		public void setLastActTime(long lastActTime) {
			this.lastActTime = lastActTime;
		}


		public long getAcwTime() {
			return acwTime;
		}


		public void setAcwTime(long acwTime) {
			this.acwTime = acwTime;
		}


		public long getAcwCount() {
			return acwCount;
		}


		public void setAcwCount(long acwCount) {
			this.acwCount = acwCount;
		}


		public long getNotReadyCount() {
			return notReadyCount;
		}


		public void setNotReadyCount(long notReadyCount) {
			this.notReadyCount = notReadyCount;
		}


		public long getNotReadyTime() {
			return notReadyTime;
		}


		public void setNotReadyTime(long notReadyTime) {
			this.notReadyTime = notReadyTime;
		}


		public long getLastNotReadyTime() {
			return lastNotReadyTime;
		}


		public void setLastNotReadyTime(long lastNotReadyTime) {
			this.lastNotReadyTime = lastNotReadyTime;
		}

		public String getEntId() {
			return entId;
		}

		public void setEntId(String entId) {
			this.entId = entId;
		}



		private void saveRecord(){
			EasyRecord record = new EasyRecord(Constants.getStatSchema() + ".CC_RPT_AGENT_INDEX", "ENT_ID", "DATE_ID","AGENT_ID");
			EasyCalendar cal = EasyCalendar.newInstance();
			record.setPrimaryValues(entId, dateId , agentId);
			record.set("FIRST_LOGIN_TIME", timePart(this.firstLoginTime)); // 首次登录时间
			record.set("LOGIN_COUNT", this.loginCount); // 登录次数
			record.set("LAST_LOGIN_TIME", this.lastLoginTime); // 最后登录

			record.set("FIRST_SERVER_TIME", timePart(this.firstServiceTime)); // 首次服务开始时间
			record.set("LAST_SERVER_END_TIME", timePart(this.lastServiceEndTime)); // 最后一次服务结束时间
			record.set("FIRST_FREE_TIME", timePart(this.firstReadyTime));  //首次置闲时间
			record.set("LAST_REST_TIME",timePart(this.lastNotReadyTime1));   //最后一次小休时间

			record.set("ONLINE_TIME", this.onlineTime / 1000); // 在线时长
			record.set("SERVICE_TIME", this.serviceTime); // 服务时长秒
			record.set("SERVICE_COUNT", this.serviceCount); // 服务次数
			record.set("NOTREADY_TIME", this.notReadyTime / 1000); // 置忙时长
			record.set("NOTREADY_COUNT", this.notReadyCount);  // 置忙次数
			record.set("MAX_CALL_TIME",this.maxServiceTime); // 最长通话时长
			record.set("CALL_COUNT", this.serviceCount); //通话次数
			record.set("VALID_CALL_5COUNT", this.service5Count); // 5秒有效通话次数
			record.set("VALID_CALL_30COUNT", this.service30Count); // 30秒有效通话次数
			record.set("WORK_NOTREADY_TIME", this.acwTime / 1000); // 话后处理次数
			record.set("WORK_NOTREADY_COUNT", this.acwCount); // 话后处理次数
			record.set("OUTBOUND_COUNT", this.outboundCount); // 切换呼出状态次数
			record.set("INBOUND_COUNT", this.inboundCount); // 切换呼入状态次数
			record.set("NOTREADY3_TIME", this.notReadTime3/1000); // 话后处理次数
			record.set("NOTREADY3_COUNT", this.notReadyCount3); // 话后处理次数
			record.set("NOTREADY2_TIME", this.notReadTime2/1000); // 话后处理次数
			record.set("NOTREADY2_COUNT", this.notReadyCount2); // 话后处理次数
			record.set("NOTREADY1_TIME", this.notReadTime1/1000); // 小休时长
			record.set("NOTREADY1_COUNT", this.notReadyCount1); // 小休次数
			record.set("OUTBOUND_NOTREADY_TIME", this.outboundNotReadyTime/1000); // 呼出置忙时长
			record.set("OUTBOUND_READY_TIME", this.outboundReadyTime/1000); // 呼出置闲时长
			record.set("INBOUND_NOTREADY_TIME", this.inboundNotReadyTime/1000); // 接话置忙时长
			record.set("INBOUND_READY_TIME", this.inboundReadyTime/1000); // 接话空闲时长
			record.set("INBOUND_TIME", this.inboundTime); // 接话时长
			record.set("OUTBOUND_TIME", this.outboundTime); // 呼出时长
			record.set("OUTBOUND_STATE_TIME", this.outboundStateTime/1000); // 呼出状态时长
			record.set("INBOUND_STATE_TIME", this.inboundStateTime/1000); // 呼入状态时长
			record.set("UPDATE_TIME", cal.getDateTime("-")); // 话后处理时间
			try {
				boolean bl = QueryFactory.getStatQuery().update(record);
				if (!bl)
					QueryFactory.getStatQuery().save(record);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] IndexModel.save() error,cause:"+ex.getMessage(),ex);
			}
		}

		public static String timePart(long time){
			if(time <=0) return "-";
			EasyCalendar  easyCalendar = EasyCalendar.newInstance(new Date(time));
			return easyCalendar.getTimeString();
		}

		/**
		 * 获得当前凌晨的时间
		 * @return
		 */
		public static long getZeroTime(){
			EasyCalendar cal = EasyCalendar.newInstance();
			cal = EasyCalendar.newInstance(cal.getDateString("-"),"yyyy-MM-dd");
			return cal.getTimeInMillis();
		}

	public static IndexModel getIndexModel(String entId,String agentId){

		IndexModel indexModel = null;

		int curDateId = EasyCalendar.newInstance().getDateInt();

		try {

			indexModel = agentIndexs.get(agentId);
			if(indexModel!=null && indexModel.getDateId()==curDateId){
//				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 直接从内存中获取到当天index对象");
				return indexModel;
			}

			//同步锁
			synchronized (agentId.intern()){
				EasyCalendar cal = EasyCalendar.newInstance();
				curDateId = cal.getDateInt();
				indexModel = agentIndexs.get(agentId);
				if(indexModel!=null){
					if(indexModel.getDateId()==curDateId){
						return indexModel;
					}
					//todo 处理跨天
					IndexModel todayIndex = getTodayIndex(indexModel);
					agentIndexs.put(agentId,todayIndex);
//					MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 内存：处理完跨天，把新对象放进agentIndexs,直接返回");
					return todayIndex;
				}else{
					//先读取缓存（模块重启后内存消失）
					String curDayKey = "AGENT_IDX_"+curDateId+"_"+agentId;
					String curDayVal = cache.get(curDayKey);
					if(StringUtils.isNotBlank(curDayVal)){
						indexModel = JSONObject.toJavaObject(JSONObject.parseObject(curDayVal), IndexModel.class);
						if(indexModel.getDateId()==curDateId){
							agentIndexs.put(agentId,indexModel);
//							MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 从缓存中获取到当天index对象");
							return indexModel;
						}
						//todo 处理跨天
						IndexModel todayIndex = getTodayIndex(indexModel);
						agentIndexs.put(agentId,todayIndex);
//						MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 缓存：处理完跨天，把新对象放进agentIndexs,直接返回");
						return todayIndex;
					}
				}
				//创建新的对象
				indexModel = new IndexModel();
				indexModel.setAgentId(agentId);
				indexModel.setEntId(entId);
				indexModel.setDateId(curDateId);
				String  key = "AGENT_IDX_"+curDateId+"_"+agentId;
				String val = JSONObject.toJSONString(indexModel);
				cache.put(key,val, 24*3600);
				agentIndexs.put(agentId,indexModel);
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 内存和缓存都没取到，直接放一个新对象");
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		return indexModel;
	}

	/**
	 * 处理跨天问题
	 * @param indexModel 昨天的对象
	 */
	private static IndexModel getTodayIndex(IndexModel indexModel){
		String agentId = indexModel.getAgentId();
		EasyCalendar cal = EasyCalendar.newInstance();
		int curDateId = cal.getDateInt();
		int oldDateId = indexModel.getDateId();
		cal.add(EasyCalendar.DAY, -1);
		int frontDataId = cal.getDateInt();

		//重置后的新对象
		IndexModel newIndexModel = new IndexModel();
		newIndexModel.setAgentId(agentId);
		newIndexModel.setEntId(indexModel.getEntId());
		newIndexModel.setDateId(curDateId);

		//indexModel对象日期属于昨天
		if(oldDateId == frontDataId){
			indexModel.finishDayLogin();//更新昨天的数据
			String  key = "AGENT_IDX_"+frontDataId+"_"+agentId;
			cache.delete(key);
			//今天的对象要从0点开始计算
			long zeroTime = EasyCalendar.newInstance(EasyCalendar.newInstance().getDateString("-"),"yyyy-MM-dd").getTimeInMillis();
			indexModel.calStateTime(zeroTime);
			newIndexModel.setWorkMode(indexModel.getWorkMode());
			newIndexModel.setNotReadyCause(indexModel.getNotReadyCause());
			newIndexModel.setLastLoginTime(zeroTime);
			newIndexModel.setLastInboundStateTime(zeroTime);
			newIndexModel.setLastOutboundStateTime(zeroTime);
			newIndexModel.setLastNotReadyTime(zeroTime);
			newIndexModel.setLastReadyTime(zeroTime);
			newIndexModel.setLastServiceTime(zeroTime);
		}else{
			MediaCenterLogger.getDebugLogger().warn("IndexModel.getIndexModel() indexModel对象日期非昨天，dateId："+oldDateId);
			String oldDateIdStr = String.valueOf(oldDateId);
			String oldDateStr = oldDateIdStr.substring(0,4) + "-" + oldDateIdStr.substring(4,6) + "-" + oldDateIdStr.substring(6);
			long zeroTime = EasyCalendar.newInstance(oldDateStr,"yyyy-MM-dd").getTimeInMillis();
			indexModel.calStateTime(zeroTime);
			indexModel.saveToCache("finishDayLogin");
		}

		String  key = "AGENT_IDX_"+curDateId+"_"+agentId;
		String val = JSONObject.toJSONString(newIndexModel);
		cache.put(key,val, 24*3600);
		agentIndexs.put(agentId,newIndexModel);

		return newIndexModel;
	}

	public synchronized static IndexModel getIndexModel2(String entId,String agentId){

		IndexModel indexModel = null;
		try {
			EasyCalendar cal =  EasyCalendar.newInstance();
			//获取当天的index对象
			String  key = "AGENT_IDX_"+cal.getDateInt()+"_"+agentId;
			String  val = cache.get(key);
			//如果memcache存在当日的IndexModel，直接返回
			if(StringUtils.isNotBlank(val)){
				indexModel = JSONObject.toJavaObject(JSONObject.parseObject(val), IndexModel.class);
				return indexModel;
			}else{
				indexModel = new IndexModel();
				indexModel.setAgentId(agentId);
				indexModel.setEntId(entId);
			}
			//获取昨天的index对象
			cal.add(EasyCalendar.DAY, -1);
			String frontKey = "AGENT_IDX_"+cal.getDateInt()+"_"+agentId;
			String frontVal = cache.get(frontKey);
			//如果昨天的数据存在，则获取昨天的数据。
			if(StringUtils.isNotBlank(frontVal)){
				cache.delete(frontKey);  //删除昨天的数据。
				JSONObject indexObject = JSONObject.parseObject(frontVal);
				IndexModel _indexModel = JSONObject.toJavaObject(indexObject, IndexModel.class);

				if(System.currentTimeMillis() - _indexModel.getLastLoginTime() < 3600*1000){
					_indexModel.finishDayLogin();
					indexModel.setWorkMode(_indexModel.getWorkMode());
					indexModel.setLastLoginTime(_indexModel.getLastLoginTime());
					indexModel.setLastInboundStateTime(_indexModel.getLastInboundStateTime());
					indexModel.setLastOutboundStateTime(_indexModel.getLastOutboundStateTime());
					indexModel.setLastNotReadyTime(_indexModel.getLastNotReadyTime());
					indexModel.setLastReadyTime(_indexModel.getLastReadyTime());
					indexModel.setNotReadyCause(_indexModel.getNotReadyCause());
					indexModel.setLastServiceTime(_indexModel.getLastServiceTime());
					indexModel.setDateId(_indexModel.getDateId());
				}
			}else{
				EasyCalendar today =  EasyCalendar.newInstance();
				if(agentIndexs.containsKey(agentId)){
					IndexModel _indexModel = agentIndexs.get(agentId);
					if(_indexModel.getDateId() == today.getDateInt()){
						indexModel = _indexModel;
					}
				}
			}

		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error(ex,ex);
		}

		return indexModel;
	}

//	public static IndexModel getIndexModel2(String entId,String agentId){
//
//		IndexModel indexModel = null;
//		try {
//			EasyCalendar cal =  EasyCalendar.newInstance();
//			String  key = "AGENT_IDX_"+cal.getDateInt()+"_"+agentId;
//			String  val = cache.get(key);
//			//如果memcache中不存在当日的IndexModel
//			if(StringUtils.isNotBlank(val)){
//				indexModel = JSONObject.toJavaObject(JSONObject.parseObject(val), IndexModel.class);
//				return indexModel;
//			}else{
//				indexModel = new IndexModel();
//				indexModel.setAgentId(agentId);
//				indexModel.setEntId(entId);
//			}
//
//			cal.add(EasyCalendar.DAY, -1);
//			String frontKey = "AGENT_IDX_"+cal.getDateInt()+"_"+agentId;
//			String frontVal = cache.get(frontKey);
//			//如果昨天的数据存在，则获取昨天的数据。
//			if(StringUtils.isNotBlank(frontVal)){
//				cache.delete(frontKey);  //删除昨天的数据。
//				JSONObject indexObject = JSONObject.parseObject(frontVal);
//				IndexModel _indexModel = JSONObject.toJavaObject(indexObject, IndexModel.class);
//
//				if(System.currentTimeMillis() - _indexModel.getLastLoginTime() < 3600*1000){
//					_indexModel.finishDayLogin();
//					indexModel.setWorkMode(_indexModel.getWorkMode());
//					indexModel.setLastLoginTime(_indexModel.getLastLoginTime());
//					indexModel.setLastInboundStateTime(_indexModel.getLastInboundStateTime());
//					indexModel.setLastOutboundStateTime(_indexModel.getLastOutboundStateTime());
//					indexModel.setLastNotReadyTime(_indexModel.getLastNotReadyTime());
//					indexModel.setLastReadyTime(_indexModel.getLastReadyTime());
//					indexModel.setNotReadyCause(_indexModel.getNotReadyCause());
//					indexModel.setLastServiceTime(_indexModel.getLastServiceTime());
//					MediaCenterLogger.getDebugLogger().warn("IndexModel.getIndexModel() 昨天的对象--->"+indexObject);
//				}
//			}else{
//				EasyCalendar today =  EasyCalendar.newInstance();
//				if(agentIndexs.containsKey(agentId)){
//					IndexModel _indexModel = agentIndexs.get(agentId);
//					if(_indexModel.getDateId() == today.getDateInt()){
//						indexModel = _indexModel;
//					}
//				}
//			}
//
//		} catch (Exception ex) {
//		}
//
//		return indexModel;
//	}

		public synchronized void saveToCache(String command){

			EasyCalendar cal = EasyCalendar.newInstance();
			this.updateTime = cal.getDateTime("-");

			this.command = command;
			try {
				this.saveRecord();
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

			try {
				String  key = "AGENT_IDX_"+dateId+"_"+agentId;
				String val = JSONObject.toJSONString(this);
				MediaCenterLogger.getLogger().info("<"+command+"> >> "+val);
//				agentIndexs.put(this.getAgentId(), this);
				cache.put(key,val, 24*3600);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

		}

		public void saveReadTime(long readTime){
			EasyCalendar cal = EasyCalendar.newInstance();
			this.updateTime = cal.getDateTime("-");
			this.inboundReadyTime += readTime;
			try {
				this.saveRecord();
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

			try {
				String  key = "AGENT_IDX_"+dateId+"_"+agentId;
				String val = JSONObject.toJSONString(this);
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <saveReadTime> >> "+val);
//				agentIndexs.put(this.getAgentId(), this);
				cache.put(key,val, 24*3600);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

		}

		public void saveNotReadyTime(long notReadyTime){

			EasyCalendar cal = EasyCalendar.newInstance();
			this.updateTime = cal.getDateTime("-");
			this.inboundNotReadyTime += notReadyTime;
			try {
				this.saveRecord();
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

			try {
				String  key = "AGENT_IDX_"+dateId+"_"+agentId;
				String val = JSONObject.toJSONString(this);
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <saveReadTime> >> "+val);
//				agentIndexs.put(this.getAgentId(), this);
				cache.put(key,val, 24*3600);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}
		}

}
