<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="mediagw" name="全媒体网关" package-by="ljp46" package-time="2025-08-04 16:14:59" version="1.01#20250814-1">
 <datasources>
        <datasource description="云呼平台数据源(写1)" isnull="true" name="yc-write-ds-1"/> 
        <datasource description="云呼平台数据源(写2)" isnull="true" name="yc-write-ds-2"/> 
        <datasource description="云呼平台数据源(读)" isnull="true" name="yc-read-ds"/>
         <datasource description="业务数据源" isnull="true" name="yw-ds"/>
    </datasources>
    <description>
        1.01#20250814-1
            1.优化超长消息文本存储逻辑，避免频繁写库。
            2.解决进入排队后，发送引用消息未写入到数据库的问题。
            3.解决H5在线客服人工满意度评价不显示问题。
            1.增加超过8小时的缓存数据持久化逻辑，避免设置太长时间，频繁更新导致缓存失效。
            1.增加数字人客服相关接口。
            2.所有接口增加XSS过滤。
        1.01#20250710-1
            1.优化日志输出，减少非必要日志。
            2.整理常量定义，调整访客队列名称缓存时间。
            3.优化特殊访客角色列表查询报错。
        1.01#20250703-1
            1.优化附件下载地址替换外网地址问题。
            2.优化访客进线日志。
        1.01#20250626-1
            1.增加消息引用功能和专家咨询满意度卡片。
            2.美粉会员等级标签规则优化。
    </description>
    <versionHistory>
        1.01#20250612-1
        1.处理BUG2025060607740【生产】在线客服主动评价结果没有记录。
        1.01#20250515-1
        1.优化H5客户端消息，历史会话的未评价机器人消息不需要显示点赞/点踩。
        1.01#20250508-1
        1.H5客户端消息发送接口返回消息serialId。
        1.01#20250410-1
        1.优化同步锁清理逻辑。
        2.增加开发者调试入口。
        3.调整CSS工单事件接口相关日志输出。
        4.调整工单-提交补充信息的回复语。
        5.增加满意度开关控制会话结束时是否推送满意度逻辑。
        6.增加留言开关控制非工作时间和排队超时是否进入留言流程。
        1.01#********-1
            1.美云销对接需求，新增：获取满意度配置接口，提交满意度接口，机器人会话记录同步接口；适配传入会话id。
            2.新增访客在机器人会话中，访客发送商品卡片信息时（msgType=news）读取商品链接（userUrl）转发给机器人。
            3.移除org.apache.commons.lang.StringUtils。
            4.优化调用AIGC大模型总结接口报错问题。
            5.随路数据的accountType默认值改为openid。
            6.新增美粉会员适配需求。
            7.优化提交图文满意度时错误判断超时的问题。
        1.01#********-1
            1.优化移除同步锁String.intern()。
        1.01#********-1
            1.优化接口/yc-mediagw/mediaApi?action=channelConfig，屏蔽敏感配置，增加styleConf.openAigcOcr用于前端控制“图片”“拍照”按钮的解锁。
            2.新增渠道配置“AIGC图片识别”，开启后，接收到图片消息，先请求AIGC图片识别接口，将识别结果转发给云问，图片则不发送。（已添加配置，未对接AIGC）
            3.新增文本机器人用户体验埋点体系建立需求。
            4.渠道按键列表查询接口，新增返回按键图标，导航提示语，不返回隐藏的按键。
            5.自助服务专区查询接口，新增校验非商家角色访客不展示“商家入口”。
        1.01#********-1
            1.优化部分聊天记录超长写入数据库失败的问题，必须配合yc-ccbar（1.0#********-1）升级，ycbusi库执行脚本ycbusi-********.sql。
            2.优化CSS工单操作(提交催办，提交修改时间)到CSS接口，入参operator取值问题。
            3.优化机器人会话中访客主动下线或超时下线都要通知机器人平台下线。
        1.01#20241205-1
            1.优化会话初始化时进入选择按键流程（selectKey），重复触发机器人访客初始化上线逻辑。
            2.优化聊天记录加载接口。
            3.增加文本机器人自助服务及受理需求-CSS工单逻辑：初始化上线自动推送工单，FAQ触发工单，工单操作事件，上传补充信息的图片到CSS接口。
            4.调整通知栏接口，当渠道配置-机器人配置未开启““CSS工单自动推送””才去查询未完结工单。
        1.01#20241008-1
            1.优化H5客户端提交满意度逻辑。
        1.01#20240918-1
            1.新增机器人满意度评价胶囊逻辑。
        1.01#20240619-1
            1.优化，接入机器人时欢迎语发送和入库的serialId不一致的问题。
        1.01#20240613-1
            1.用户转人工后进行人机摘要大模型总结
        1.01#20240223-1
            1.优化H5客户端获取消息接口（longpolling）增加5秒内重发机制。
        1.01#20240126-1
            1.新增APP日志文件上传接口。
        1.01#20240125-1
            解决【在线客服】【生产】系统公告设置了发布时间，未按发布时间展示
        1.01#20240123-1
            1.优化美居APPwebsocket消息接口，收到onClose()时，对比当前session与缓存的session是否一致，不一致就不处理，确保缓存中的session一直存在。
    </versionHistory>
</application>
