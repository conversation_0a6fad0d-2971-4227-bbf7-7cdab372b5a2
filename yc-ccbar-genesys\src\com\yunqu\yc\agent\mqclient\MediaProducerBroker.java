package com.yunqu.yc.agent.mqclient;

import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.listener.GlobalContextListener;
import com.yunqu.yc.agent.log.EventLogger;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;

import java.util.HashMap;
import java.util.Map;


/**
 * <AUTHOR>
 * 每个CCBAR 根据所在的mars独立创建属于该mars的MQ，Agent是通过nignx进行登录，agent会在其中的一台mars上进行登录。 因此每台mars的ccbar只需要处理当前坐席的请求信息。
 */
public class MediaProducerBroker {

    private static EasyCache cache = CacheManager.getMemcache();

    private static Broker mediaCenterBroker;

    private static Map<String, Broker> userBrokers = new HashMap<String, Broker>();

    private static Map<String, Broker> agentBrokers = new HashMap<String, Broker>();


    public static void sendMediaCenterMessage(String msg) {
        try {
            if (mediaCenterBroker == null) {
                if(!GlobalContextListener.runState){
                    return;
                }
                String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
                mediaCenterBroker = BrokerFactory.getProducerQueueBroker(addr, Constants.ICCS_BROKER, "", "");
            }

            EventLogger.getLogger().info("<Producer>[MediaCenter][" + Constants.ICCS_BROKER + "] >> " + msg);
            mediaCenterBroker.sendMessage(msg);
        } catch (Exception ex) {
            MediaLogger.getLogger().error(" >> sendMediaCenterMessage(" + Constants.ICCS_BROKER + ") error,cause:" + ex.getMessage(), ex);
        }
    }


    /**
     * 发送消息给坐席消息队列
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2021/10/15 10:37
     * @Param agentId:
     * @Param msg:
     * @return: void
     */
    public static void sendAgentMessage(String agentId, String msg) {
        if (StringUtils.isBlank(agentId)) {
            MediaLogger.getLogger().error(" >> sendAgentMessage error,cause: agentId is null", null);
            return;
        }
        //获得当前坐席所在的mars上的MQ
        String brokerName = MediaCacheUtil.get(Constants.BROKER_AGENT_NAME + agentId);

        if (StringUtils.isBlank(brokerName)) {
            MediaLogger.getLogger().error(" >> sendAgentMessage error,cause: agentId[" + agentId + "] broker is null", null);
            return;
        }

        Broker broker = agentBrokers.get(brokerName);
        if (broker == null) {
            if(!GlobalContextListener.runState){
                return;
            }
            String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
            broker = BrokerFactory.getProducerQueueBroker(addr, brokerName, "", "");
            agentBrokers.put(brokerName, broker);
        }
        EventLogger.getLogger().info("<Producer>[" + agentId + "][" + brokerName + "] >> " + msg);
        try {
            broker.sendMessage(msg);
        } catch (Exception ex) {
            MediaLogger.getLogger().error(" >> sendAgentMessage(" + agentId + "," + brokerName + ") error,cause:" + ex.getMessage(), ex);
        }
    }


    /**
     * 获得用户所对应的MARS。
     *
     * @return
     */
    public static void sendUserMessage(String sessionId, String msg) {

        if (StringUtils.isBlank(sessionId)) {
            MediaLogger.getLogger().error(" >> sendUserMessage error,cause: sessionId is null", null);
            return;
        }
        //获得当前坐席所在的mars上的MQ
        String brokerName = cache.get(Constants.BROKER_USER_NAME + sessionId);

        if (StringUtils.isBlank(brokerName)) {
            MediaLogger.getLogger().error(" >> sessionId error,cause: sessionId[" + sessionId + "] broker is null", null);
            return;
        }

        Broker broker = userBrokers.get(brokerName);
        if (broker == null) {
            if(!GlobalContextListener.runState){
                return;
            }
            String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
            broker = BrokerFactory.getProducerQueueBroker(addr, brokerName, "", "");
            userBrokers.put(brokerName, broker);
        }
        EventLogger.getLogger().info("<Producer>[" + sessionId + "][" + brokerName + "] >> " + msg);
        try {
            broker.sendMessage(msg);
        } catch (Exception ex) {
            MediaLogger.getLogger().error(" >> sendUserMessage(" + sessionId + "," + brokerName + ") error,cause:" + ex.getMessage(), ex);
        }

    }

    public static void closeBroker() {
        if (mediaCenterBroker != null) {
            try {
                mediaCenterBroker.close();
            } catch (Exception e) {
                MediaLogger.getLogger().error(e.getMessage(), e);
            }
        }

        for (Broker broker : userBrokers.values()) {
            if (broker != null) {
                try {
                    broker.close();
                } catch (Exception e) {
                    MediaLogger.getLogger().error(e.getMessage(), e);
                }
            }
        }
    }

}
