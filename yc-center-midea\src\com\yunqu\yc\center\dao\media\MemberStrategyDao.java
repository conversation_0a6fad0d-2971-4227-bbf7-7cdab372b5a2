package com.yunqu.yc.center.dao.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;
import com.yunqu.yc.center.util.MemberConfigUtil;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 会员身份策略管理DAO
 * <AUTHOR>
 */
@WebObject(name="memberStrategy")
public class MemberStrategyDao extends AppDaoContext {

    /**
     * 会员身份策略列表
     */
    @WebControl(name="list", type=Types.LIST)
    public JSONObject list(){
        EasySQL sql = this.getEasySQL("select * from CC_MEDIA_MEMBER_CONFIG cfg where 1=1");
        sql.append(param.getString("memberId"), " and cfg.MEMBER_ID = ?");
        sql.append(param.getString("isToAgent"), " and cfg.IS_TO_AGENT = ?");
        sql.append(" order by cfg.RANK_INDEX asc, cfg.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 获取单条记录
     */
    @WebControl(name="record", type=Types.RECORD)
    public JSONObject record(){
        String configId = param.getString("configId");
        if(StringUtils.isBlank(configId)) {
            return null;
        }
        return queryForRecord(new EasyRecord("CC_MEDIA_MEMBER_CONFIG", "CONFIG_ID").setPrimaryValues(configId));
    }



    /**
     * 关联渠道策略列表（某个会员身份下的所有渠道）
     */
    @WebControl(name="channelConfigList", type=Types.LIST)
    public JSONObject channelConfigList(){
        String configId = param.getString("configId");
        String chKeyword = param.getString("chKeyword");

        if(StringUtils.isBlank(configId)) {
            return getJsonResult(false);
        }
        EasySQL sql = this.getEasySQL("select t1.*,t2.MEMBER_ID,t2.MEMBER_NAME");
        sql.append(" from CC_MEDIA_MEMBER_CHANNEL t1");
        sql.append(" left join CC_MEDIA_MEMBER_CONFIG t2 on t1.CONFIG_ID=t2.CONFIG_ID");
        sql.append(" where 1=1");
        sql.append(configId, " and t1.CONFIG_ID = ?");

        if(StringUtils.isNotBlank(chKeyword)) {
            sql.append(chKeyword, " and ( t1.CHANNEL_KEY = ?");
            sql.append(chKeyword, " or t1.CHANNEL_NAME = ? )");
        }
        sql.append(" order by t1.RANK_INDEX desc, t1.UPDATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 预设会员身份字典
     */
    @WebControl(name="memberDict", type=Types.DICT)
    public JSONObject memberDict(){
        return this.getJsonResult(MemberConfigUtil.getMemberDictMap());
    }

    /**
     * 身份等级字典（1-20）
     */
    @WebControl(name="rankIndexDict", type=Types.DICT)
    public JSONObject rankIndexDict(){
        return this.getDictByArray("1","1","2","2","3","3","4","4","5","5",
                "6","6","7","7","8","8","9","9","10","10",
                "11","11","12","12","13","13","14","14","15","15",
                "16","16","17","17","18","18","19","19","20","20");
    }

    /**
     * 渠道字典
     */
    @WebControl(name="channelDict", type=Types.DICT)
    public JSONObject channelDict(){
        String entId = param.getString("entId");
        return getDictByQuery("select CHANNEL_KEY,CHANNEL_NAME from CC_CHANNEL where CHANNEL_STATE=0 and ENT_ID=? order by CHANNEL_NAME", new Object[]{entId});
    }

    /**
     * 技能组字典
     */
    @WebControl(name="agentGroupDict", type=Types.DICT)
    public JSONObject agentGroupDict(){
        String entId = param.getString("entId");
        return getDictByQuery("select SKILL_GROUP_ID,SKILL_GROUP_NAME from "+getTableName(entId,"CC_SKILL_GROUP t1")+" where ENT_ID=? order by GROUP_NAME", new Object[]{entId});
    }

    /**
     * 获取可选择的渠道列表（排除当前会员策略已关联的）
     */
    @WebControl(name="availableChannels", type=Types.LIST)
    public JSONObject availableChannels(){
        String entId = param.getString("entId");
        String configId = param.getString("configId");

        EasySQL sql = this.getEasySQL("select t1.CHANNEL_KEY, t1.CHANNEL_NAME from CC_CHANNEL t1 where t1.CHANNEL_STATE=0");
        sql.append(entId, " and  ENT_ID=?");
        if(StringUtils.isNotBlank(configId)) {
            sql.append(configId," and t1.CHANNEL_KEY not in (select CHANNEL_KEY from CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ?)");
        }
        return queryForList(sql.getSQL(), sql.getParams());
    }
}
