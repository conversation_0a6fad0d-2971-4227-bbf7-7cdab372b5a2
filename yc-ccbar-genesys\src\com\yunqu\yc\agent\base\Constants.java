package com.yunqu.yc.agent.base;

import java.net.InetAddress;

import java.net.NetworkInterface;
import java.util.Enumeration;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.kit.RandomKit;

import com.yunqu.yc.agent.log.MediaLogger;

/**
 * 甯搁噺
 * <AUTHOR>
 *
 */
/**
 * 甯搁噺
 * <AUTHOR>
 *
 */
public class Constants {

	public final static String GW_NAME = "<agentgw> ";

	public final static String VERSION_NAME="v1.0.7";
	
	public final static String VERSION_DATE="20180208";
	
	public final static String DS_WIRTE_NAME_ONE = "yc-wirte-ds-1";     
	
	public final static String DS_WIRTE_NAME_TWO = "yc-wirte-ds-2";

	public final static String DS_READ_NAME = "yc-read-ds";


	public final static String APP_NAME = "yc-ccbar";    
	

	public final static String BROKER_AGENT_NAME = "BROKER_AGENT_";
	
	public final static String BROKER_USER_NAME = "BROKER_USER_";
	public final static String USER_VISITOR = "BROKER_USER__VISITOR_";
	public final static String CACHE_VISITOR_NAME = "VisitorModel_";

	public final static String MEDIACENTER_BROKER = "mediacenter-broker";
	
	public final static String ICCS_BROKER = "iccs-broker";
	
	
	public final static String MONGODB_DBNAME = "YCMAIN";
	
	private final static String STAT_DB = "stat";
	
	private final static AppContext apiContext = AppContext.getContext("yc-api");
	
	private final static AppContext ccbarContext = AppContext.getContext("yc-ccbar");


	public static AppContext getCcbarContext(){
		return AppContext.getContext(APP_NAME);
	}

	private final static String   nodeName = getLocalAddr();
	
	public static String getStatSchema(){
		return apiContext.getProperty("STAT_DB",STAT_DB);
	}
	
	public static String getYwdbSchema(){
		return "YWDB";
	}
	
	
	public static String getGenesysIp1(){
		return ccbarContext.getProperty("GENESYS_IP1","");
	}
	
	public static String getGenesysIp2(){
		return ccbarContext.getProperty("GENESYS_IP2","");
	}
	
	public static int getGenesysAgentPort1(){
		return Integer.parseInt(ccbarContext.getProperty("GENESYS_PORT1","3000"));
	}
	public static int getGenesysAgentPort2(){
		return Integer.parseInt(ccbarContext.getProperty("GENESYS_PORT2","3000"));
	}
	
	public static int getGenesysConfPort(){
		return Integer.parseInt(ccbarContext.getProperty("GENESYS_PORT","2020"));
	}
	
	public static int getGenesysStatPort(){
		return Integer.parseInt(ccbarContext.getProperty("GENESYS_PORT","5660"));
	}
	
	public static String getSystemType(){
		return ccbarContext.getProperty("ICCS_TYPE","petra");
	}
	
	public static String getAgentBrokerName(){
		return  BROKER_AGENT_NAME+ getLocalAddr();//ccbarContext.getProperty("AGENT_BROKER_NAME", System.currentTimeMillis()+"");
	}
	
	/**
	 * 是否采集坐席指标
	 * @return
	 */
	public static boolean  isCollAgentIndex(){
		String  collIndex =  ccbarContext.getProperty("COLL_INDEX","false");
		return !"false".equalsIgnoreCase(collIndex);
	}
	
	public static String   getArea(){
		return ccbarContext.getProperty("AREA","SD");
	}
	
	public static String getOutLink(){
		return ccbarContext.getProperty("OUT_LINK","");
	}

	
	 private static String getAddress(String addr){
		  if(StringUtils.isBlank(addr)) return "";
		  //System.out.println(addr);
		  int len = StringUtils.split(addr, ".").length;
		  if(len<4) return "";
		  //System.out.println("addr.startsWith(127)="+addr.startsWith("127"));
		  if(addr.startsWith("127")) return "";
		  return addr;
	  }
	  
	  private static String getLocalAddr() {
		  try {
			  Enumeration<NetworkInterface> enums  = NetworkInterface.getNetworkInterfaces();
				String ipaddr =  null;
				while (enums.hasMoreElements()) {
					NetworkInterface net = (NetworkInterface) enums.nextElement();
					//System.out.println("net->"+JSONObject.toJSONString(net));
					Enumeration<InetAddress> enum2 = net.getInetAddresses();
					while (enum2.hasMoreElements()) {
						InetAddress address = (InetAddress) enum2.nextElement();
						ipaddr = getAddress(address.getHostAddress());
						if(StringUtils.isBlank(ipaddr)) continue;
						if(ipaddr.startsWith(apiContext.getProperty("BROKER_ADDR", ""))){
//							MediaLogger.getLogger().info("broker addr ->"+ipaddr);
							return ipaddr;
						}
					}

				}
			} catch (Exception ex) {
				MediaLogger.getLogger().error(ex,ex);
			}
		  
			return RandomKit.uniqueStr();
	  }

	/**
	 * 助手Ws地址
	 * 格式如：ws://*************:8192/aiaengine/innerApi.ws?infoType=************
	 * @return
	 */
	public static String  getVoiceWsAddr(){
		return getCcbarContext().getProperty("VOICE_WS_ADDR","");
	}
	/**
	 * 助手账号
	 * @return
	 */
	public static String  getVoiceHelperAccount(){
		return getCcbarContext().getProperty("VOICE_HELPER_ACCOUNT","");
	}
	/**
	 * 助手密码
	 * @return
	 */
	public static String  getVoiceHelperPwd(){
		return getCcbarContext().getProperty("VOICE_HELPER_PWD","");
	}
}

