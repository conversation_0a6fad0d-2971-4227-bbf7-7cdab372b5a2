CREATE TABLE CC_MEDIA_CACHE_DATA(
        CACHE_KEY	VARCHAR2(100) NOT NULL,
        CREATE_TIME	VARCHAR2(19) NOT NULL,
        UPDATE_TIME	VARCHAR2(19) NOT NULL,
        CACHE_VALUE	VARCHAR2(4000) NOT NULL,
        LOST_TIME	VARCHAR2(19) NOT NULL,
        PRIMARY KEY (CACHE_KEY)
);
CREATE INDEX IDX_CC_MEDIA_CACHE_DATA_1 ON CC_MEDIA_CACHE_DATA(CREATE_TIME);
CREATE INDEX IDX_CC_MEDIA_CACHE_DATA_2 ON CC_MEDIA_CACHE_DATA(UPDATE_TIME);
CREATE INDEX IDX_CC_MEDIA_CACHE_DATA_3 ON CC_MEDIA_CACHE_DATA(LOST_TIME);

COMMENT ON TABLE CC_MEDIA_CACHE_DATA IS '缓存数据表，用于保存缓存时间超过8小时的数据';
COMMENT ON COLUMN CC_MEDIA_CACHE_DATA.CACHE_KEY IS '缓存KEY';
COMMENT ON COLUMN CC_MEDIA_CACHE_DATA.CREATE_TIME IS '创建时间';
COMMENT ON COLUMN CC_MEDIA_CACHE_DATA.UPDATE_TIME IS '更新时间';
COMMENT ON COLUMN CC_MEDIA_CACHE_DATA.CACHE_VALUE IS '缓存内容';
COMMENT ON COLUMN CC_MEDIA_CACHE_DATA.LOST_TIME IS '失效时间';

grant insert,select,update,delete on CC_MEDIA_CACHE_DATA to ycbusi;
grant insert,select,update,delete on CC_MEDIA_CACHE_DATA to ycuser;
grant insert,select,update,delete on CC_MEDIA_CACHE_DATA to ywdb;

alter table ycbusi.CC_MEDIA_CACHE_DATA modify CACHE_KEY VARCHAR2(100);