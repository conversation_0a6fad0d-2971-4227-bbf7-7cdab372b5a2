<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="yc-mediacenter" name="全媒体路由控制中心" package-by="ljp46" package-time="2025-07-24 15:31:32" version="2.0#20250814-1">
    <datasources>
         <datasource description="主数据库(1)" isnull="true" name="yc-write-ds-1"/> 
        <datasource description="主数据库(2)" isnull="true" name="yc-write-ds-2"/> 
        <datasource description="读数据源" isnull="true" name="yc-read-ds"/> 
    </datasources>
    <description>
        2.0#20250814-1
            1.增加超过8小时的缓存数据持久化逻辑，避免设置太长时间，频繁更新导致缓存失效。
        2.0#20250402-1
            1.移除无用日志。
        2.0#20241128-1
            1.新增OnlineModel对象，用于代替IndexModel中的计算各个时长的统计。
        2.0#20241017-1
            1.优化IndexModel对象，getIndexModel()增加同步锁。
        2.0#20240418-1
            1.优化坐席工作统计-空闲时长计算逻辑，添加跟踪日志。
        2.0#20240308-1
            1.优化坐席工作统计-空闲时长计算逻辑。
        2.0#20240130-1
            1.优化用户端异常时发送取消邀请（errorInviteVideo），如果是已接通视频，则设置为用户端挂断 11，并更新视频会话记录 END_TIME，TOTAL_TIME
        2.0#20240102-1
            1.增加转移到技能组逻辑。
        2.0#20231221-1
            1.视频满意度评价链接增加参数。
        1.0#20230105-1 熟客优先逻辑增加 更新 接入记录 CC_MEDIA_ACCESS_RECORD.CLEAR_CAUSE增加：15.熟客优先转人工结束

    </description>
    <versionHistory>
        2.0#20221130-1
            1.新增熟客优先接入逻辑。
        2.0#20221125-1
            1.优化EntContext.DaemonThread的死循环线程。
            2.优化EntContext.getContext()。
        2.0#20221122-1
            1.坐席监控-增加“超过60秒未响应的会话数”“超过180秒未响应的会话数”
        2.0#20221027-1
            1.优化会话结束时，发送close到客户端时，不许需要将结束语写入数据库，yc-medigw 模块也会将该条信息写入到数据库中。
        2.0#20211201-1
            1.新增，保存发送的系统回复语，不在yc-mediagw中保存了。
            2.新增，系统回复语替换msgContent中的用户会员等级名称。
            3.优化，每次发送消息时都要设置data.sender。
            4.优化，美居APP端增加视频等待页标识（videoMsgId）,用于防止用户重复取消视频邀请，导致重复取消。
        2.0#20210901-1 每次发送消息到yc-mediagw，yc-ccbar时，都要重置消息流水id messageModel.setSerialId(RandomKit.randomStr())
        2.0#20210623-1 视频邀请时设置会话记录的视频通话标志
        2.0#20210602-1
            1.调整会话结束逻辑，保证会话结束后发送Closed通知坐席页面关闭会话。
            2.解决转移会话后，原坐席手动关闭已转移的会话导致转移后的坐席页面被转移会话被关闭。
    </versionHistory>
</application>
