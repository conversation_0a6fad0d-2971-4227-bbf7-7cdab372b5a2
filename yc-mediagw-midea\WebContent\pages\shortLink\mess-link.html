<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta name="renderer" content="webkit" />
    <meta
      content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no"
      name="viewport"
    />
    <meta content="IE=EmulateIE8" http-equiv="X-UA-Compatible" />
    <meta content="yes" name="apple-mobile-web-app-capable" />
    <meta content="black" name="apple-mobile-web-app-status-bar-style" />
    <meta name="robots" content="index,follow" />
    <meta http-equiv="pragma" content="no-cache" />
    <meta http-equiv="cache-control" content="no-cache" />
    <meta http-equiv="expires" content="0" />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/libs/element-ui/theme-chalk/index.css"
    />
    <link
      rel="stylesheet"
      href="/easitline-cdn/vue-yq/theme/core.css?v=1.0.0"
    />

    <style>
      html,
      body {
        height: 100%;
        margin: 0;
        padding: 0;
      }

      #messLink {
        font-family: Alibaba PuHuiTi 3;
        width: 100%;
        height: 100vh;
        background: url("/yc-mediagw/pages/shortLink/img/1.png") no-repeat center;
        background-size: cover;
        /* background: linear-gradient(180deg, #0092D8 0%, #C5EFFF 60%, #F2F4F7 100%); */
        display: flex;
        flex-direction: column;
        position: relative;
        overflow: hidden;
      }

      /* 头部区域 */
      .header {
        position: relative;
      }

      .header .md-logo {
        margin-top: 10px;
        width: 80px;
        height: 42px;
        margin-left: 32px;
      }
      .app-title {
        margin-left: 32px;
        font-size: 32px;
        font-weight: bold;
        line-height: 48px;
        background: linear-gradient(180deg, #ffffff 0%, #b2e6ff 100%);
        -webkit-background-clip: text;
        -webkit-text-fill-color: transparent;
        background-clip: text;
        text-fill-color: transparent;
        text-shadow: 0px 4px 8px rgba(0, 146, 216, 0.4);
        margin-bottom: 20px;
      }

      /* 主要内容区域 */
      .content-container {
        color: #262626;
        flex: 1;
        padding: 0 20px 12px;
        display: flex;
        flex-direction: column;
        border-radius: 16px;
        font-size: 28px;
        line-height: 44px;
      }

      .message-card {
        background: white;
        border-radius: 16px;
        padding: 24px;
        box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
        flex: 1;
        position: relative;
        background: rgba(255, 255, 255, 0.9);
        box-sizing: border-box;
        z-index: 2;
      }
      .message-card .card-bg {
        position: absolute;
        left:0;
        top: 0;
        width: 100%;
        height: 48px;
        border-radius: 16px 16px 0px;
        background: linear-gradient(
          180deg,
          #d4edff 0%,
          rgba(247, 252, 255, 0) 100%
        );
        border-image: linear-gradient(
            180deg,
            #ffffff 50%,
            rgba(255, 255, 255, 0) 100%
          )
          2;
      }
      .message-title {
        font-size: 16px;
        font-weight: bold;
        margin-bottom: 12px;
      }

      .message-content {
        line-height: 1.5;
        font-size: 14px;
      }

      .message-content p {
        margin: 0 0 16px 0;
      }

      .phone-number {
        color: #0092d8;
        font-weight: bold;
        cursor: pointer;
        text-decoration: underline;
      }

      .location {
        color: #666;
      }

      .service-info {
        margin-top: 10px;
      }

      .bottom-img {
        position: absolute;
        bottom: 170px;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
      }
      .logo-img {
        width: calc(100% - 24px);
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 2;
      }
      .bottom-img1 {
        width: calc(100% - 0px);
        position: absolute;
        bottom: 0;
        left: 50%;
        transform: translateX(-50%);
        z-index: 1;
      }
      [v-cloak] {
        display: none;
      }
    </style>
  </head>

  <body>
    <div id="messLink" v-cloak>
      <!-- 头部 -->
      <div class="header">
        <img class="md-logo" src="/yc-mediagw/pages/shortLink/img/5.png" alt="" />
        <!-- 图片 -->
        <div class="app-title">美的信息</div>
      </div>

      <!-- 主要内容 -->
      <div class="content-container">
        <div class="message-card">
          <div class="card-bg"></div>
          <div class="message-title">{{title}}</div>
          <div class="message-content">
            <template v-for="(item, index) in contentItems">
              <p v-if="item.text || item.phone">
                <span v-if="item.text">{{item.text}}</span>
                <span
                  v-if="item.phone"
                  class="phone-number"
                  @click="makeCall(item.phone)"
                  >{{item.phone}}</span
                >
              </p>
            </template>
          </div>
        </div>
      </div>

      <!-- 背景装饰 -->
      <img class="bottom-img1" src="/yc-mediagw/pages/shortLink/img/2.png" alt="" />
      <img class="logo-img" src="/yc-mediagw/pages/shortLink/img/3.png" alt="" />
      <img class="bottom-img" src="/yc-mediagw/pages/shortLink/img/4.png" alt="" />
    </div>
    <script src="/easitline-cdn/vue-yq/libs/vue.min.js"></script>
    <script src="/easitline-cdn/vue-yq/libs/element-ui/index.js"></script>
    <script src="/easitline-static/js/jquery.min.js" charset="utf-8"></script>
    <script src="/easitline-cdn/vue-yq/libs/core.js?v=1.0.0"></script>

    <script>
      var app = new Vue({
        el: "#messLink",
        data() {
          return {
            title: "",
            contentItems: [],
          };
        },
        methods: {
          base64UrlDecode(str) {
            // 替换 URL 安全字符为标准 Base64 字符
            str = str.replace(/-/g, "+").replace(/_/g, "/");
            // 补全填充字符
            while (str.length % 4 !== 0) {
              str += "=";
            }
            return decodeURIComponent(
                    atob(str)
                            .split("")
                            .map(function (c) {
                              return "%" + ("00" + c.charCodeAt(0).toString(16)).slice(-2);
                            })
                            .join("")
            );
          },
          // 解码短链数据
          decodeShortLinkData() {
            const urlParams = new URLSearchParams(window.location.search);
            const encryptedData = urlParams.get("data");

            if (!encryptedData) {
              return null;
            }

            try {
              // Base64解码
              const decryptedData = this.base64UrlDecode(encryptedData);
              console.log("解码后的数据:", decryptedData);

              // 解析查询字符串格式的数据
              const decryptedParams = new URLSearchParams(decryptedData);
              let params = {};

              for (const [key, value] of decryptedParams.entries()) {
                params[key] = decodeURIComponent(value);
              }

              return {
                title: params.title || "",
                content: this.parseContentItems(params)
              };
            } catch (error) {
              console.error("Base64解密失败:", error);
              return null;
            }
          },

          // 解析内容项
          parseContentItems(params) {
            let content = [];
            let maxIndex = 0;

            // 查找最大索引
            for (const key in params) {
              if (key.startsWith("text") || key.startsWith("phone")) {
                const index = parseInt(key.replace("text", "").replace("phone", ""));
                if (!isNaN(index) && index > maxIndex) {
                  maxIndex = index;
                }
              }
            }

            // 构建内容项
            for (let i = 1; i <= maxIndex; i++) {
              const textKey = "text" + i;
              const phoneKey = "phone" + i;

              if (params[textKey]) {
                content.push({ type: "text", value: params[textKey] });
              }
              if (params[phoneKey]) {
                content.push({ type: "phone", value: params[phoneKey] });
              }
            }

            return content;
          },

          // 获取URL参数并设置数据
          getUrlParams() {
            const result = this.decodeShortLinkData();

            if (result) {
              this.title = result.title;
              this.contentItems = this.convertContentToItems(result.content);
            } else {
              // fallback到直接解析URL参数
              this.parseDirectUrlParams();
            }
          },

          // 转换content数组为contentItems格式
          convertContentToItems(content) {
            let items = [];
            let currentItem = { text: "", phone: "" };

            for (const item of content) {
              if (item.type === "text") {
                if (currentItem.text || currentItem.phone) {
                  items.push(currentItem);
                  currentItem = { text: "", phone: "" };
                }
                currentItem.text = item.value;
              } else if (item.type === "phone") {
                currentItem.phone = item.value;
              }
            }

            if (currentItem.text || currentItem.phone) {
              items.push(currentItem);
            }

            return items;
          },

          // 直接解析URL参数的fallback方法
          parseDirectUrlParams() {
            const urlParams = new URLSearchParams(window.location.search);
            let params = {};

            for (const [key, value] of urlParams.entries()) {
              params[key] = value;
            }

            this.title = params.title || "";
            this.contentItems = [];

            let maxIndex = 0;
            for (const key in params) {
              if (key.startsWith("text") || key.startsWith("phone")) {
                const index = parseInt(key.replace("text", "").replace("phone", ""));
                if (!isNaN(index) && index > maxIndex) {
                  maxIndex = index;
                }
              }
            }

            for (let i = 1; i <= maxIndex; i++) {
              const textKey = "text" + i;
              const phoneKey = "phone" + i;

              if (params[textKey] || params[phoneKey]) {
                this.contentItems.push({
                  text: params[textKey] || "",
                  phone: params[phoneKey] || "",
                });
              }
            }
          },
          // 拨打电话
          makeCall(phoneNumber) {
            if (phoneNumber) {
              window.location.href = "tel:" + phoneNumber;
            }
          },
        },
        created() {
          this.getUrlParams();
        },
      });
    </script>
  </body>
</html>
