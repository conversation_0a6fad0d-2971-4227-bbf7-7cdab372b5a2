package com.yunqu.yc.mediagw.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import org.apache.log4j.Logger;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * 缓存超过4000字节的文本内容
 */
public class CacheLongTxtUtil {
    public static final String CACHE_PREFIX = "cacheLongTxt";
    public static final int CACHE_TIME = 3 * 3600; // 缓存时间，单位：秒

    // 文本长度相关常量
    private static final int MIN_CACHE_SIZE = 4000; // 最小缓存阈值，单位：字节
    private static final int MAX_CONTENT_SIZE = 50000; // 最大内容长度，单位：字节
    private static final int SEGMENT_SIZE = 1333; // 每段文本长度，单位：字符
    private static final int SEGMENT_EXTEND_SIZE = 300; // 段落扩展长度，单位：字符
    private static final int SEGMENT_MIN_BYTES = 3000; // 段落最小字节数
    private static final int MAX_SEGMENTS = 15; // 最大分段数量

    // 缓存等待相关常量
    private static final int CACHE_CHECK_INTERVAL = 200; // 缓存检查间隔，单位：毫秒

    // 异常处理相关常量
    private static final int MAX_RETRY_TIMES = 3; // 最大重试次数
    private static final long RETRY_DELAY_MS = 100; // 重试延迟，单位：毫秒

    private static final Logger logger = MediagwLogger.getLongTxtLogger();

    /**
     * set
     * @param schemaId 数据库schema ID
     * @param record EasyRecord
     * @param id 唯一id
     * @param dbkKeys 需要缓存的数据库字段key 数组
     */
    public static void set(String schemaId, EasyRecord record, String id, String ...dbkKeys){
        // 增强参数验证
        if(!validateSetParameters(schemaId, record, id, dbkKeys)){
            return;
        }
        long thid = Thread.currentThread().getId();
        for (String dbKey : dbkKeys){
            String value1 = record.getString(dbKey);
            if(StringUtils.isBlank(value1)){
                continue;
            }
            int length = value1.getBytes(StandardCharsets.UTF_8).length;
            if(length < MIN_CACHE_SIZE){
                continue;
            }
            if(length > MAX_CONTENT_SIZE){
                logger.error("Thread["+thid+"] 超长文本：保存，内容字节长度（"+length+"）超过"+MAX_CONTENT_SIZE+"，直接丢弃，不处理！id："+id);
                record.set(dbKey ,"加载失败：内容过长！");
                continue;
            }

            try {
                //机器人答案，使用answerId作为唯一标识。
                if("ROBOT_DATA".equals(dbKey)){
                    JSONObject robotDataObj = JSONObject.parseObject(value1);
                    String answerId = robotDataObj.getString("answerId");
                    id = StringUtils.isNotBlank(answerId)?answerId:id;
                    logger.info("Thread["+thid+"] 超长文本：保存，保存机器人消息内容，answerId："+answerId);
                }
                //模板消息。tips：每天更新一次，dataId：渠道key_satisfy6_日期id
                if("TEMP_CONFIG".equals(dbKey)){
                    JSONObject tempDataObj = JSONObject.parseObject(value1);
                    String dataId = tempDataObj.getString("dataId");
                    id = StringUtils.isNotBlank(dataId)?dataId:id;
                }

                String cacheKey = CACHE_PREFIX+dbKey+":"+id;
                record.set(dbKey ,cacheKey);
//            logger.warn("Thread["+thid+"] 超长文本：保存，内容字节长度（"+length+"）超过4000，写入缓存和数据库，cacheKey：" + cacheKey);
                saveDb(schemaId, id, cacheKey, value1);
            } catch (Exception e) {
                logger.error("Thread["+thid+"] 超长文本：保存，执行失败，id："+id+"，异常信息：" + e.getMessage());
                logger.error(e.getMessage(),e);
            }

        }
    }

    /**
     * 获取超长文本消息内容
     * @param schemaId 数据库id
     * @param serialId 消息id
     * @param dbkKey 数据库字段key
     * @param cacheKey 缓存key，数据库中保存的缓存标识，例如：cacheLongTxtROBOT_DATA:82457101736939489227247
     * @return
     */
    public static String get(String schemaId, String serialId, String dbkKey, String cacheKey){
        // 增强参数验证
        if(!validateGetParameters(schemaId, serialId, dbkKey)){
            return "";
        }

        //如果未传缓存标识，则通过消息id组装
        if(StringUtils.isBlank(cacheKey)){
            cacheKey = CACHE_PREFIX+dbkKey+":"+serialId;
        }
        long thid = Thread.currentThread().getId();

        //读取缓存中的历史数据
        String value1 = CacheUtil.get(cacheKey);
        if(StringUtils.isNotBlank(value1)){
            return value1;
        }

        //从缓存中读取分段内容
        String cacheTxtLength = cacheKey+":length";//缓存key：消息内容长度
        String cacheKeyCount = cacheKey+":count";//缓存key：消息分段总长度
        String cacheIdexValue = cacheKey+":value";//缓存key：每段消息
        int txtCount = 0;
        Object o = CacheUtil.get(cacheKeyCount);
        if(o!=null){
            txtCount = Integer.parseInt(o.toString());
        }
        if(txtCount>0){
            StringBuilder cacheTxtContent = new StringBuilder();
            for (int i = 0; i < txtCount; i++) {
                String cacheKeyIndex = cacheIdexValue + ":" + i;
                Object indexObj = CacheUtil.get(cacheKeyIndex);
                if(indexObj==null){
                    logger.warn("Thread["+thid+"] 超长文本：查询，分段数据在缓存中不存在，cacheKeyIndex：" + cacheKeyIndex);
                    continue;
                }
                cacheTxtContent.append(indexObj);
            }
            String cacheValue = cacheTxtContent.toString();
            if(StringUtils.isNotBlank(cacheValue)){
                logger.info("Thread["+thid+"] 超长文本：查询，缓存中存在分段消息，拼接完成，cacheKey：" + cacheKey+"，内容长度："+cacheValue.length());
                return cacheValue;
            }
            logger.warn("Thread["+thid+"] 超长文本：查询，缓存不存在分段消息，拼接失败，cacheKey：" + cacheKey+"，需要查询数据库！");
        }

        //从数据库中读取分段内容
        try {
            List<JSONObject> list = getTxtListByDb(schemaId, cacheKey);
            if(list==null||list.isEmpty()){
                logger.warn("Thread["+thid+"] 超长文本：查询，数据库中不存在分段消息，返回空，cacheKey：" + cacheKey);
                return "";
            }

            StringBuilder dbTxtContent = new StringBuilder();
            int txtIndex = 0;//每份切割内容所属下标
            for (; txtIndex < list.size(); txtIndex++) {
                String txtContent1 = list.get(txtIndex).getString("TXT_CONTENT");
                dbTxtContent.append(txtContent1);
                String cacheKeyIndex = cacheIdexValue + ":" + txtIndex;
                CacheUtil.put(cacheKeyIndex ,txtContent1 ,CACHE_TIME);
//                logger.info("Thread["+thid+"] 超长文本：查询，查询数据库后写入缓存中，cacheKey：" + cacheKey+"，分段缓存key："+cacheKeyIndex);
            }
            value1 = dbTxtContent.toString();
            if(StringUtils.isNotBlank(value1)){
                CacheUtil.put(cacheKeyCount ,String.valueOf(txtIndex) ,CACHE_TIME);
                CacheUtil.put(cacheTxtLength ,String.valueOf(value1.length()) ,CACHE_TIME);
                logger.info("Thread["+thid+"] 超长文本：查询，数据库中存在，拼接完成并记录“消息分段总长度”："+txtIndex+"，cacheKey：" + cacheKey+"，内容长度："+value1.length());
            }
            return value1;
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return "";
    }

    /**
     * 从数据库中查询大文本内容
     * @param schemaId
     * @param cacheKey
     * @return
     */
    private static List<JSONObject> getTxtListByDb(String schemaId, String cacheKey){
        String sql = "select TXT_CONTENT from "+schemaId+".CC_MEDIA_CHAT_LONG_TXT where CACHE_ID=? order by TXT_INDEX";
        try {
            return QueryFactory.getQuery().queryForList(sql, new Object[]{cacheKey}, new JSONMapperImpl());
        } catch (Exception e) {
            logger.error(e.getMessage(),e);
        }
        return null;
    }


    private static void saveDb(String schemaId, String serialId, String cacheKey, String value){
        long startTime = System.currentTimeMillis();
        long thid = Thread.currentThread().getId();
        int currLength = value.length();
        String cacheTxtLength = cacheKey+":length";//缓存key：消息内容长度
        String cacheKeyCount = cacheKey+":count";//缓存key：消息分段总长度
        String cacheIdexValue = cacheKey+":value";//缓存key：每段消息

        logger.info("Thread["+thid+"] 超长文本：保存开始，内容长度："+currLength+"，cacheKey：" + cacheKey);

        //内容是否有变化
        long changeCheckStart = System.currentTimeMillis();
        if(!hasChange(schemaId,cacheKey,currLength,cacheTxtLength,cacheKeyCount,cacheIdexValue)){
            long changeCheckTime = System.currentTimeMillis() - changeCheckStart;
            logger.info("Thread["+thid+"] 超长文本：保存，内容无变化，跳过保存，检查耗时："+changeCheckTime+"ms，cacheKey：" + cacheKey);
            return;
        }
        long changeCheckTime = System.currentTimeMillis() - changeCheckStart;
        logger.info("Thread["+thid+"] 超长文本：保存，内容有变化，需要重新保存，检查耗时："+changeCheckTime+"ms，cacheKey：" + cacheKey);

        int txtIndex = 0;//每份切割内容所属下标
        int txtSize = SEGMENT_SIZE;//每次切割长度（按照1个汉字占3个字节，每次切割的内容不超过4000字节）
        try {
            int dateInt = EasyCalendar.newInstance().getDateInt();
            for ( ; txtIndex < MAX_SEGMENTS; txtIndex++) {
                String txtContent;
                if(txtSize < value.length()){
                    txtContent = value.substring(0,txtSize);
                    //每次截取尽可能接近4000字节
                    int newTxtSize = txtSize + SEGMENT_EXTEND_SIZE;
                    if(txtContent.getBytes().length < SEGMENT_MIN_BYTES && newTxtSize < value.length()){
                        txtContent = value.substring(0, newTxtSize);
                        value = value.substring(newTxtSize);
                    }else{
                        value = value.substring(txtSize);
                    }
                }else{
                    txtContent = value;
                    value = "";
                }
                if(StringUtils.isBlank(txtContent)){
                    break;
                }
                try {
                    EasyRecord record = new EasyRecord(schemaId+".CC_MEDIA_CHAT_LONG_TXT","ID");
                    record.set("DATE_ID", dateInt);
                    record.set("SERIAL_ID",serialId);
                    record.set("CACHE_ID",cacheKey);
                    record.setPrimaryValues(RandomKit.randomStr());
                    record.set("TXT_INDEX",txtIndex);
                    record.set("TXT_CONTENT",txtContent);
                    QueryFactory.getQuery().save(record);
                } catch (Exception e) {
                    logger.error("Thread["+thid+"] 超长文本：写库，写入数据库失败，cacheKey：" + cacheKey+"，error："+e.getMessage());
                }

                String cacheKeyIndex = cacheIdexValue + ":" + txtIndex;
                CacheUtil.put(cacheKeyIndex ,txtContent ,CACHE_TIME);
                logger.info("Thread["+thid+"] 超长文本：保存，写入数据库中，cacheKey：" + cacheKey+"，分段缓存key："+cacheKeyIndex);
            }
            CacheUtil.put(cacheKeyCount ,String.valueOf(txtIndex) ,CACHE_TIME);
            CacheUtil.put(cacheTxtLength ,String.valueOf(currLength) ,CACHE_TIME);
            logger.info("Thread["+thid+"] 超长文本：保存，写入数据库完成，记录“消息分段总长度”："+(txtIndex)+"，缓存key：" + cacheKeyCount+"，内容总长度："+currLength);
        } catch (Exception e) {
            logger.error("Thread["+thid+"] 超长文本：写库，写入数据库失败，cacheKey：" + cacheKey+"，error："+e.getMessage());
        }
    }

    /**
     * 内容是否有变化，只校验长度是否有变化
     * @param schemaId 业务库名称
     * @param cacheKey 缓存key
     * @param currLength 当前内容长度
     * @param cacheTxtLength 缓存key：消息内容长度
     * @param cacheKeyCount 缓存key：消息分段总长度
     * @param cacheIdexValue 缓存key：每段消息
     * @return
     */
    private static boolean hasChange(String schemaId,String cacheKey,int currLength,String cacheTxtLength,String cacheKeyCount,String cacheIdexValue){
        long thid = Thread.currentThread().getId();
        Object o = CacheUtil.get(cacheTxtLength);
        //先检查缓存中是否存在消息
        if(o!=null){
            try {
                int oldLength = Integer.parseInt((String) o);
                //无变化
                if(oldLength == currLength){
                    logger.info("Thread["+thid+"] 超长文本：保存，写入数据库之前，缓存中已存在，内容无变化，不再写入，cacheKey：" + cacheKey);
                    return false;
                }
                //有变化，清理数据库
                cleanupExistingData(schemaId, cacheKey, thid);
            } catch (Exception e) {
                logger.error("Thread["+thid+"] 超长文本：保存，写入数据库之前删除失败，cacheKey：" + cacheKey+"，error："+e.getMessage());
            }
            //缓存不存在，检查数据库中是否存在，如果存在则判断是否有变化
        }else{
            List<JSONObject> list = getTxtListByDb(schemaId, cacheKey);
            if(list!=null && !list.isEmpty()){
                StringBuilder dbTxtContent = new StringBuilder();
                for (int txtIndex = 0; txtIndex < list.size(); txtIndex++) {
                    String txtContent1 = list.get(txtIndex).getString("TXT_CONTENT");
                    dbTxtContent.append(txtContent1);
                }
                String value1 = dbTxtContent.toString();
                int oldLength = value1.length();

                //无变化，写入缓存
                if(oldLength == currLength){
                    // 重新写入分段缓存
                    for (int txtIndex = 0; txtIndex < list.size(); txtIndex++) {
                        String txtContent1 = list.get(txtIndex).getString("TXT_CONTENT");
                        String cacheKeyIndex = cacheIdexValue + ":" + txtIndex;
                        CacheUtil.put(cacheKeyIndex, txtContent1, CACHE_TIME);
                    }
                    CacheUtil.put(cacheKeyCount, String.valueOf(list.size()), CACHE_TIME);
                    CacheUtil.put(cacheTxtLength, String.valueOf(currLength), CACHE_TIME);
                    logger.info("Thread["+thid+"] 超长文本：保存，写入数据库之前，缓存中不存在，数据库中存在，内容无变化，不再写入数据库，重新写入缓存，cacheKey：" + cacheKey);
                    return false;
                }
                //有变化，清理数据库
                cleanupExistingData(schemaId, cacheKey, thid);
            }
        }
        return true;
    }

    /**
     * 清理数据库中已存在的数据
     * @param schemaId 数据库schema
     * @param cacheKey 缓存key
     * @param thid 线程ID
     */
    private static void cleanupExistingData(String schemaId, String cacheKey, long thid) {
        logger.warn("Thread["+thid+"] 超长文本：保存，内容有变化，需要清理数据库已存在的内容后重新写入，cacheKey：" + cacheKey);

        String sql = "delete from " + schemaId + ".CC_MEDIA_CHAT_LONG_TXT where CACHE_ID=?";
        int deleteResult = executeWithRetry(() -> QueryFactory.getQuery().executeUpdate(sql, cacheKey), "删除数据库内容", cacheKey, thid);

        if(deleteResult == 0){
            logger.warn("Thread["+thid+"] 超长文本：保存，删除数据库内容不成功，cacheKey：" + cacheKey);
        }
    }

    /**
     * 带重试机制的数据库操作执行器
     * @param operation 数据库操作
     * @param operationName 操作名称
     * @param cacheKey 缓存key
     * @param thid 线程ID
     * @return 操作结果
     */
    private static int executeWithRetry(DatabaseOperation operation, String operationName, String cacheKey, long thid) {
        Exception lastException = null;

        for (int attempt = 1; attempt <= MAX_RETRY_TIMES; attempt++) {
            try {
                return operation.execute();
            } catch (Exception e) {
                lastException = e;
                logger.warn("Thread["+thid+"] 超长文本："+operationName+"，第"+attempt+"次尝试失败，cacheKey：" + cacheKey + "，error：" + e.getMessage());

                if (attempt < MAX_RETRY_TIMES) {
                    try {
                        Thread.sleep(RETRY_DELAY_MS * attempt); // 递增延迟
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        break;
                    }
                }
            }
        }

        logger.error("Thread["+thid+"] 超长文本："+operationName+"，重试"+MAX_RETRY_TIMES+"次后仍然失败，cacheKey：" + cacheKey, lastException);
        return 0;
    }

    /**
     * 数据库操作接口
     */
    private interface DatabaseOperation {
        int execute() throws Exception;
    }


    /**
     * 验证set方法的参数
     * @param schemaId 数据库schema ID
     * @param record EasyRecord对象
     * @param id 唯一ID
     * @param dbkKeys 数据库字段key数组
     * @return 验证是否通过
     */
    private static boolean validateSetParameters(String schemaId, EasyRecord record, String id, String[] dbkKeys) {
        long thid = Thread.currentThread().getId();

        if (StringUtils.isBlank(schemaId)) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，schemaId不能为空");
            return false;
        }

        if (record == null) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，record不能为null");
            return false;
        }

        if (StringUtils.isBlank(id)) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，id不能为空");
            return false;
        }

        if (dbkKeys == null || dbkKeys.length == 0) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，dbkKeys不能为空");
            return false;
        }

        return true;
    }

    /**
     * 验证get方法的参数
     * @param schemaId 数据库schema ID
     * @param serialId 消息ID
     * @param dbkKey 数据库字段key
     * @return 验证是否通过
     */
    private static boolean validateGetParameters(String schemaId, String serialId, String dbkKey) {
        long thid = Thread.currentThread().getId();

        if (StringUtils.isBlank(schemaId)) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，schemaId不能为空");
            return false;
        }

        if (StringUtils.isBlank(serialId)) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，serialId不能为空");
            return false;
        }

        if (StringUtils.isBlank(dbkKey)) {
            logger.error("Thread["+thid+"] 超长文本：参数验证失败，dbkKey不能为空");
            return false;
        }

        return true;
    }
}

