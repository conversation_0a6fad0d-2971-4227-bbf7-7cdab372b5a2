package com.yunqu.yc.agent.base;

import org.apache.commons.lang3.StringUtils;


import org.easitline.common.db.EasyQuery;

import com.yunqu.yc.agent.log.CcbarLogger;


/**
 * 
 * <AUTHOR>
 */
public class QueryFactory {
	private static EasyQuery writeQuery1 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
	private static EasyQuery writeQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);

	public static EasyQuery getQuery(){
		return writeQuery1;
	}
	public static EasyQuery getReadQuery(){
		return readQuery;
	}
	public static EasyQuery getQuery(String entId){
		EasyQuery  easyQuery ;
		if(StringUtils.isBlank(entId)){
			easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
		}else{
			int _entId = Integer.parseInt(entId);
			if(_entId%2==0)   easyQuery =   EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_ONE);
			else easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_WIRTE_NAME_TWO);
		}
		easyQuery.setLogger(CcbarLogger.getLogger());
		return easyQuery;
	}
	
	public static EasyQuery getStatQuery(){
		 EasyQuery statQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.DS_READ_NAME);
		 statQuery.setLogger(CcbarLogger.getLogger());
		 return statQuery;
	}
}
