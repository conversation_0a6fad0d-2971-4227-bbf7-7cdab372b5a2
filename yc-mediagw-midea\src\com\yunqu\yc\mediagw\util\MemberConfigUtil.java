package com.yunqu.yc.mediagw.util;

import com.alibaba.fastjson.JSONObject;

import java.util.LinkedHashMap;
import java.util.Map;

/**
 * 会员身份策略工具类
 */
public class MemberConfigUtil {

    //一级，会员身份策略，格式：Map<memberId, JSONObject>
    private final static Map<String, JSONObject> memberConfigMap = new LinkedHashMap<>();

    //二级，渠道会员身份策略，格式：Map<channelKey+memberId, JSONObject>
    private final static Map<String, JSONObject> memberChannelMap = new LinkedHashMap<>();

    //TODO 查询YCUSER.CC_MEDIA_MEMBER_CONFIG表加载会员身份策略，按照RANK_INDEX字段降序

    //TODO 查询YCUSER.CC_MEDIA_MEMBER_CHANNEL表加载渠道会员身份策略，按照CHANNEL_KEY，RANK_INDEX字段降序

}

