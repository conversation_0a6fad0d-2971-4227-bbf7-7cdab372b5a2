package com.yunqu.yc.mediagw.base;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.event.impl.SQLExecutor;
import com.yunqu.yc.mediagw.event.impl.UserEventDispatcher;
import com.yunqu.yc.mediagw.filter.ExtXssHttpservletRequest;
import com.yunqu.yc.mediagw.log.MediagwIntefaceLogger;
import com.yunqu.yc.mediagw.model.VisitorModel;
import com.yunqu.yc.mediagw.util.CacheLongTxtUtil;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.web.EasyBaseServlet;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.ServletException;
import javax.servlet.ServletInputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.ByteArrayOutputStream;
import java.io.DataInputStream;
import java.io.IOException;
import java.io.InputStream;
import java.nio.charset.StandardCharsets;
import java.security.InvalidParameterException;
import java.sql.SQLException;
import java.util.*;

public abstract class AppBaseServlet extends EasyBaseServlet { 
	/**
	 *线程本地变量，每个线程独立
	 */
 	private static final ThreadLocal<JSONObject> bodyParamTL = new ThreadLocal();

	private static final Map<String,String>  schemas = new HashMap<String,String>();

	private static final long serialVersionUID = 1L;

	@Override
	protected void doGet(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        super.doGet(req, resp);
        bodyParamTL.remove();
    }
	@Override
    protected void doPost(HttpServletRequest req, HttpServletResponse resp) throws ServletException, IOException {
        super.doPost(req, resp);
        bodyParamTL.remove();
    }

	@Override
	protected String getAppDatasourceName() {
		return Constants.WRITER_DS_NAME_ONE;
	}

	@Override
	protected String getAppName() {
		return Constants.APP_NAME;
	}

	@Override
	protected String getLoggerName() {
		return Constants.APP_NAME;
	}
	
	protected String getTableName(String entId,String tableName){
		String dbName=getSchema(entId);
		if(StringUtils.notBlank(dbName)){
			return dbName+"."+tableName;
		}
		return tableName;
	}
	
	@Override
	public EasyQuery getQuery(){
		EasyQuery query=null;
		if(this.getUserPrincipal() == null){
			query=EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
			return query;
		}
		String entId = getEntId();
		if(StringUtils.isBlank(entId)){
			query=EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
			return query;
		}
		
		int _entId=Integer.parseInt(entId);
		if(_entId%2==0){
			 query=EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
		}else{
			query=EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_TOW);
		}
		return query;
	}
	
	/**
	 * 获取企业id，请求中必须带有entId或channelKey
	 * @return
	 */
	protected String getEntId() {
		JSONObject requestParam = getRequestParam();
		String entId = requestParam.getString("entId");
		String channelKey = requestParam.getString("channelKey");
		if(StringUtils.isBlank(entId)) {
			EntContext entContext = EntContext.getContext(channelKey);
			entId = entContext.getEntId();
		}
		return entId;
	}
	
	/**
	 * 获得
	 * @param entId
	 * @return
	 */
	protected String getSchema(String entId) {
		if (!schemas.containsKey(entId)) {
			try {
				String sql = "  SELECT  SCHEMA_ID  from  CC_ENT_RES  where  ENT_ID = ?  ";
				String schemaId = this.getQuery().queryForString(sql, new String[] { entId });
				schemas.put(entId, schemaId);
			} catch (Exception ex) {
			}
		}
		return schemas.get(entId);
	}
	
	protected JSONObject getParam(){
		JSONObject requestParam = getRequestParam();
		String msgId = requestParam.getString("_msgId");
		if(StringUtils.isBlank(msgId)) {
			msgId = RandomKit.uniqueStr();
		}
		requestParam.put("msgId", msgId);
		return requestParam;
	}

	/**
	 * 校验请求参数是否为空
	 * @param keys
	 * @throws Exception
	 */
	protected void checkParam(String[] keys) throws Exception{
		JSONObject param = getParam();
		checkParam(param,keys);
	}
	/**
	 * 校验请求参数是否为空
	 * @param keys
	 * @throws Exception
	 */
	protected void checkParam(JSONObject param,String[] keys) throws Exception{
		if(param==null||keys==null){
			throw new InvalidParameterException("parameter or keys is blank!");
		}
		for (String key : keys) {
			if(StringUtils.isBlank(param.getString(key))) {
				throw new Exception("必填参数["+key+"]不能为空");
			}
		}
	}

	protected EasyResult getResult() {
		EasyResult result = new EasyResult();
		try {
			result.put("msgId", getParam().getString("msgId"));
		} catch (Exception e) {
		}
		return result;
	}

	/**
	 * 从request中获取链接上的参数
	 * @return
	 */
	protected JSONObject getRequestQueryParams(){
		//query string
		JSONObject  paramObj = new JSONObject();
		HttpServletRequest request = getRequest();
		try {
			Map<String, String[]> paramMap=request.getParameterMap();
			if(paramMap!=null&& !paramMap.isEmpty()) {
				Iterator<String> paramKeys=paramMap.keySet().iterator();
				while(paramKeys.hasNext()) {
					String paramKey=paramKeys.next();
					String[] value = paramMap.get(paramKey);
					for(int j=0;j<value.length;j++){
						paramObj.put(paramKey, ExtXssHttpservletRequest.filter(value[j]));
					}
				}
			}
		} catch (Exception ex) {
			MediagwIntefaceLogger.getLogger().error(ex.getMessage(),ex);
		}
		return paramObj;
	}
	/**
	 * 从request中获取链接上的参数和body数据，body数据一次请求只能读取一次
	 * @return
	 */
	private JSONObject getRequestParam2(){
		//query string
		JSONObject  paramObj = getRequestQueryParams();
		HttpServletRequest request = getRequest();

		//body 数据
		int contLength = request.getContentLength();
		if(contLength<=0){
			return paramObj;
		}
		//注意：一次请求只支持DataInputStream.readFully()方法读取一次
		byte[] content = new byte[contLength];
		InputStream inputStream = null;
		DataInputStream dis = null;
		try {
			inputStream = request.getInputStream();
			dis = new DataInputStream(inputStream);
			dis.readFully(content);
			String contentStr = new String(content,StandardCharsets.UTF_8);
			JSONObject contObj = JSON.parseObject(ExtXssHttpservletRequest.filter(contentStr));
			if (contObj!=null){
				paramObj.putAll(contObj);
				bodyParamTL.set(contObj);
			}
		} catch (Exception ex) {
			MediagwIntefaceLogger.getLogger().error(ex.getMessage(),ex);
			JSONObject bodyParamTLObj = bodyParamTL.get();
			if (bodyParamTLObj!=null){
				paramObj.putAll(bodyParamTLObj);
			}
		}finally{
			try {
				if(inputStream!=null){
					inputStream.close();
				}
				if(dis!=null){
					dis.close();
				}
			} catch (Exception ex) {
				MediagwIntefaceLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
		return paramObj;
	}


	private JSONObject getRequestParam(){
		//query string
		JSONObject  paramObj = getRequestQueryParams();
		HttpServletRequest request = getRequest();
		//body 数据
		int contLength = request.getContentLength();
		if(contLength<=0){
			return paramObj;
		}
		//注意：一次请求只支持DataInputStream.readFully()方法读取一次
		InputStream inputStream = null;
		ByteArrayOutputStream buffer = null ;
		try {
			inputStream = request.getInputStream();
			buffer = new ByteArrayOutputStream();
			byte[] by1 = new byte[1024]; // 我们可以使用1024字节的缓冲区
			int bytesRead;
			// 循环读取输入流
			while ((bytesRead = inputStream.read(by1, 0, by1.length)) != -1) {
				buffer.write(by1, 0, bytesRead); // 写入缓冲器
			}
			buffer.flush(); // 确保缓冲区有数据
			byte[] contentBytes = buffer.toByteArray(); // 转换为字节数组
			String contentStr = new String(contentBytes, StandardCharsets.UTF_8);
			JSONObject contObj = JSON.parseObject(contentStr);
			if (contObj!=null){
				paramObj.putAll(contObj);
				bodyParamTL.set(contObj);
			}
		} catch (Exception ex) {
//			MediagwIntefaceLogger.getLogger().error(ex.getMessage(),ex);
			JSONObject bodyParamTLObj = bodyParamTL.get();
			if (bodyParamTLObj!=null){
				paramObj.putAll(bodyParamTLObj);
			}
		}finally{
			try {
				if(inputStream!=null){
					inputStream.close();
				}
				if(buffer!=null){
					buffer.close();
				}
			} catch (Exception ex) {
				MediagwIntefaceLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
		return paramObj;
	}

	/**
	 * 保存tempId=satisfy7的满意度
	 * @return
	 */
	protected EasyResult saveResultSatisfy7(JSONObject param){
		long thid = Thread.currentThread().getId();
		EasyResult result = getResult();
		result.setMsg("我们已收到反馈，感谢您的参与！");
		try {
			checkParam(param,new String[]{"satisfy7Data"});
			String sessionId = param.getString("sessionId");
			String channelKey = param.getString("channelKey");
			String serialId = param.getString("serialId");
			String chatSessionId = param.getString("chatSessionId");
			String satisfy7DataStr = param.getString("satisfy7Data");//
			JSONObject satisfy7Data = JSONObject.parseObject(satisfy7DataStr);
			String satisfyCode = satisfy7Data.getString("FIRST_SATISFY_CODE");//一级满意度选中code，单选
			String satisfyItemCode = satisfy7Data.getString("SECOND_SATISFY_CODE");//二级满意度细项选中code，多选 ，示例：1,2,3
			String busiType = satisfy7Data.getString("busiType");//满意度卡片提交类型：机器人满意度：10，人工会话满意度：2（会话中），6（会话结束后）

			String content = param.getString("content");
			String entId = getEntId();
			EasyCalendar cal = EasyCalendar.newInstance();
			int dateInt = cal.getDateInt();
			String satisfTime = cal.getDateTime("-");
			VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
			EasyQuery query = this.getQuery();
			EntContext context = EntContext.getContext(channelKey);

			//调用服务保存结果
			JSONObject reqParam = new JSONObject();

			reqParam.put("serviceId", "NEW-SATISFY-MEDIA-SERVICE");
			reqParam.put("command", "subSatisfy");
			reqParam.put("sessionId", chatSessionId);//会话id
			reqParam.put("channelKey", channelKey);

			reqParam.putAll(satisfy7Data);

			MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] saveResultSatisfy7 更新满意度明细接口 >> " + reqParam);
			IService service = ServiceContext.getService("NEW-SATISFY-MEDIA-SERVICE");
			JSONObject resultObj = service.invoke(reqParam);
			MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] saveResultSatisfy7 更新满意度明细接口 << " + resultObj);

			if (resultObj == null) {
				MediagwIntefaceLogger.getLogger().error("Thread[" + thid + "] saveResultSatisfy7  >> 保存[satisfy7-表情卡片]满意度失败，subSatisfy接口返回结果为空！");
				return result;
			}
			int saveState = resultObj.getIntValue("state");//0 失败，1 成功，2 超时，3重复
			result.setData(saveState);

			if (saveState == 2) {
				MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] saveResultSatisfy7  >> 会话已评价或超过评价时间！！！！");
				result.setMsg("该满意度调研已过期，谢谢您的支持！");
			}
			if (saveState == 3) {
				MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] saveResultSatisfy7  >> 重复评价！！！！");
				result.setMsg("您已完成本次满意度调研，感谢您的支持！");
			}

			//更新聊天记录满意度
			String str = query.queryForString("select TEMP_CONFIG from " + context.getTableName("CC_MEDIA_CHAT_RECORD") + " where CHAT_ID=?", new Object[]{serialId});
			if (StringUtils.isNotBlank(str)) {
				String dbName = getSchema(entId);
				if(str.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
					String str3 = CacheLongTxtUtil.get(dbName, serialId, "TEMP_CONFIG",str);
					if(StringUtils.isNotBlank(str3)){
						str = str3;
					}
				}
				JSONObject tempCofig = JSONObject.parseObject(str);
				if (tempCofig != null) {
					tempCofig.put("satisfyCode", satisfyCode);
					tempCofig.put("satisfyItemCode", satisfyItemCode);
					tempCofig.put("content", content);
					tempCofig.put("chatSessionId", chatSessionId);
					tempCofig.put("satisfy7Data", satisfy7Data);
					tempCofig.put("state", saveState);
					EasyRecord record2 = new EasyRecord(context.getTableName("CC_MEDIA_CHAT_RECORD"), "CHAT_ID");
					record2.setPrimaryValues(serialId);
					record2.set("TEMP_CONFIG", tempCofig.toJSONString());
					SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "update");
					UserEventDispatcher.addEvent(sqlExecutor2);
				}
			}

			if(saveState == 1){
				//更新会话的满意度
				EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_RECORD"), "SERIAL_ID");
				record.setPrimaryValues(chatSessionId);
				record.set("SATISF_CODE", satisfy7Data.getString("FIRST_SATISFY_CODE"));
				record.set("SATISF_NAME", satisfy7Data.getString("FIRST_SATISFY_NAME"));
				record.set("SATISF_CAUSE", satisfy7Data.getString("FIRST_SATISFY_CODE"));
				record.set("SATISF_USER_REMARKS", content);
				record.set("SATISF_TIME", satisfTime);
				SQLExecutor sqlExecutor = new SQLExecutor(sessionId, record, "update");
				UserEventDispatcher.addEvent(sqlExecutor);
				if("10".equals(busiType)){
					visitor.setSatisfyRobot(true);
				}else{//2和6都是人工会话满意度。
					visitor.setSatisfy(true);
				}
			}

		} catch (Exception e) {
			MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
			result.addFail("操作失败");
		}
		return result;
	}
	/**
	 * 保存tempId=satisfy7的满意度
	 * @return
	 */
	protected EasyResult saveResultSatisfy7() {
		return saveResultSatisfy7(getParam());
	}


}
