package com.yunqu.yc.mediagw.message.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.mediagw.base.*;
import com.yunqu.yc.mediagw.event.impl.*;
import com.yunqu.yc.mediagw.callback.SessionEventTrigger;
import com.yunqu.yc.mediagw.callback.event.SessionAutoReplyEvent;
import com.yunqu.yc.mediagw.callback.event.SessionCloseEvent;
import com.yunqu.yc.mediagw.callback.event.SessionQueueEvent;
import com.yunqu.yc.mediagw.callback.event.SessionStartEvent;
import com.yunqu.yc.mediagw.event.impl.UserEventDispatcher;
import com.yunqu.yc.mediagw.http.Proxy;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.*;
import com.yunqu.yc.mediagw.server.WebSocket;
import com.yunqu.yc.mediagw.service.largeModel.LargeModelHandle;
import com.yunqu.yc.mediagw.util.CacheUtil;
import com.yunqu.yc.mediagw.util.CommonUtil;
import com.yunqu.yc.mediagw.util.MediaCacheUtil;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.service.IService;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.Date;

/**
 * 全媒体路由中心（yc-mediacenter）发送消息到用户端
 *
 * <AUTHOR>
 */
public class CenterServerMessage extends ServerMessage {

//	private static Logger logger = MediagwServerLogger.getLogger();
    

    @Override
    public void invoke(String msg) {
        thid = Thread.currentThread().getId();

        try {
//            MediagwLogger.getLogger().debug("<CenterServerMessage> 收到MQ消息，处理全媒体路由中心发送的消息 << " + msg);
            JSONObject jsonObject = JSONObject.parseObject(msg);
            JSONObject dataObject = jsonObject.getJSONObject("data");
            if (dataObject == null) {
                MediagwLogger.getLogger().error("Thread["+thid+"]<CenterServerMessage> ConsumerMessage.onMessage() error ,cause:jsonObject is null");
                return;
            }
            JSONObject centerUserInfo = dataObject.getJSONObject("userInfo");
            String sessionId = dataObject.getString("sessionId");
            //此处有bug,重启或更新之后，内存中的用户信息被清空了，影响留言信息和离线消息的推送
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if (visitor == null) {
                MediagwLogger.getLogger().warn("Thread["+thid+"]<CenterServerMessage> ConsumerMessage.onMessage() error ,cause:user[" + sessionId + "] VisitorModel is null!");

                visitor = VisitorInfos.getInstance().getVisitorByDB(sessionId);
            }
            if (visitor == null) {
                MediagwLogger.getLogger().error("Thread["+thid+"]<CenterServerMessage> ConsumerMessage.onMessage() error ,cause:VisitorModel is null");
                return;
            }

            if (centerUserInfo == null) {
                centerUserInfo = new JSONObject();
            }

            EntContext entContext = EntContext.getContext(visitor.getChannelKey());

            String callbackUrl = visitor.getCallbackUrl();
            String msgType = dataObject.getString("msgType");
            dataObject.put("msgTime", EasyCalendar.newInstance().getDateTime("-"));//消息发送时间
            dataObject.put("userInfo", visitor.getSimpleUserInfo());
            dataObject.put("sessionId", visitor.getSessionId());
            dataObject.put("channelKey", visitor.getChannelKey());
            dataObject.put("channelId", entContext.getChannelId());
            dataObject.put("channelName", entContext.getChannelName());
            dataObject.put("channelType", entContext.getChannelType());

            RequestDataModel requestDataModel = new RequestDataModel(CommonUtil.getKey());
            String serialId = jsonObject.getString("serialId");
            if(StringUtils.isBlank(serialId)) {
                serialId = RandomKit.uniqueStr();
                jsonObject.put("serialId",serialId);
            }
            requestDataModel.setSerialId(serialId);
            requestDataModel.setChannelKey(visitor.getChannelKey());
            requestDataModel.setEntId(visitor.getEntId());
            requestDataModel.setTimestamp(System.currentTimeMillis() + "");
            requestDataModel.setCallbackUrl(callbackUrl);
            requestDataModel.setData(dataObject);

            String mediaEvent = dataObject.getString("mediaEvent");
            if (StringUtils.isBlank(mediaEvent)) mediaEvent = jsonObject.getString("event");

            if ("event".equals(msgType)) dataObject.put("event", mediaEvent);

            requestDataModel.setEvent(mediaEvent);
            MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage>  (" + sessionId + ") bizType[" + visitor.getBizType() + "],mediaEvent[" + mediaEvent + "]");

            if (StringUtils.isBlank(requestDataModel.getEvent())) {
                requestDataModel.setEvent("agent");
            }
            //开始会话,生成会话ID
            if ("start".equals(mediaEvent)) {
                AigcAgentExecutor.zxkfChatStart(visitor.getChannelKey(),sessionId,visitor.getChatSessionId());
                visitor.setChatSessionId(dataObject.getString("chatSessionId"));
                visitor.setAgent(dataObject);
                visitor.setBizType("zxkf");
//                CacheUtil.put(MediaConstants.USER_VISITOR + sessionId, visitor.toJSONString(), Constants.getVisitorCacheTimeOut());
                MediaCacheUtil.put(MediaConstants.USER_VISITOR + sessionId, visitor.toSimpleJSONString(), Constants.getVisitorCacheTimeOut());

                //1.01#20210429-2
                //验证是否要回复美的会员回复语
                dataObject.put("mediaEvent", "start");
                dataObject.put("event", "start");
                requestDataModel.setData(dataObject);
                requestDataModel.setEvent("start");
                requestDataModel = replyMemberWelcomeMsg(requestDataModel, jsonObject);
                jsonObject.put("data", requestDataModel.getData());
                //接入人工成功后，需要更新本地用户信息中的技能组
                visitor.getUserInfo().put("skillGroupId", centerUserInfo.getString("skillGroupId"));
                visitor.getUserInfo().put("skillGroupName", centerUserInfo.getString("skillGroupName"));
                triggerSessionStart(visitor.getEntId() , visitor.getChannelKey(), sessionId , dataObject);
            }

            //1.01#20210512-1 yc-mediacenter增加增加了转移事件（transfer）通知，搜到该事件通知时，需更新内存中visitorModel里的chatSessionId，agentId，用户无感知
            if ("transfer".equalsIgnoreCase(mediaEvent)) {
                visitor.setAgent(dataObject);
                visitor.setBizType("zxkf");
                visitor.setChatSessionId(dataObject.getString("chatSessionId"));
                MediaCacheUtil.put(MediaConstants.USER_VISITOR + sessionId, visitor.toSimpleJSONString(), Constants.getVisitorCacheTimeOut());
                return;
            }

            if ("selectKey".equals(mediaEvent)) {
                requestDataModel.setEvent("welcome");
                visitor.setBizType("selectKey");
                dataObject.put("msgType", "text");
            }

            if ("welcome".equals(mediaEvent)) {
                requestDataModel.setEvent("welcome");
                visitor.setBizType("welcome");
                dataObject.put("msgType", "text");
            }

            if ("getAgent".equals(mediaEvent)) {
                requestDataModel.setEvent("agent");
                visitor.setBizType("getAgent");
                dataObject.put("msgType", "text");
            }

            //坐席发送的消息
            if ("agent".equals(mediaEvent)) {
                requestDataModel.setEvent("agent");
                dataObject.put("sender", "agent");
            }

            //正在排队中，不接受88以外的命令
            if ("queue".equals(mediaEvent)) {
                requestDataModel.setEvent("queue");
                visitor.setBizType("queue");
                dataObject.put("msgType", "text");
                SessionQueueEvent queueEvent = new SessionQueueEvent(visitor.getEntId() , visitor.getChannelKey() , sessionId, dataObject);
                SessionEventTrigger.getInstance().process(queueEvent);
            }

            //这里如果是自动回复，则设置转机器人
            JSONObject robotInitcssData = null;
            if ("automatic".equals(mediaEvent)) {
                requestDataModel.setEvent("robot");
                visitor.setBizType("robot");

                String msgContent = entContext.getInRobotMsg();
                //保存系统回复语
                dataObject.put("sender", "robot");
                dataObject.put("event", "robot");
                dataObject.put("mediaEvent", mediaEvent);
                dataObject.put("msgType", "text");
                dataObject.put("msgContent", msgContent);
                dataObject.put("channelKey", visitor.getChannelKey());
                jsonObject.put("data", dataObject);

                requestDataModel = replyMemberWelcomeMsg(requestDataModel, jsonObject);
                String replyMsgContent = requestDataModel.getData().getString("msgContent");
                dataObject.put("msgContent", replyMsgContent);

                JSONObject contentObj = new JSONObject();
                contentObj.put("content", replyMsgContent);
                dataObject.put("msgContent", contentObj.toJSONString());

                //机器人消息体，content不需要json格式
                JSONObject robotData = new JSONObject();
                robotData.put("content", replyMsgContent);
                dataObject.put("robotData", robotData);
                //判断是否特殊访客，获取特殊访客角色
                H5SpRole spCustRole = CommonUtil.getSpCustRole(visitor);
                visitor.getUserInfo().put("userRoleId", spCustRole.getId());
                visitor.getUserInfo().put("userRoleName", spCustRole.getRoleName());

                SessionAutoReplyEvent autoReplyEvent = new SessionAutoReplyEvent(visitor.getEntId() , visitor.getChannelKey() , sessionId, dataObject);
                SessionEventTrigger.getInstance().process(autoReplyEvent);


                //初始化机器人
                if(!"000000".equals(visitor.getChannelKey())){

                    JSONObject initRobotJson = jsonObject.clone();

                    //读取CSS工单号，兼容美的服务工单详情页打开H5在线客服带入的工单号
                    JSONPath.remove(dataObject,"$.userInfo.serviceOrderNo");
                    JSONPath.remove(dataObject,"$.serviceOrderNo");
                    JSONPath.remove(initRobotJson,"$.data.userInfo.serviceOrderNo");
                    JSONPath.remove(initRobotJson,"$.data.serviceOrderNo");
                    visitor.getUserInfo().remove("serviceOrderNo");
//				    MediagwLogger.getLogger().info("Thread["+thid+"]<ConsumerMessage> ("+sessionId+") 用户接入机器人按键，初始化机器人请求 >>"+jsonObject);
                    JSONObject robotResult = CommonUtil.sendMsgToRobot(initRobotJson, visitor);
                    MediagwLogger.getLogger().info("Thread["+thid+"]<ConsumerMessage> ("+sessionId+") 用户接入机器人按键，初始化机器人成功！");
    //				MediagwLogger.getLogger().info("Thread["+thid+"]<ConsumerMessage> ("+sessionId+") 用户接入机器人按键，初始化机器人结果 <<"+robotResult);
                    Object welcomeCardList = JSONPath.eval(robotResult, "$.data.welcomeCardList");
                    if (welcomeCardList != null) {
                        String answerId = robotData.getString("answerId");
                        if(StringUtils.isBlank(answerId)){
                            answerId = visitor.getChatSessionId();
                        }
                        robotData.put("answerId", answerId);
                        robotData.put("welcomeCardList", welcomeCardList);
                    }
                    Object bottomNavList = JSONPath.eval(robotResult, "$.data.bottomNavList");
                    if (bottomNavList != null) {
                        robotData.put("bottomNavList", entContext.filterBottomNavList(bottomNavList));
                    }
                    //CSS工单数据
                    robotInitcssData = (JSONObject) JSONPath.eval(robotResult, "$.data.cssData");
                    if (robotInitcssData != null) {
                        dataObject.remove("cssData");
                    }
                    dataObject.remove("welcomeCardList");
                    dataObject.remove("bottomNavList");
                    dataObject.put("robotEvaluation", entContext.showRobotEvaluation(sessionId));
                    jsonObject.put("data", dataObject);
//                    MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage>  (" + sessionId + ") bizType[" + visitor.getBizType() + "],初始化机器人保存回复语："+jsonObject);
                    ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_ROBOT);
                }

                //2.0#20201014-1 更新接入记录的进机器人时间
                JSONObject data = new JSONObject();
                data.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                data.put("IS_IN_ROBOT", 1);
                data.put("ROBOT_START_TIME", EasyCalendar.newInstance().getDateTime("-"));
                LargeModelHandle.setInRobotFlag(visitor.getChatSessionId());
                AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), data);
            }

            String initialEvent = jsonObject.getString("initialEvent");//原始事件
            if (StringUtils.isNotBlank(initialEvent)) {
                requestDataModel.setEvent(initialEvent);
            }
            //视频客服事件，20201207参照《2021年美的用户交互中心平台视频客服项目详细设计说明书-20201123》
            if (mediaEvent.toLowerCase().contains("video")) {
                requestDataModel.setEvent(mediaEvent);
                dataObject.put("event", mediaEvent);
            }

            //处理小程序卡片消息，如果miniprogramUrl不为空，表示要动态生成小程序访问链接给H5客户端打开小程序
            if (StringUtils.equals("miniprogrampage", msgType)) {
                this.hanMiniprogramMsg(dataObject, entContext, sessionId);
            }

            requestDataModel.setData(dataObject);

            //通知机器人下线
            if ("close".equals(mediaEvent)||"end".equals(mediaEvent)) {
                CommonUtil.notifyRobotLogout(visitor,"CenterServerMessage:mediaEvent="+mediaEvent);
            }

            //mediacenter发过来的信息，关闭会话。// '挂机原因， 1 用户主动结束服务  2  坐席结束服务  3  超时结束  4 用户排队结束  5 用户登录超时 6 转移结束',
            if ("close".equals(mediaEvent)) {

                String agentPhone = visitor.getAgentPhone();

                visitor.setBizType("end");
                visitor.clearAgent();
                requestDataModel.setEvent("end");
                visitor.clearSelectKey();
                visitor.setLastChatTime(System.currentTimeMillis());//1.01#20210527-1


                String clearCause = dataObject.getString("clearCause");
                triggerSessionClose(visitor.getEntId() , visitor.getChannelKey() , sessionId , clearCause, dataObject);

                //人工会话结束
                if ("1".equals(clearCause) || "2".equals(clearCause) || "3".equals(clearCause)) {
                    AigcAgentExecutor.zxkfChatStop(visitor.getChannelKey(),sessionId,visitor.getChatSessionId());

                    //20210106 增加强制结束会话标记，不进入满意度，在发起视频会话时，用户点击强制结束上一通文本会话时发送data.notSatisfy=1。
                    String notSatisfy = CacheUtil.get(MediaConstants.NOT_IN_SATISFY_FLAG + sessionId);
                    if ("1".equals(notSatisfy)) {
                        visitor.setBizType("welcome");
                        CacheUtil.delete(MediaConstants.NOT_IN_SATISFY_FLAG + sessionId);
                        MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage> (" + sessionId + ") 用户点击强制结束上一通文本会话，不进入满意度评价也不回复任何消息");
                        return;
                    }

                    //20210204视频满意度评价完成之后，文本会话结束后不需要推送满意度
//					String flag = CacheUtil.get("SATISF_FLAG_"+visitor.getChatSessionId());

                    //判断满意度开关,未配置则默认开启
                    boolean satisfySwitch = entContext.getSatisfySwitch();
                    boolean chatIsSatisfy = visitor.isSatisfy();
                    if (!satisfySwitch || chatIsSatisfy) {
                        visitor.setBizType("welcome");
                        requestDataModel.setEvent("end");
                        MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage> (" + visitor.getSessionId() + ") 结束会话不推送满意度，满意度开关："+satisfySwitch+"，用户是否已评价："+chatIsSatisfy);
                    } else {
                        //满意推送方式：satisfy6-卡片（星星），satisfy7-卡片（表情）
                        String satisfyType = entContext.getChannelConf().getString("SATISFY_TYPE");

                        if (StringUtils.isNotBlank(satisfyType) && !"satisfy1".equals(satisfyType)) {

                            requestDataModel.setEvent("end");
                            requestDataModel.getData().put("chatSessionId", visitor.getChatSessionId());
                            JSONObject json = requestDataModel.toJSON();
                            jsonObject.put("data", requestDataModel.getData());
                            ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_SYS);//系统消息
                            //发送文本满意度内容到客户端
                            this.sendMsgTOClient(json.toJSONString());

                            //发送满意度模板到客户端
                            requestDataModel.setEvent("satisfy");
                            serialId = RandomKit.uniqueStr();

                            JSONObject tempConfig = entContext.getSatisfTempConfig(sessionId,visitor.getChatSessionId(), agentPhone,"6");
                            //人工会话满意度写入缓存，评价后删除
                            String tempConfigStr = tempConfig.toJSONString();
                            CacheUtil.put(MediaConstants.CKEY_LAST_A_CHAT_ID + sessionId, visitor.getChatSessionId(), 24*3600);
                            CacheUtil.put(MediaConstants.CKEY_A_CHAT_SATISFY + visitor.getChatSessionId(), serialId, 24*3600);
                            CacheUtil.put(MediaConstants.CKEY_A_SATISFY_ID + visitor.getChatSessionId(), tempConfig.getString("serialId"), 24*3600);

                            dataObject.put("tempConfig", tempConfigStr);
                            requestDataModel.setSerialId(serialId);
                            requestDataModel.getData().put("tempConfig", tempConfigStr);

                            //读取满意度推送提示语
                            String satisfyPushMsg = entContext.getSatisfyPushMsg();
                            requestDataModel.getData().put("msgContent", satisfyPushMsg);

                            jsonObject.put("serialId", serialId);
                            jsonObject.put("data", requestDataModel.getData());
                            ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_SYS);//系统消息
                            this.sendMsgTOClient(requestDataModel.toJSON().toJSONString());

                            visitor.setBizType("welcome");
                            visitor.setChatSessionId(RandomKit.randomStr());
                            return;
                        } else {
                            //满意推送方式： 为空-文本，satisfy1-文本，
                            requestDataModel.setEvent("satisfy");
                            visitor.setBizType("satisfy");
                            try {
                                IService service = ServiceContext.getService("MEDIA-SATISFY-SERVICE");
                                if (service != null) {
                                    requestDataModel.setData(dataObject);
                                    JSONObject result = service.invoke(JSONObject.parseObject(requestDataModel.toString()));

                                    dataObject.put("event", "satisfy");
                                    if (result == null) {
                                        visitor.setBizType("welcome");
                                        requestDataModel.setEvent("end");
                                    } else {
                                        JSONObject satisfyData = result.getJSONObject("data");
                                        if ("bye".equals(satisfyData.getString("command"))) {//专家坐席没有满意度
                                            visitor.setBizType("end");
                                            requestDataModel.setEvent("end");
                                            dataObject.put("mediaEvent", "close");//用于关闭专家咨询页面的输入框
                                            requestDataModel.setData(dataObject);
                                        } else {
                                            dataObject.put("msgContent", satisfyData.getString("msgContent"));
                                            requestDataModel.setData(dataObject);
                                            jsonObject.put("msgContent", satisfyData.getString("msgContent"));
                                            jsonObject.put("data", requestDataModel.getData());
                                            JSONObject data = jsonObject.getJSONObject("data");
                                            data.put("channelKey", visitor.getChannelKey());
                                            dataObject.put("mediaEvent", "satisfy");
                                            jsonObject.put("data", data);
                                        }
                                    }
                                } else {
                                    MediagwLogger.getLogger().warn("Thread["+thid+"]<CenterServerMessage> (" + visitor.getSessionId() + ") 未找到满意度评价服务[MEDIA-SATISFY-SERVICE]！");
                                }
                            } catch (Exception ex) {
                                MediagwLogger.getLogger().error(ex.getMessage());
                                // 如果满意度处理异常，则设置为welcome状态
                                visitor.setBizType("welcome");
                                requestDataModel.setEvent("end");
                                MediagwLogger.getLogger().error(ex.getMessage(), ex);
                            }
                        }
                    }

                } else if ("4".equals(clearCause)) {//用户取消排队
                    if ("000000".equals(visitor.getChannelKey())) {
                        dataObject.put("mediaEvent", "close");//用于关闭专家咨询页面的输入框
                        requestDataModel.setData(dataObject);
                    }
                } else if ("5".equals(clearCause)) {//排队超时

                    if ("000000".equals(visitor.getChannelKey())) {//咨询专家不进入进入留言
                        dataObject.put("mediaEvent", "close");
                        requestDataModel.setData(dataObject);
                    } else {
                        JSONObject accesssData = new JSONObject();
                        accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                        accesssData.put("END_TIME", EasyCalendar.newInstance().getDateTime("-"));
                        accesssData.put("CLEAR_CAUSE", 5);//5.排队超时结束
                        //判断排队超时是否开启自动进入留言功能
                        //留言超时时间小于0，不进入留言，直接结束会话
                         long wordTimeout = entContext.wordTimeout();
                        if(!entContext.isQueueTimeout2WordFlag()||wordTimeout <= 0) {
                            MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage> (" + sessionId + ") 排队超时未开启自动进入留言或者留言超时，直接结束会话");
                            visitor.setBizType("end");
                            visitor.clearSelectKey();
                            jsonObject.put("event", "end");
                            requestDataModel.setEvent("end");
                        }else{
                            MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage> (" + sessionId + ") 排队超时未开启自动进入留言功能，直接结束会话");
                            Thread.sleep(200);
                            visitor.setBizType("word");
                            requestDataModel.setEvent("word");
                            jsonObject.put("event", "end");
                            accesssData.put("IS_IN_WORD", 1);
                            accesssData.put("CLEAR_CAUSE", 7);//7.排队超时进留言
                        }
                        //更新接入记录
                        AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
                    }
                } else if ("6".equals(clearCause)) {//转移结束

                } else {
                    if ("000000".equals(visitor.getChannelKey())) {//用于关闭专家咨询页面的输入框
                        dataObject.put("mediaEvent", "close");
                        requestDataModel.setData(dataObject);
                    }
                }
                requestDataModel.getData().put("msgType", "text");
            }

            //替换回调消息中的文件服务器地址
            dataObject.put("msgContent", entContext.replaceFileServerUrl(dataObject.getString("msgType"),dataObject.getString("msgContent"), "out"));

            //统一处理结束事件，如果结束会话，则清空
            if ("end".equals(visitor.getBizType()) || "end".equals(mediaEvent)) {

                if ("000000".equals(visitor.getChannelKey())) {

                    visitor.setChatSessionId(RandomKit.uniqueStr());
                    dataObject.put("mediaEvent", "close");//用于关闭专家咨询页面的输入框
                    requestDataModel.setData(dataObject);
                    requestDataModel.getData().put("msgType", "text");
                }

                //清除按键信息
                visitor.clearSelectKey();
                visitor.setBizType("welcome");
                requestDataModel.setEvent("end");

                //如果离线消息类型是外部的渠道，OFFLINE_CHAT_TYPE="OUT"（websocket连接方式），发送消息后，断开客户端连接
                if (entContext.isWS()) {
                    JSONObject params = new JSONObject();
                    params.put("serialId", requestDataModel.getSerialId());
                    params.put("channelKey", requestDataModel.getChannelKey());
                    params.put("entId", requestDataModel.getEntId());
                    params.put("timestamp", requestDataModel.getTimestamp());
                    params.put("sign", requestDataModel.getSign());
                    params.put("data", requestDataModel.getData());
                    params.put("event", requestDataModel.getEvent());
                    WebSocket.sendMessageToClient(sessionId, params.toJSONString());
                    return;
                }

            }

            //过滤事件消息，mediaEvent=start或close 时,msgContent是空的
            if (StringUtils.isBlank(requestDataModel.getData().getString("msgContent"))) {
                return;
            }

            //如果离线消息类型是外部的渠道，OFFLINE_CHAT_TYPE="OUT"（websocket连接方式），直接发送消息给客户端
            if (entContext.isWS()) {
                JSONObject params = new JSONObject();
                params.put("serialId", requestDataModel.getSerialId());
                params.put("channelKey", requestDataModel.getChannelKey());
                params.put("entId", requestDataModel.getEntId());
                params.put("timestamp", requestDataModel.getTimestamp());
                params.put("sign", requestDataModel.getSign());
                params.put("data", requestDataModel.getData());
                params.put("event", requestDataModel.getEvent());
                WebSocket.sendMessageToClient(sessionId, params.toJSONString());
                return;
            }

            requestDataModel = replyMemberWelcomeMsg(requestDataModel, jsonObject);
            //替换agentName
            String msgContent = requestDataModel.getData().getString("msgContent");
            String agentName = visitor.getAgentName();
            if (StringUtils.isNotBlank(agentName))
                requestDataModel.getData().put("msgContent", msgContent.replaceAll("#agentName#", agentName));

            requestDataModel.getData().put("chatSessionId", visitor.getChatSessionId());

            JSONObject json = requestDataModel.toJSON();
            JSONObject dataObj = json.getJSONObject("data");
            if (!dataObj.containsKey("channelKey")) {
                dataObj.put("channelKey", visitor.getChannelKey());
            }
            this.sendMsgTOClient(json.toJSONString());

            //保存接入机器人时推送的CSS工单列表，msgType=cssData
            doCssData(robotInitcssData,jsonObject,visitor);
        } catch (Exception ex) {
            MediagwLogger.getLogger().error(ex.getMessage(), ex);
        }

    }

    private void triggerSessionStart(String entId , String channelKey , String sessionId , JSONObject jsonObject){
        SessionStartEvent sessionStartEvent = new SessionStartEvent(entId , channelKey , sessionId  , jsonObject);
        UserEventDispatcher.addEvent(new SessionEventExecutor(sessionStartEvent));
    }

    /**
     * 触发媒体会话关闭事件
     */
    private void triggerSessionClose(String entId , String channelKey , String sessionId , String clearCause, JSONObject jsonObject){
        SessionCloseEvent sessionCloseEvent = new SessionCloseEvent(entId , channelKey , sessionId , clearCause , jsonObject);
        UserEventDispatcher.addEvent(new SessionEventExecutor(sessionCloseEvent));
    }


    /**
     * 回复美的会员欢迎语
     * @return
     */
    private RequestDataModel replyMemberWelcomeMsg(RequestDataModel requestDataModel, JSONObject jsonObject) {
        try {
            JSONObject dataObject = requestDataModel.getData();
            String cacheKey = "levelNameContent_" + requestDataModel.getData().getString("sessionId");
            String chacheLevelName = CacheUtil.get(cacheKey);

//            MediagwLogger.getLogger().debug("replyMemberWelcomeMsg()--->cacheKey:" + cacheKey + "===" + chacheLevelName);
            if (StringUtils.isBlank(chacheLevelName)) {
                chacheLevelName = "客户";
            }

            String msgContent = dataObject.getString("msgContent");
            if (StringUtils.isNotBlank(msgContent)) {
                msgContent = msgContent.replaceAll("#levelName#", chacheLevelName).replaceAll("#levelName_md_2#", chacheLevelName);
            }
            requestDataModel.getData().put("msgContent", msgContent);
//			jsonObject.getJSONObject("data").put("msgContent",msgContent);
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
        return requestDataModel;
    }

    /**
     * 处理小程序卡片消息
     *
     * @Description : 处理小程序卡片消息，如果miniprogramUrl不为空，表示要动态生成小程序访问链接给H5客户端打开小程序，生成成功后要更新聊天记录中的小程序卡片消息
     * <AUTHOR>
     * @Datetime 2022/3/23 19:44
     * @Param dataObject:
     * @Param entContext:
     * @return: void
     */
    private void hanMiniprogramMsg(JSONObject dataObject, EntContext entContext, String openId) {
        JSONObject msgObj = dataObject.getJSONObject("msgContent");
        String miniprogramUrl = msgObj.getString("miniprogramUrl");
        String chatId = msgObj.getString("chatId");
        if (StringUtils.isBlank(miniprogramUrl)) {
            return;
        }
        String miniProgramPagePath = msgObj.getString("miniProgramPagePath");
        miniProgramPagePath = miniProgramPagePath.replace(miniprogramUrl + "?", "");
        String appletLink = this.getApplet(miniprogramUrl, entContext.getWXGenerateSchemeUrl(), msgObj.getString("miniProgramAppId"), miniProgramPagePath, openId);
        msgObj.put("miniProgramPagePath", appletLink);
        dataObject.put("msgContent", msgObj.toJSONString());

        //更新聊天记录中的小程序卡片消息，确保客户端重新加载历史聊天记录可打开小程序
        try {
            String msgStr = QueryFactory.getQuery().queryForString("select MSG_CONTENT FROM " + entContext.getTableName("CC_MEDIA_CHAT_RECORD") + " WHERE CHAT_ID = ?", new Object[]{chatId});
            JSONObject jsonObject1 = JSONObject.parseObject(msgStr);
            jsonObject1.put("miniProgram", msgObj.toJSONString());
            QueryFactory.getQuery().execute("update " + entContext.getTableName("CC_MEDIA_CHAT_RECORD") + " set MSG_CONTENT = ? where CHAT_ID = ? ", jsonObject1.toJSONString(), chatId);
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
    }

    /**
     * 生成小程序链接
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2022/3/4 19:01
     * @Param minUrl: 小程序地址
     * @Param appletUrl: 中控生成小程序接口
     * @Param appid: 小程序appid
     * @Param pagePath: 小程序地址页面路径
     * @return: java.lang.String
     */
    private String getApplet(String minUrl, String appletUrl, String appid, String pagePath, String openId) {
        JSONObject result = new JSONObject();

        try {
            EasyCalendar cal = EasyCalendar.newInstance();
            cal.add(EasyCalendar.HOUR, 2);
            JSONObject postData = new JSONObject();
            postData.put("expireTime", cal.getTimeInMillis() / 1000);//有效期，时间戳/1000
            postData.put("openId", openId);
            postData.put("userType", "openId");
            postData.put("isExpire", "true");

            JSONObject jumpWxa = new JSONObject();
            jumpWxa.put("path", minUrl);
            jumpWxa.put("query", pagePath);
            jumpWxa.put("envVersion", "release");
            postData.put("jumpWxa", jumpWxa);
//            MediagwLogger.getLogger().info("Thread["+thid+"]调用中控接口查询小程序视频客服链接，url=" + appletUrl + ",请求参数：" + postData);
            String postBody = Proxy.doPostJson(appletUrl, postData);
//            MediagwLogger.getLogger().info("Thread["+thid+"]调用中控接口查询小程序视频客服链接，响应结果：" + postBody);
            JSONObject parseObject = JSON.parseObject(postBody);

            //旧接口地址
//			{"serverTime":1650792417,"code":0,"msg":"成功","requestId":389286227789197312,"errorCode":0,"errorMessage":"成功","errorMsg":"成功","value":"weixin://dl/business/?t=T73Ra9C7Tab","data":"weixin://dl/business/?t=T73Ra9C7Tab"}
            if (appletUrl.contains("WCP_MA_GENERATE_SCHEME/") && parseObject.getIntValue("code") == 0) {
                pagePath = parseObject.getString("value");
            }

            //新接口地址
//			{"serverTime":1650792044,"code":0,"msg":"成功","requestId":389284664259497984,"errorCode":0,"errorMessage":"成功","errorMsg":"成功","value":{"expireTime":1650799244,"creationTime":1650792044422,"scheme":"weixin://dl/business/?t=DmQhsKAm8Zf","openId":"oFtQywNSvcMOqbvAK3g7FLN3JAnQ","userType":"openId"},"data":{"expireTime":1650799244,"creationTime":1650792044422,"scheme":"weixin://dl/business/?t=DmQhsKAm8Zf","openId":"oFtQywNSvcMOqbvAK3g7FLN3JAnQ","userType":"openId"}}
            if (appletUrl.contains("WCP_MA_GENERATE_SCHEME_NEW/") && parseObject.getIntValue("code") == 0) {
                pagePath = (String) JSONPath.eval(parseObject, "$.data.scheme");
            }
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
        MediagwLogger.getLogger().info("Thread["+thid+"]调用中控接口查询小程序视频客服链接，查询到的pagePath：" + pagePath);

        return pagePath;
    }

    /**
     * 处理CSS工单数据
     * @param robotInitcssData
     * @param resultJson
     * @param visitor
     * @throws Exception
     */
    private void doCssData(JSONObject robotInitcssData,JSONObject resultJson,VisitorModel visitor) throws Exception {
        if (robotInitcssData != null) {
            String sessionId = visitor.getSessionId();
            String title = (String) robotInitcssData.getOrDefault("title", "您是否想咨询以下工单？可直接对工单进行操作哦");
            //2.发送并保存工单数据，msgType=cssData
//            {
//                "data": {
//                    "userInfo":{...},
//                    "channelType":"2",
//                    "sessionId":"oFtQywNSvcMOqbvAK3g7FLN3JAnQ",
//                    "chatSessionId":"82680670059014448373474",
//                    "msgContent":"",
//                    "channelKey":"gh_58f8a50fddbb",
//                    "serialId":"82680670052018846654216css",
//                    "msgTime":"2024-11-18 20:29:54",
//                    "channelName":"美美测试H5",
//                    "event":"robot",
//                    "channelId":"84664589459219995533711",
//                    "sender":"robot",
//                    "msgType":"cssData",
//                    "bizType":"robot",
//                    "robotData":{
//                        "content":"您是否想咨询一下工单？可直接对工单进行操作哦",
//                        "cssData":{
//                              "title":"您是否想咨询一下工单？可直接对工单进行操作哦",
//                            "list":[...]
//                        },
//                        "bottomNavList":[...]
//                    }
//                }
//            }
            JSONObject jsonObject_clone2 = resultJson.clone();
            long timestamp_clone2 = jsonObject_clone2.getLongValue("timestamp");
            if(timestamp_clone2==0){
                timestamp_clone2 = System.currentTimeMillis();
            }
            timestamp_clone2 += 20;
            EasyCalendar easyCal2 = EasyCalendar.newInstance(new Date(timestamp_clone2));
            jsonObject_clone2.put("timestamp",timestamp_clone2);
            String serialId_clone2 = jsonObject_clone2.getString("serialId")+"2";
            JSONObject dataObject_clone2 = jsonObject_clone2.getJSONObject("data");
            JSONObject robotData_clone2 = dataObject_clone2.getJSONObject("robotData");
            robotData_clone2.remove("welcomeCardList");
//            robotData_clone2.remove("bottomNavList");
            robotData_clone2.remove("answerId");
            robotData_clone2.put("cssData", robotInitcssData);
            robotData_clone2.put("content",title);
            dataObject_clone2.put("robotData", robotData_clone2);
            dataObject_clone2.put("msgTime",easyCal2.getDateTime("-"));
            dataObject_clone2.put("msgType", "cssData");
            dataObject_clone2.put("serialId",serialId_clone2);
            dataObject_clone2.put("msgContent", "");//sender=robot时，页面不使用该字段，而是使用robotData.content
            dataObject_clone2.remove("mediaEvent");

            if (!dataObject_clone2.containsKey("channelKey")) {
                dataObject_clone2.put("channelKey", visitor.getChannelKey());
            }
            jsonObject_clone2.put("data", dataObject_clone2);
            jsonObject_clone2.put("serialId", serialId_clone2);
            JSONPath.set(jsonObject_clone2,"$.data.robotData.cssData.showTxtContent",1);
            //发送消息
            Thread.sleep(50);
            this.sendMsgTOClient(jsonObject_clone2.toJSONString());
//            MediagwLogger.getLogger().info("Thread["+thid+"]<CenterServerMessage>  (" + sessionId + ") bizType[" + visitor.getBizType() + "],初始化机器人保存CSS工单数据2："+jsonObject_clone2);
            ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject_clone2, ChatMessage.MSG_SENDER_ROBOT);
        }
    }
}
