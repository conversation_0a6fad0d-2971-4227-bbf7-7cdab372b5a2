package com.yunqu.yc.mediagw.servlet.app;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.BaseServlet;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.enums.ResponseEnum;
import com.yunqu.yc.mediagw.log.MediagwIntefaceLogger;
import com.yunqu.yc.mediagw.model.VisitorModel;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.List;
import java.util.Set;

/**
 * 美居app视频客服评价页面
 * <AUTHOR>
 *
 */
@WebServlet({ "/servlet/videoAppraise" })
public class VideoAppraiseServlet extends BaseServlet {

	private static final long serialVersionUID = 4313271959495941178L;
	
	
	@Override
	public JSONObject doing(JSONObject dataByRequest) throws Exception {
		System.out.println(1111);
		//会话记录
		String query = dataByRequest.getString("query");
		JSONObject json =  new JSONObject();
		JSONObject result = ResponseEnum.SUCC.toJSON();
		//请求数据
		if(query.equals("getInfo")){
			json= getInfo(dataByRequest);
		}else if(query.equals("getEvaluate")){
			json= getEvaluate(dataByRequest);
		}else if(query.equals("submitAppraise")){
			json= submitAppraise(dataByRequest);
		}else{
			
		}
		result.put("data", json);
		return result;
	}
	public  JSONObject getInfo(JSONObject dataByRequest){
		String serialId = dataByRequest.getString("serialId");
		EasyQuery query = QueryFactory.getQuery();

		String sql="select AGENT_NO as agentNo from ycbusi.CC_MEDIA_RECORD where serial_Id=? ";
		try {
			return query.queryForRow(sql, new Object[]{serialId},new JSONMapperImpl());
		} catch (SQLException e) {
			MediagwIntefaceLogger.getLogger().error(e.getMessage(),e);
			e.printStackTrace();
		}
		return new JSONObject();
	}
	public  JSONObject getEvaluate(JSONObject dataByRequest){
		EasyQuery query = QueryFactory.getQuery();
		JSONObject jsonObject = new JSONObject();
		String reloadKey = "YC-MEDIAGW-MIDEA_EVALUATE";
		String channelReload = CacheManager.getMemcache().get(reloadKey);//获取配置
		MediagwIntefaceLogger.getLogger().debug(reloadKey+"缓存："+channelReload);

		String serialId = dataByRequest.getString("serialId");

		if(StringUtils.isBlank(channelReload)){
			String sql="select T1.SORT_NUM CODE,T1.NAME from  ywdb.C_CF_DICT T1,ywdb.C_CF_DICTGROUP T2 where T1.DICT_GROUP_ID=T2.ID AND T2.CODE=? order by T1.SORT_NUM DESC";//2022-03-01 改为降序 5~1
			String sql1="select T1.ID,T1.NAME from  ywdb.C_CF_DICT T1,ywdb.C_CF_DICTGROUP T2 where T1.DICT_GROUP_ID=T2.ID AND T2.CODE=? order by T1.SORT_NUM ";
			String sql2="SELECT CONFIG FROM YCBUSI.CC_MEDIA_CONFIG WHERE ENT_ID =? ";
			try {
				List<JSONObject> evaluate = query.queryForList(sql, new Object[]{"MEDIA_VIDEO_EVALUATE"},new JSONMapperImpl());
				List<JSONObject> index= query.queryForList(sql1, new Object[]{"MEDIA_VIDEO_INDEX"},new JSONMapperImpl());
				JSONObject config= query.queryForRow(sql2, new Object[]{"1000"},new JSONMapperImpl());
				jsonObject.put("evaluate", evaluate);
				jsonObject.put("index", index);
				if(config.get("CONFIG")!=null){
					jsonObject.put("title", JSON.parseObject(config.getString("CONFIG")).get("VIDEO_APPRAISE_TITLE"));
				}else{
					jsonObject.put("title", "您好，如对我的解答满意，麻烦您给个好评哟，您的评价非常重要呢~");
				}	
				
				//查询会话工号
				sql="select AGENT_NO as agentNo from ycbusi.CC_MEDIA_RECORD where serial_Id=? ";
				String agentNo = query.queryForString(sql, new Object[]{serialId});
				jsonObject.put("agentNo", agentNo);
			} catch (SQLException e) {
				MediagwIntefaceLogger.getLogger().error(e.getMessage(),e);
	            String evaluate="[{'CODE':'5','NAME':'服务结果不满意'},{'CODE':'4','NAME':'服务态度不满意'},{'CODE':'3','NAME':'一般'},{'CODE':'2','NAME':'满意'},{'CODE':'1','NAME':'非常满意'}]";

	            String index="[{'NAME':'服务态度',ID:'INDEX_1'},{'NAME':'声音速度',ID:'INDEX_2'},{'NAME':'仪表笑容',ID:'INDEX_3'},{'NAME':'视频环境',ID:'INDEX_4'}]";
				jsonObject.put("evaluate", JSON.parseArray(evaluate));
				jsonObject.put("index", JSON.parseArray(index));
				jsonObject.put("title", "您好，如对我的解答满意，麻烦您给个好评哟，您的评价非常重要呢~");
				jsonObject.put("type", 0);
			}
			CacheManager.getMemcache().put(reloadKey, jsonObject.toJSONString(),10*60);;
		}else{
			jsonObject=JSONObject.parseObject(channelReload) ;
		}
		return jsonObject;
	}
	/**
	 * 提交满意度
	 * @param dataByRequest
	 * @return
	 */
	public  JSONObject submitAppraise(JSONObject dataByRequest){
		MediagwIntefaceLogger.getLogger().info("提交视频满意度评价："+dataByRequest);
		EasyQuery query = QueryFactory.getQuery();
		JSONObject jsonObject = new JSONObject();
		String serialId = dataByRequest.getString("serialId");
		String content  = dataByRequest.getString("content");
		String evaluate = dataByRequest.getString("evaluate");
		String evaluateName = dataByRequest.getString("evaluateName");
		String updateSQL="UPDATE ycbusi.CC_MEDIA_RECORD SET SATISF_CODE=?,SATISF_NAME=?,SATISF_USER_REMARKS=?  where SERIAL_ID=?";
		try {
			int executeUpdate = query.executeUpdate(updateSQL,new Object[]{evaluate,evaluateName,content,serialId});
			if(executeUpdate!=0){
				query.execute("delete from ycbusi.C_STF_VIDEO_SATISF_COLLECTION where RECORD_ID=?", new Object[] {serialId});
				 Set<String> keySet = dataByRequest.keySet();
				 for (String key : keySet) {
					 String value = dataByRequest.getString(key);
					 if(key.startsWith("item-")&&StringUtils.isNotBlank(value)){
						 String[] valueArr = value.split(",");
						 JSONObject json = new JSONObject();
						 json.put("RECORD_ID", serialId);
						 json.put("SATISF_NAME", valueArr[0]);
						 json.put("SATISF_VALUE", valueArr[1]);
						 json.put("SATISF_ID", key.replace("item-", ""));//满意度ID预留
						 json.put("ID", RandomKit.randomStr());
						 json.put("CREATE_TIME", EasyDate.getCurrentDateString());//
						 EasyRecord record = new EasyRecord("ycbusi.C_STF_VIDEO_SATISF_COLLECTION", "ID").setColumns(json);
						 query.save(record);
		            }
				}
			}

			//20210204视频满意度评价完成之后需标记为已评价，文本会话结束后不需要推送满意度
			JSONObject chatRecord = query.queryForRow("select * from ycbusi.CC_MEDIA_RECORD where SERIAL_ID=?",new Object[]{serialId},new JSONMapperImpl());
			if(chatRecord!=null&&StringUtils.isNotBlank(chatRecord.getString("SATISF_CODE"))){
				String sessionId = chatRecord.getString("SESSION_ID");
				VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
				if(visitorModel!=null&&StringUtils.equals(serialId,visitorModel.getChatSessionId())){
					visitorModel.setSatisfy(true);
				}
			}

		} catch (Exception e) {
			MediagwIntefaceLogger.getLogger().error("提交满意度失败"+e.getMessage(),e);
		}
		jsonObject.put("msg", "操作成功");
		return jsonObject;
		
	}
}
