package com.yunqu.yc.agent.chat;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.EntContext;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.agent.chat.model.MessageModel;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.util.CacheLongTxtUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.utils.string.StringUtils;

import java.util.ArrayList;
import java.util.List;

public abstract class ChatMessage {

	protected String entId ;
	
	
	protected String  getTableName(String tableName){
		EntContext entContext = EntContext.getContext(this.entId);
		return entContext.getTableName(tableName);
	}
	
	protected EasyQuery getQuery(){
		EasyQuery easyQuery = QueryFactory.getQuery(this.entId);
		easyQuery.setLogger(MediaLogger.getLogger());
		return easyQuery;
	}
	
	/**
	 * 保存聊天记录
	 * @param msgModel
	 * @throws Exception
	 */
	public  abstract  void save(MessageModel msgModel) throws Exception;
	
	/**
	 * 更新聊天记录
	 * @param data
	 * @throws Exception
	 */
	public  abstract  void update(JSONObject data) throws Exception;
	
	/**
	 * 获取用户聊天记录
	 * @param sessionId
	 * @param msgTime
	 * @return
	 */
	public  abstract  List<MessageModel> load(String sessionId,String msgTime) throws Exception;
	
	/**
	 * 获取用户聊天记录
	 * @param chatId
	 * @param msgTime
	 * @return
	 */
	public  abstract  List<MessageModel> loadHis(String chatId,String msgTime) throws Exception;

	/**
	 * 查找本次聊天的聊天记录
	 * @param sessionId
	 * @param content
	 * @return
	 */
	public abstract List<MessageModel> findSessionMessage(String sessionId,String content,String startTime,String endTime) throws Exception;

	/**
	 * 查找客户相关的聊天记录
	 * @param custSessionId
	 * @param content
	 * @return
	 */
	public   abstract  List<MessageModel> findCustMessage(String custSessionId,String content,String startTime,String endTime) throws Exception;

	/**
	 * 查询所有聊天记录
	 * @param content
	 */
	public abstract List<MessageModel> findAllMessage(String content,String startTime,String endTime) throws Exception;

	public abstract MessageModel getMessage(String serialId) throws Exception;

	public abstract JSONObject getChat(String sessionId,String chatSessionId) throws Exception;

	
	protected List<MessageModel> sort(List<MessageModel> list){
		EntContext entContext = EntContext.getContext(this.entId);
		String schemaId = entContext.getSchemaId();
		List<MessageModel> _list = new  ArrayList<MessageModel>();
		for(int i = (list.size()-1) ;i >= 0; i--){
			//读取缓存中的超长内容 begin
			MessageModel msgModel = list.get(i);
			String serialId = msgModel.getSerialId();
			String robotData = msgModel.getRobotData();
			if(StringUtils.isNotBlank(robotData) && robotData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str1 = CacheLongTxtUtil.get(schemaId,serialId, "ROBOT_DATA",robotData);
				if(StringUtils.isNotBlank(str1)){
					//移除"welcomeCardList"，"bottomNavList"
					JSONObject robotDataObj = JSONObject.parseObject(str1);
					robotDataObj.remove("welcomeCardList");
					robotDataObj.remove("bottomNavList");
					msgModel.setRobotData(robotDataObj.toJSONString());
				}
			}
			String msgContent = msgModel.getMsgContent();
			if(StringUtils.isNotBlank(msgContent) && msgContent.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str2 = CacheLongTxtUtil.get(schemaId,serialId, "MSG_CONTENT",msgContent);
				if(StringUtils.isNotBlank(str2)){
					msgModel.setMsgContent(str2);
				}
			}
			String tempConfig = msgModel.getTempConfig();
			if(StringUtils.isNotBlank(tempConfig) && tempConfig.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str3 = CacheLongTxtUtil.get(schemaId,serialId, "TEMP_CONFIG",tempConfig);
				if(StringUtils.isNotBlank(str3)){
					msgModel.setTempConfig(str3);
				}
			}
			//消息引用逻辑：读取被引用消息的超长文本内容
			String quoteData = msgModel.getQuoteData();
			if(StringUtils.isNotBlank(quoteData) && quoteData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
				String str4 = CacheLongTxtUtil.get(schemaId, serialId, "QUOTE_DATA",quoteData);
				if(StringUtils.isNotBlank(str4)){
					msgModel.setQuoteData(str4);
				}
			}

			//读取缓存中的超长内容 end
			parseMsg(msgModel);
			_list.add(msgModel);
		}
		return _list;
	}

	/**
	 * 处理全媒体在线客服消息
	 * @param msgModel
	 * @return
	 */
	private void parseMsg(MessageModel msgModel){
		String content = msgModel.getMsgContent();
		String robotDataStr = msgModel.getRobotData();
		JSONObject robotData = null;
		if(StringUtils.isNotBlank(robotDataStr)){
			try {
				robotData = JSONObject.parseObject(robotDataStr);
			} catch (Exception e) {
				MediaLogger.getLogger().error(e.getMessage(),e);
			}
		}
		if(robotData == null){
			return;
		}
		if("{}".equals(content)){
			//机器人引导问消息
			JSONObject answerList = robotData.getJSONObject("answerList");
			if(answerList != null && answerList.containsKey("beforeWord")&& answerList.containsKey("itemList")){
				JSONArray itemList = answerList.getJSONArray("itemList");
				if (itemList !=null&&itemList.size()>0) {
					String beforeWord = answerList.getString("beforeWord");
					String afterWord = (String) answerList.getOrDefault("afterWord","请点击上方问题获取答案，或重新详细描述您的问题");
					StringBuilder contentSbd = new StringBuilder(beforeWord + "\n");
					for (int i = 0; i <itemList.size(); i++) {
						JSONObject questionObj = itemList.getJSONObject(i);
						contentSbd.append(questionObj.getString("seq")).append(".").append(questionObj.getString("question")).append("\n");
					}
					contentSbd.append(afterWord);
					msgModel.setMsgContent(contentSbd.toString());
				}
			}
			return;
		}

		//css工单信息
		String msgType = msgModel.getMsgType();
		JSONObject cssData = robotData.getJSONObject("cssData");
		if("cssData".equals(msgType)&&cssData!=null){
			JSONArray orderList = cssData.getJSONArray("list");
			if(orderList!=null && !orderList.isEmpty()){
				StringBuilder contentSbd = new StringBuilder(cssData.getString("title") + "\n");
				for (int i = 0; i < orderList.size(); i++) {
					JSONObject order = orderList.getJSONObject(i);
					contentSbd.append("服务单号：").append(order.getString("serviceOrderNo"))
							.append("，服务类型：").append(order.getString("serviceTypeName"))
							.append("，创建时间：").append(order.getString("contactTime")).append("\n");
					JSONArray userDemandList = order.getJSONArray("userDemandList");
					if(userDemandList!=null && !userDemandList.isEmpty()){
						contentSbd.append("		产品：").append("\n");
						for (int j = 0; j < userDemandList.size(); j++) {
							JSONObject userDemand = userDemandList.getJSONObject(j);
							contentSbd.append(userDemand.getString("brandName")+userDemand.getString("subAttribute4")).append("\n");
						}
					}
				}
				msgModel.setMsgContent(contentSbd.toString());
			}
		}
	}

}
