package com.yunqu.yc.mediagw.log;

import com.yunqu.yc.mediagw.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.calendar.EasyCalendar;

public class MediagwLogger {
	
	public static Logger getLogger(){
		EasyCalendar cal  = EasyCalendar.newInstance();
		String hours = cal.getTimeString().substring(0, 2);
		return LogEngine.getLogger("yc-mediagw_"+hours);
	}

	//大模型总结接口的日志
	public static Logger getSummaryLogger(){
		return LogEngine.getLogger("yc-mediasummary");
	}

	public static Logger getSatifyLog(){
		return LogEngine.getLogger("yc-mediagw-onlineser");
	}


	public static Logger getEventQueueErrorLogger(){
		return LogEngine.getLogger("yc-mediagw-EventQueueError");
	}

	public static Logger getCacheDataLogger(){
		return LogEngine.getLogger(Constants.APP_NAME+"-cacheData");
	}
	public static Logger getLongTxtLogger(){
		return LogEngine.getLogger(Constants.APP_NAME+"-longtxt");
	}

}
