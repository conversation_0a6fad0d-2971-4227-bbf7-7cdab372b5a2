package com.yunqu.yc.agent.servlet;

import java.io.IOException;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.List;
import java.util.Random;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;

import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.EntContext;
import com.yunqu.yc.agent.base.EventType;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.agent.genesys.util.RespResultEnum;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.GenesysLogger;
import com.yunqu.yc.agent.log.HeartbeatLogger;
import com.yunqu.yc.agent.log.ICCSLogger;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.mqclient.MediaProducerBroker;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.MediaAgentStateEnum;
import com.yunqu.yc.agent.msg.MediaRespCmdEnum;
import com.yunqu.yc.agent.msg.impl.v1.EventHandlerV1;
import com.yunqu.yc.agent.msg.model.RequestDataV1;
import com.yunqu.yc.agent.sbc.SBCRouter;
import com.yunqu.yc.sso.impl.YCUserPrincipal;

@WebServlet({ "/AgentEvent" })
public class AgentEventServlet extends EventBaseServlet {

	private static final long serialVersionUID = 2151657556079304871L;
	private static final String RESULT_SUCC = "succ";
	private static final String RESULT_FAIL = "fail";
	EventHandlerV1 eventMessage = (EventHandlerV1) EventFactory.getEventMessage("v1");
	
	public EasyResult actionForAgentLogin() throws ServletException, IOException {
		setResHeader();
		String cmdJsonString = getRequest().getParameter("cmdJson");

		JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
		
		if (cmdJson == null) {
			return getDataResult("SDKLogin", RESULT_FAIL, "签入失败，原因:cmdJson is null!", null);
		}

		// 坐席工号
		String agentId = cmdJson.getString("agentId");
		// 企业ID
		String entId = cmdJson.getString("entId");
		// 登录密码
		String token = cmdJson.getString("token");
		
		String productId = cmdJson.getString("productId");
		
		String loginKey = cmdJson.getString("loginKey");
			CcbarLogger.getLogger().info(cmdJson.toJSONString());
		if (StringUtils.isBlank(entId)) {
			return getDataResult("AgentLogin", RESULT_FAIL, "签入失败，原因:企业ID为空", null);
		}

		if (StringUtils.isBlank(agentId)) {
			return getDataResult("AgentLogin", RESULT_FAIL, "签入失败，原因:坐席工号为空", null);
		}

		if (StringUtils.isBlank(token)) {
			return getDataResult("AgentLogin", RESULT_FAIL, "签入失败，原因:登录token为空", null);
		}

		if (StringUtils.isBlank(productId)) {
			return getDataResult("AgentLogin", RESULT_FAIL, "签入失败，原因:productId为空", null);
		}

		if (StringUtils.isBlank(loginKey)) {
			return getDataResult("AgentLogin", RESULT_FAIL, "签入失败，原因:loginKey为空", null);
		}

		String httpSessionId = this.getRequest().getSession().getId();
		CcbarLogger.getLogger().info("getRequest().getSession().getId()->"+httpSessionId);

		//判断坐席是否已经签入全媒体或语音
		String _sessionId = this.cache.get("CCBAR_LOGIN_AGENT_" + this.getAgentId());
		if (StringUtils.isNotBlank(_sessionId)) {
			if (!httpSessionId.equals(_sessionId)) {
				return getDataResult("Login", RESULT_FAIL, "签入失败[403]，原因:坐席工号已签入", null);
			}
		}

		String sql = "select count(1)  from CC_BUSI_ORDER  where BUSI_ID = ? and  ENT_ID = ?";

		// 判断订购的产品是否存在
		try {
			if (!QueryFactory.getQuery(null).queryForExist(sql, new Object[] { productId, entId })) {
				CcbarLogger.getLogger().warn("<CCBAR-SDKLogin> Login error,cause: busiId[" + productId
						+ "] order not found!,loginInfo->" + cmdJsonString);
				return getDataResult("SDKLogin", RESULT_FAIL, "签入失败，产品订购信息[" + productId + "]不存在!", null);
			}
		} catch (Exception ex) {
			CcbarLogger.getLogger().error(ex, ex);
			return EasyResult.error(500, "系统处理请求失败，原因:" + ex.getMessage());
		}

		// sql = "select count(1) from CC_USER where USER_ACCT = ? and ENT_ID =
		// ? ";
		sql = "select count(1) from  CC_USER  where USER_ACCT = ? ";
		try {
			if (!QueryFactory.getQuery(null).queryForExist(sql, new Object[] { agentId })) {
				CcbarLogger.getLogger().warn("<CCBAR-SDKLogin> Login error,cause: Agent[" + agentId
						+ "] not exist!,loginInfo->" + cmdJsonString);
				return getDataResult("SDKLogin", RESULT_FAIL, "签入失败[404]，原因：坐席工号[" + agentId + "]不存在!", null);
			}
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("<CCBAR-SDKLogin> " + agentId + "登录失败[500]，原因：" + ex.getMessage(), ex);
			return EasyResult.error(500, "系统处理请求失败，原因:" + ex.getMessage());
		}
		
		
		YCUserPrincipal principal = getYCUserPrincipal();
		if (principal != null) {
			String _agentId = this.getAgentId();
			CcbarLogger.getLogger().info("actionForAgentLogin--1->_agentId["+_agentId+"],agentId["+agentId+"]");
			// 如果相同的，则不做登出处理。
			if (!agentId.equals(_agentId)) {
				try {
					this.getRequest().logout();
					Thread.sleep(2000);
				} catch (Exception ex) {
					CcbarLogger.getLogger().error(ex, ex);
				}
			}
		}
		
		principal = getYCUserPrincipal();
		if (principal == null) {
			CcbarLogger.getLogger().info("actionForAgentLogin-2-->agentId["+agentId+"]");
			// 执行平台的sso登录
			try {
				this.getRequest().login(agentId, token);
			} catch (Exception ex) {
				CcbarLogger.getLogger().error("<CCBAR-SDKLogin> " + agentId + " SSO登录失败[403]，原因：" + ex.getMessage(),ex);
				return getDataResult("SDKLogin", RESULT_FAIL, "签入失败[SSO:403]，原因：坐席工号或密码错误", null);
			}
		}
		principal = getYCUserPrincipal();
		if (principal == null) {
			CcbarLogger.getLogger().warn("<" + agentId + ">执行SSO登录后，获取principal对象为空，登录失败,登录信息->" + cmdJsonString);
			return getDataResult("CcbarLogin", RESULT_FAIL, "签入失败[SSO:404]，原因：坐席工号或密码错误", null);
		}
		CcbarLogger.getLogger().info("actionForAgentLogin-3-->agentId["+principal.getLoginAcct()+"]:"+principal);
		principal.setBusiId(productId);
		//重新登录，清空待执行事件
		this.eventMessage.getAgentEvent().clear(this.getAgentId());
		GenesysLogger.getLogger().info("[clearAgentEvent]->"+this.getAgentId());
		JSONObject config = new JSONObject();
		config.put("area", Constants.getArea()); //取值 SD 顺德 HF 合肥
		return getDataResult("AgentLogin", RESULT_SUCC, RESULT_SUCC, config.toJSONString());
		
	}
	
	public EasyResult actionForSDKLogin() throws ServletException, IOException {
		setResHeader();
		String cmdJsonString = getRequest().getParameter("cmdJson");
		CcbarLogger.getLogger().info("<CCBAR-SDKLogin> << " + cmdJsonString);
		YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
		if (userPrincipal == null) {
			return this.getSessionTimeoutResult();
		}
		CcbarLogger.getLogger().info("actionForSDKLogin-5-->agentId["+userPrincipal.getLoginAcct()+"]:"+userPrincipal);

		// 执行iccs坐席签入
		EasyResult result = null;
		try {
			result = actionForLogin();
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("<" + this.getAgentId() + "> 登录失败，原因：" + ex.getMessage(), ex);
			return getDataResult("SDKLogin", RESULT_FAIL, "签入失败，原因:" + ex.getMessage(), null);
		}

		// 如果坐席签入失败，则直接执行sso的logout的操作。
		JSONObject jsonObject = JSONObject.parseObject((String) result.getData());
		if (jsonObject == null) {
			return getDataResult("SDKLogin", RESULT_FAIL, "签入失败，原因：坐席工号或密码错误", null);
		}
		if (RESULT_FAIL.equalsIgnoreCase(jsonObject.getString("code"))) {
			this.getRequest().logout();
		}
		
		CcbarLogger.getLogger().info("<" + this.getAgentId() + "> 登录结果->" + result.toJSONString());
		return result;
	}

	/**
	 * 
	 * URL:http://*************:9060/AgentEvent?action=AgentProxy
	 * post内容：command=login|logout|getState&phoneNum:话机号码
	 * 
	 * 返回：result=login|logout ,login代表登录状态 logout代表目前坐席状态为登录状态。
	 * 
	 * @return
	 * @throws Exception
	 */

	/**
	 * 处理坐席的签入
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForVoiceLogin() throws Exception {
		setResHeader();
		AgentModel agent = AgentInfos.getAgentInfo(this.getAgentId());
		String cmdJsonString = getRequest().getParameter("cmdJson");
		
		ICCSLogger.getLogger().info("<CCBAR-Login><" + this.getAgentId() + "> << " + cmdJsonString);
		
		JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
		EntContext entContext = EntContext.getContext(this.getResEntId());

		String displayForAgent = "";

		// 获取登录传入的号码，如果这个号码不存在，则系统自动获取一个号码
		String phoneNum = cmdJson.getString("phoneNum");
		if (StringUtils.isBlank(phoneNum)) {
			String sql = "select PHONE_NUM from CC_USER  where USER_ACCT = ? ";
			try {
				phoneNum = this.getQuery().queryForString(sql, new Object[] { this.getAgentId() });
			} catch (SQLException ex) {
				CcbarLogger.getLogger().error("LoginAgentServlet.actionForLogin() error,cause:" + ex.getMessage(), ex);
			}
		}
		String _phoneNum = phoneNum;

		JSONObject data = new JSONObject();
		String sql = "";

		displayForAgent = this.getDispalyForAgent();
		// 如果话机号码少于7位，则生成真实的话机号码。
		if (_phoneNum.length() < 7) {
			_phoneNum = this.getLocalPhoneNum(_phoneNum);
		}
		YCUserPrincipal userPrincipal = getYCUserPrincipal();
		if (userPrincipal == null) {
			return this.getSessionTimeoutResult();
		}
		// 判断是否存在坐席工号，避免admin进行登录。
		if (StringUtils.isBlank(userPrincipal.getAgentPhone())) {
			return getDataResult("Login", RESULT_FAIL, "签入失败，原因:坐席[" + this.getAgentId() + "]未分配坐席工号", null);
		}

		// 判断签入的话机号码是否存在
		sql = "select PHONE_TYPE,PHONE_PWD,MAC_ADDR from CC_PHONE where  ENT_ID = ?  and PHONE_NUM = ? ";
		EasyRow row = getQuery().queryForRow(sql,
				new Object[] { this.getResEntId(), (phoneNum.length() < 7) ? _phoneNum : phoneNum });
		if (row == null) {
			return getDataResult("Login", RESULT_FAIL, "签入失败，原因:话机号码[" + phoneNum + "]不存在", null);
		}
		
		
		// 话机类型，１、硬话机 ２、SIP硬话机 ３、SIP软话机 4、外线 ,只有SIP软话机才拉起
		if ("3".equals(row.getColumnValue("PHONE_TYPE"))) {
			data.put("sbcAddr", SBCRouter.getLoginAddr(this.getResEntId(), _phoneNum, row.getColumnValue("PHONE_PWD"),
					row.getColumnValue("MAC_ADDR")));
		} else {
			data.put("sbcAddr", "");
		}
		data.put("phoneType", row.getColumnValue("PHONE_TYPE"));

		sql = "select OUTBOUND,INBOUND from " + entContext.getTableName("CC_BUSI_USER")
				+ "  where USER_ID = ? and BUSI_ORDER_ID = ?";
		data.put("outbound", "on");
		data.put("inbound", "on");
		try {
			row = this.getQuery().queryForRow(sql, new Object[] { this.getRemoteUser(), this.getBusiOrderId() });
			if (row != null) {
				if (!"1".equals(row.getColumnValue("OUTBOUND"))) {
					data.put("outbound", "off");
				}
				if (!"1".equals(row.getColumnValue("INBOUND"))) {
					data.put("inbound", "off");
				}
			}
		} catch (Exception ex) {
			CcbarLogger.getLogger().error(ex, ex);
		}

		// //如果是外线，则需要加话机前缀，根据
		// if("4".equals(row.getColumnValue("PHONE_TYPE"))){
		// if(_phoneNum.length()>=8) _phoneNum =
		// HCodeUtil.getReallyCalled(this.getResEntId(),displayForAgent,_phoneNum);
		// }

		try {

			RequestDataV1 requestDataModel = new RequestDataV1();
			requestDataModel.setEntId(this.getEntId());
			requestDataModel.setAgentId(getRemoteUser());
			requestDataModel.setTimestamp(System.currentTimeMillis() + "");
			requestDataModel.setVersion("v1");
			requestDataModel.setBizSessionId(getRequest().getSession().getId());
			requestDataModel.setAgentId(userPrincipal.getLoginAcct());
			requestDataModel.setCommand("cmdLogin");
			
//			String logoutFlag =  cache.get("CCBAR_LOGOUT_AGENT_"+this.getAgentId());
//			//logoutFlag为false，则代表目前坐席还没有正常登出。
//			if(StringUtils.isNotBlank(logoutFlag)){
//				//cache.delete("CCBAR_LOGOUT_AGENT_"+this.getAgentId());
//				requestDataModel.setCommand("cmdQueryAgentInfo");
//				requestDataModel.setData(new JSONObject());
//				this.eventMessage.handleMessage(requestDataModel);
//				Thread.sleep(1000l);
//			}
//			
//			requestDataModel.setCommand("cmdLogin");

			JSONObject eventData = new JSONObject();
			eventData.put("station", _phoneNum);
			eventData.put("displayForAgent", displayForAgent);
			eventData.put("skillId", agent.getSkillGroupId());
			eventData.put("keepAlive", "1");
			eventData.put("readyMode", "ready");
			eventData.put("voiceSwitch", agent.getVoiceSwitch());
			eventData.put("multiMediaSwitch", agent.getMultiMediaSwitch());
			// 20180607 fix by tzc 只有在呼叫中心场景下才需要设置呼入呼出模式。
			
			if (this.getBusiId().equals("003")) {
				if ("on".equals(data.getString("outbound"))) {
					eventData.put("workMode", "outbound");
				} else {
					eventData.put("workMode", "inbound");
				}
			} else {
				eventData.put("workMode", "all");
			}
			eventData.put("ipAddress", this.getRequest().getRemoteAddr());
			eventData.put("workReadyFlag", agent.getWorkReadyFalg());
			eventData.put("workReadyTimeout", agent.getWorkReadyTime());
			eventData.put("userData", "{}");
			// eventData.put("state", "2"); // 登录后初始工作方式 2 ready：就绪 1
			// notReady：暂停
			eventData.put("notifyUrl", "ccbar");
			requestDataModel.setData(eventData);
			// 处理请求信息
			
			this.eventMessage.handleMessage(requestDataModel);
			AgentInfos.putLogin(this.getAgentId(), requestDataModel);

		} catch (Exception ex) {
			ICCSLogger.getLogger().error("LoginAgentServlet.actionForLogin() error,cause:" + ex.getMessage(), ex);
			return getDataResult("Login", RESULT_FAIL, "签入失败，原因:" + ex.getMessage(), null);
		}
		return getDataResult("Login", RESULT_SUCC, RESULT_SUCC, data);
	}
	
	
	/**
	 * 设置自动应答
	 * @return
	 * @throws Exception
	 */
	public EasyResult actionForAutoAnswer() throws Exception {
		setResHeader();
		if (this.getYCUserPrincipal() == null) {
			return this.getSessionTimeoutResult();
		}
		String cmdJsonString = getRequest().getParameter("cmdJson");
		JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);

		if (cmdJson == null) {
			return getDataResult("autoAnswer", RESULT_FAIL, "操作失败，原因:cmdJson is null!", null);
		}
		
		AgentModel agentModel = AgentInfos.getAgentInfo(this.getAgentId());
		
		String autoAnswer = cmdJson.getString("autoAnswer");
		if("false".equalsIgnoreCase(autoAnswer)){
			agentModel.setAutoAnswer(false);
		}else{
			agentModel.setAutoAnswer(true);
		}
		
		JSONObject data = new JSONObject();
		return getDataResult("autoAnswer", RESULT_SUCC, RESULT_SUCC, data);
		
	}
	
	/**
	 * 处理坐席的签入
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForWorkMode() throws Exception {
		setResHeader();
		if (this.getYCUserPrincipal() == null) {
			return this.getSessionTimeoutResult();
		}

		String cmdJsonString = getRequest().getParameter("cmdJson");

		CcbarLogger.getLogger().info("<CCBAR-WorkMode><" + this.getAgentId() + "> << " + cmdJsonString);
		
		AgentModel agent = AgentInfos.getAgentInfo(this.getAgentId());
		

		// CcbarLogger.getLogger().info("LoginAgentServlet.actionForMakecall(" +
		// getYCUserPrincipal().getLoginAcct() + ")->:" + cmdJsonString);
		JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);

		if (cmdJson == null) {
			return getDataResult("WorkMode", RESULT_FAIL, "操作失败，原因:cmdJson is null!", null);
		}
		// inbound呼入 outbound呼出 pdsbound智能外呼
		String workMode = cmdJson.getString("workMode");

		JSONObject data = new JSONObject();

		try {

			RequestDataV1 requestDataModel = new RequestDataV1();
			requestDataModel.setEntId(this.getEntId());
			requestDataModel.setAgentId(this.getAgentId());
			requestDataModel.setTimestamp(System.currentTimeMillis() + "");
			requestDataModel.setVersion("v1");
			requestDataModel.setBizSessionId(getRequest().getSession().getId());
			requestDataModel.setAgentId(this.getAgentId());
			requestDataModel.setCommand("cmdChangeWorkMode");
			JSONObject eventData = new JSONObject();
			eventData.put("workMode", workMode);
			requestDataModel.setData(eventData);

			// 处理请求信息
			this.eventMessage.handleMessage(requestDataModel);

		} catch (Exception ex) {
			CcbarLogger.getLogger().error("LoginAgentServlet.actionForWorkMode() error,cause:" + ex.getMessage(), ex);
			return getDataResult("WorkMode", RESULT_FAIL, "签入失败，原因:" + ex.getMessage(), null);
		}
		return getDataResult("WorkMode", RESULT_SUCC, RESULT_SUCC, data);
	}

	/**
	 * 获得当前坐席所在的技能组内的坐席列表
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForAgentList() throws ServletException, IOException {
		setResHeader();
		YCUserPrincipal userPrincipal = getYCUserPrincipal();
		if (userPrincipal == null) {
			return this.getSessionTimeoutResult();
		}

		String cmdJsonString = getRequest().getParameter("cmdJson");

		CcbarLogger.getLogger().info("<CCBAR-AgentList><" + this.getAgentId() + "> << " + cmdJsonString);

		// CcbarLogger.getLogger().info("LoginAgentServlet.actionForMakecall(" +
		// getYCUserPrincipal().getLoginAcct() + ")->:" + cmdJsonString);
		JSONObject cmdJson = null;

		if (StringUtils.isNotBlank(cmdJsonString)) {
			cmdJson = JSONObject.parseObject(cmdJsonString);
		}

		if (cmdJson == null)
			cmdJson = new JSONObject();

		String skillGroupId = cmdJson.getString("skillGroupId");

		if (StringUtils.isBlank(skillGroupId)) {
			skillGroupId = (String)userPrincipal.getAttribute("skillGroupId");
		}
		
		AgentModel agentModel = AgentInfos.getAgentInfo(this.getAgentId());
//		if("on".equalsIgnoreCase(agentModel.getMultiMediaSwitch())){
//			return this.getDataResult("AgentList", RESULT_SUCC, RESULT_SUCC, this.getMediaAgentList(skillGroupId));
//		}
		
		// 活动当前可以咨询和转移的技能组和坐席组
		JSONObject data = new JSONObject();
		String sql = "";
		
		if("on".equalsIgnoreCase(agentModel.getVoiceSwitch())){  //如果是语音的则，不显示全媒体的技能组
			sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME from " + this.getTableName("CC_SKILL_GROUP")
					+ " where AGENT_COUNT>0  and ENT_ID = ?  and BUSI_ORDER_ID = ? and SKILL_GROUP_ID not in "
					+ " (select distinct SKILL_GROUP_ID from "+this.getTableName("CC_SKILL_GROUP_CHANNEL")+") order by IDX_ORDER ";
		}else{
			sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME from " + this.getTableName("CC_SKILL_GROUP")
				+ " where AGENT_COUNT>0  and ENT_ID = ?  and BUSI_ORDER_ID = ? and SKILL_GROUP_ID  in "
					+ " (select distinct SKILL_GROUP_ID from "+this.getTableName("CC_SKILL_GROUP_CHANNEL")+") order by IDX_ORDER ";
		}

		try {
			List<JSONObject> groups = this.getQuery().queryForList(sql,
					new Object[] { this.getEntId(), this.getBusiOrderId() }, new JSONMapperImpl());
			data.put("groups", groups);

			CcbarLogger.getLogger().info("<CCBAR-AgentList><Group SQL> << " + sql+","+this.getEntId()+","+this.getBusiOrderId()+","+data);
			
			sql = "select t1.AGENT_ID, t4.AGENT_NAME,t4.AGENT_PHONE  from  " + Constants.getStatSchema()+ ".CC_RPT_AGENT_MONITOR  t1 , " + this.getTableName("cc_skill_group_user") + " t2 , cc_user t3 "
					+ "	,"+this.getTableName("cc_busi_user") +" t4 where  t4.busi_order_id = ? and  t4.user_id = t2.user_id and  t1.AGENT_STATE in ('空闲','可呼') and  t1.agent_id = t3.user_acct  and  t3.user_id = t2.user_id  and  t2.skill_group_id = ?  and "
					+ "  t1.AGENT_ID is not null and t1.AGENT_ID <> ? and  t3.ENT_ID = ? and t1.LOGON_FLAG = 1  order by t4.AGENT_NAME ";

			List<JSONObject> agents = QueryFactory.getStatQuery().queryForList(sql,
					new Object[] { this.getBusiOrderId(), skillGroupId, this.getAgentId(), this.getEntId() }, new JSONMapperImpl());

			data.put("agents", agents);
			
			CcbarLogger.getLogger().info("<CCBAR-AgentList><Group SQL> << " + sql+","+this.getBusiOrderId()+","+skillGroupId+","+getAgentId()+","+getEntId()+","+data);
			
			return this.getDataResult("AgentList", RESULT_SUCC, RESULT_SUCC, data);
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("查询坐席列表失败，原因：" + ex.getMessage(), ex);
			return this.getDataResult("AgentList", RESULT_FAIL, "查询坐席列表失败，原因：" + ex.getMessage(), null);
		}
	}

	/**
	 * 执行查询坐席性别的操作
	 *
	 * 此方法用于处理查询坐席性别的请求它从请求参数中获取JSON命令，
	 * 解析后查询数据库中对应坐席号的性别信息，并返回查询结果
	 *
	 * @return EasyResult对象，包含查询结果和状态信息
	 * @throws ServletException 如果Servlet操作失败
	 * @throws IOException 如果IO操作失败
	 */
	public EasyResult actionForAgentGender() throws ServletException, IOException {
		setResHeader();
		String cmdJsonString = getRequest().getParameter("cmdJson");

		CcbarLogger.getLogger().info("<CCBAR-AgentGender><" + this.getAgentId() + "> << " + cmdJsonString);

		// CcbarLogger.getLogger().info("LoginAgentServlet.actionForMakecall(" +
		// getYCUserPrincipal().getLoginAcct() + ")->:" + cmdJsonString);
		JSONObject cmdJson = null;

		if (StringUtils.isNotBlank(cmdJsonString)) {
			cmdJson = JSONObject.parseObject(cmdJsonString);
		}

		if (cmdJson == null)
			cmdJson = new JSONObject();

		String agentPhone = cmdJson.getString("agentPhone");

		if (StringUtils.isBlank(agentPhone)) {
			return getDataResult("AgentGender", RESULT_FAIL, "操作失败，原因:agentPhone is null!", null);
		}

		CcbarLogger.getLogger().info("<CCBAR-AgentGender><" + this.getAgentId() + "> << 查询坐席性别，查询工号: " + agentPhone);

		try {
			String sql = "SELECT SEX FROM " + Constants.getYwdbSchema() + ".C_YG_EMPLOYEE WHERE WORK_NO = ?";
			String gender = getQuery().queryForString(sql, new Object[] { agentPhone});

			if (gender == null) {
				return this.getDataResult("AgentGender", RESULT_FAIL, "坐席性别查询失败，未找到相关记录", null);
			}

			JSONObject data = new JSONObject();
			data.put("gender", gender);
			return this.getDataResult("AgentGender", RESULT_SUCC, RESULT_SUCC, data);
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("查询坐席性别失败，原因：" + ex.getMessage(), ex);
			return this.getDataResult("AgentGender", RESULT_FAIL, "查询坐席性别失败，原因：" + ex.getMessage(), null);
		}
	}

//	public List<JSONObject> getMediaAgentList(String skillGroupId){
//		String groupStrings = cache.get("mediacenter_monitor_group_"+skillGroupId);
//		List<JSONObject> agents = new  ArrayList<JSONObject>();
//		if(StringUtils.isBlank(groupStrings)){
//			return agents;
//		}
//		JSONObject groupInfo = JSONObject.parseObject(groupStrings);
//		JSONArray  users = groupInfo.getJSONArray("onlineAgents");
//		for(int i =  0 ;i <users.size();i++){
//			JSONObject agent = users.getJSONObject(i);
//			if(!agent.getBooleanValue("isReady")){
//				continue;
//			}
//			JSONObject _agent = new JSONObject();
//			_agent.put("AGENT_ID", agent.getString("agentId"));
//			_agent.put("AGENT_NAME", agent.getString("agentName"));
//			agents.add(_agent);
//		}
//		return agents;
//	}

	/**
	 * 获得来显号码列表
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForCallerList() throws ServletException, IOException {
		setResHeader();
		YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
		if (userPrincipal == null) {
			return this.getSessionTimeoutResult();
		}
		JSONObject data = new JSONObject();
		List<String> callers = this.getManualCallerList();
		data.put("callers", callers);
		return this.getDataResult("CallerList", RESULT_SUCC, RESULT_SUCC, data);
	}

	/**
	 * 获得手工外呼来显号码列表
	 * 
	 * @return
	 */
	private List<String> getManualCallerList() {
		YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
		List<String> callers = new ArrayList<String>();
		String sql = "select t1.PREFIX_NUM,t1.PREFIX_NUM_RULE from " + this.getTableName("CC_SKILL_GROUP") + " t1 , "
				+ this.getTableName("CC_SKILL_GROUP_USER")
				+ " t2  where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t2.USER_ID = ?  and t1.BUSI_ORDER_ID = ? and t1.ENT_ID = ?";
		try {
			JSONObject groupInfo = this.getQuery().queryForRow(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() },
					new JSONMapperImpl());
			// 系统自动选择
			if (groupInfo.getIntValue("PREFIX_NUM_RULE") == 0) {
				String caller = this.getAgentNextCaller("");
				callers.add(caller);
				return callers;

			}
			String num = this.getQuery().queryForString(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() });
			if (!StringUtils.isBlank(groupInfo.getString("PREFIX_NUM"))) {
				callers.add(num);
			}
			sql = "select t2.PREFIX_NUM from " + this.getTableName("CC_SKILL_GROUP")
					+ " t1 , CC_PREFIX_GROUP_PREFIX t2 ," + this.getTableName("CC_SKILL_GROUP_USER") + " t3  "
					+ " where t1.PREFIX_GROUP_ID = t2.PREFIX_GROUP_ID  and t1.SKILL_GROUP_ID = t3.SKILL_GROUP_ID and t3.USER_ID = ?  and t1.BUSI_ORDER_ID = ? and t1.ENT_ID = ?";
			List<EasyRow> rows = this.getQuery().queryForList(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() });
			for (EasyRow row : rows) {
				callers.add(row.getColumnValue("PREFIX_NUM"));
			}
		} catch (Exception ex) {
			this.getLogger().error("getAgentNextCaller error  -> agentId:" + userPrincipal.getUserId()
					+ ",entId:" + this.getEntId() + ",cause:" + ex.getMessage(), ex);
		}
		return callers;
	}

	/**
	 * 获得当前呼叫任务的来显电话列表
	 * 
	 * @param taskKey
	 * @param entId
	 * @param taskId
	 * @return
	 */
	private String getAgentNextCaller(String caller) {
		List<String> callers = this.getCallerList();
		// 如果界面上传的caller参数不为空的时候，则直接采用caller的参数。
		if (StringUtils.isNotBlank(caller)) {
			// 这里要增加判断，页面上传的号码必须是系统配置的号码，防止漏洞。
			if (!callers.contains(caller))
				return "";
			return caller;
		}

		if (callers.size() == 0)
			return "";
		Random r = new Random();
		return callers.get(r.nextInt(callers.size()));
	}

	/**
	 * 获得来显号码列表
	 * 
	 * @return
	 */
	private List<String> getCallerList() {
		YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
		List<String> callers = new ArrayList<String>();
		String sql = "select t1.PREFIX_NUM,t1.PREFIX_NUM_RULE from " + this.getTableName("CC_SKILL_GROUP") + " t1 , "
				+ this.getTableName("CC_SKILL_GROUP_USER")
				+ " t2  where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and t2.USER_ID = ?  and t1.BUSI_ORDER_ID = ? and t1.ENT_ID = ? ";
		try {
			JSONObject groupInfo = this.getQuery().queryForRow(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() },
					new JSONMapperImpl());

			String num = this.getQuery().queryForString(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() });
			if (!StringUtils.isBlank(groupInfo.getString("PREFIX_NUM"))) {
				callers.add(num);
			}
			sql = "select t2.PREFIX_NUM from " + this.getTableName("CC_SKILL_GROUP")
					+ " t1 , CC_PREFIX_GROUP_PREFIX t2 ," + this.getTableName("CC_SKILL_GROUP_USER") + " t3  "
					+ " where t1.PREFIX_GROUP_ID = t2.PREFIX_GROUP_ID  and t1.SKILL_GROUP_ID = t3.SKILL_GROUP_ID and t3.USER_ID = ? and t1.BUSI_ORDER_ID = ? and t1.ENT_ID = ?";
			List<EasyRow> rows = this.getQuery().queryForList(sql,
					new Object[] { userPrincipal.getUserId(), this.getBusiOrderId(), this.getEntId() });
			for (EasyRow row : rows) {
				callers.add(row.getColumnValue("PREFIX_NUM"));
			}
		} catch (Exception ex) {
			this.getLogger().error("getAgentNextCaller error  -> agentId:" + userPrincipal.getUserId()
					+ ",entId:" + this.getEntId() + ",cause:" + ex.getMessage(), ex);
		}
		return callers;
	}

	/**
	 * 处理坐席的事件请求
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForEvent() throws ServletException, IOException {
		setResHeader();
		YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
		if (userPrincipal == null) {
			return this.getSessionTimeoutResult();
		}
		EasyResult result = EasyResult.ok();

		
		String cmdJson = getRequest().getParameter("cmdJson");
		CcbarLogger.getLogger().info("<CCBAR-Event><" + this.getAgentId() + "> << " + cmdJson);

		JSONObject cmdObject = JSONObject.parseObject(cmdJson);
		if (cmdObject == null) {
			result.setSuccess("fail", "处理失败，原因：cmdJson数据请求格式错误！");
			return result;
		}
		String cmd = cmdObject.getString("messageId");
		RequestDataV1 requestDataModel = new RequestDataV1();

		String command = getPetraCmd(cmd);
		
		if("cmdForceLogout".equalsIgnoreCase(command)){
			try {
				requestDataModel.setCommand("cmdLogout");//收到强制签出时，发送cmdLogout
				String entId = this.getEntId();
				if(StringUtils.isBlank(entId)){
					entId =  "1000";
				}
				//强制签出语音坐席begin
				String agentId = cmdObject.getString("agentId");
				requestDataModel.setEntId(entId);
				requestDataModel.setAgentId(agentId);
				requestDataModel.setTimestamp(System.currentTimeMillis() + "");
				requestDataModel.setVersion("v1");
				requestDataModel.setData(new JSONObject());
				requestDataModel.setBizSessionId(getRequest().getSession().getId());
				this.eventMessage.handleMessage(requestDataModel);
				//强制签出语音坐席end

				//强制签出全媒体坐席，需要通知所有服务器处理该事件
				AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
				JSONObject reqStateObj = MediaAgentStateEnum.LOGOFF.getReq(agentModel);
				this.eventMessage.getPortalQueue().put(reqStateObj.getString("messageId"),agentId,reqStateObj.toJSONString());
				JSONObject respCmdObj = MediaRespCmdEnum.Logout.getReq(agentModel, RespResultEnum.SUCCESS);
				this.eventMessage.getPortalQueue().put(respCmdObj.getString("messageId"),agentId,respCmdObj.toJSONString());

				result.setSuccess(RESULT_SUCC, RESULT_SUCC);
			} catch (Exception ex) {
				CcbarLogger.getLogger().error("actionForEvent() error,cause:" + ex.getMessage(), ex);
				result.setSuccess(RESULT_FAIL, "操作失败,原因:" + ex.getMessage());
			}
			return result;
		}
		String agentId=this.getAgentId();
		try {
			String monitorAgentId = (String) JSONPath.eval(cmdObject, "$.userData.agentId");
			if(StringUtils.isNotBlank(monitorAgentId)){//班长监控操作
				agentId = monitorAgentId;
				CcbarLogger.getLogger().info(this.getAgentId()+"在监控页面调整" + monitorAgentId+" 状态:"+cmdJson);
			}
		}catch (Exception e){
			CcbarLogger.getLogger().error("userDaTa 据为空" + e.getMessage(), e);
		}

		EntContext entContext = EntContext.getContext(this.getEntId());

		AgentModel agentModel = AgentInfos.getAgentInfo(agentId);


		// 如果是挂机时间 并且是用户主动呼入的
		if ("cmdClearCall".equalsIgnoreCase(command)) {

			JSONObject iccsObject = agentModel.getIccsObject();
			// 只有在通话状态中才发起满意度调查
			if (iccsObject != null && "TALK".equalsIgnoreCase(cmdObject.getString("status"))) {
				String createCause = iccsObject.getString("createCause");
				// 如果是呼入的场景,进入满意度调查
				if ("1".equals(createCause) || "2".equals(createCause) || "3".equals(createCause)
						|| "5".equals(createCause)) {
					String sql = "select VOX_PATH from CC_ENT_VOX  where ENT_ID = ? and VOX_TYPE = 4";
					String voxPath = null;
					try {
						voxPath = this.getQuery().queryForString(sql, new Object[] { this.getEntId() });
					} catch (SQLException ex) {
						CcbarLogger.getLogger().error(ex, ex);
					}

					// 如果企业配置了满意度调查的语音，则执行满意度调查处理。
					if (StringUtils.isNotBlank(voxPath)) {
						JSONObject userData = iccsObject.getJSONObject("userData");
						if (userData == null)
							userData = new JSONObject();
						command = "cmdTransferCall";
						cmdObject.put("callType", "2"); // 1:呼叫座席 2:呼叫IVR 3:呼叫外线
														// 4:呼叫技能组
						cmdObject.put("called", entContext.getSatisfPrefix() + entContext.getResEntId()); // 1:被转移坐席工号
																											// 2:被转移Ivr字冠
																											// 3:被转移客户电话
																											// 4:被转移技能组ID
						cmdObject.put("displayNumber", ""); // 1:呼叫座席 2:呼叫IVR
															// 3:呼叫外线 4:呼叫技能组
						JSONObject userDataObject = new JSONObject();
						userDataObject.put("callbackUrl", entContext.getMarsUrl() + "/yc-api/callback");
						userDataObject.put("command", "respSatisf"); // 满意度调查响应
						userDataObject.put("entId", this.getEntId());
						// userDataObject.put("agentId", this.getAgentId());
						userDataObject.put("callSerialId", userData.getString("callSerialId"));
						userDataObject.put("busiOrderId", userData.getString("busiOrderId"));
						userDataObject.put("voxPath", voxPath);
						cmdObject.put("userData", userDataObject.toJSONString());
					}
				} else if ("6".equals(createCause) || "8".equals(createCause)) { // 人工外呼
																					// 和
																					// 智能外呼的场景下
					JSONObject userData = iccsObject.getJSONObject("userData");
					try {
						String taskId = userData.getString("taskId");
						if (StringUtils.isNotBlank(taskId)) {
							String sql = "select ORDER_PROD_ID,IVR_ORDER from " + this.getTableName("CC_TASK")
									+ " where TASK_ID = ? ";
							JSONObject orderInfo = this.getQuery().queryForRow(sql, new String[] { taskId },
									new JSONMapperImpl());
							if (orderInfo != null) {
								String orderProdId = orderInfo.getString("ORDER_PROD_ID");
								String ivrOrder = orderInfo.getString("IVR_ORDER");
								// 任务设定了IVR订购，并且指定了crm产品
								if (StringUtils.isNotBlank(orderProdId) && StringUtils.isNotBlank(ivrOrder)) {
									JSONObject userDataObject = new JSONObject();
									userDataObject.put("callbackUrl", entContext.getMarsUrl() + "/yc-api/callback");
									userDataObject.put("command", "respIVROrder"); // 满意度调查响应
									userDataObject.put("entId", this.getEntId());
									userDataObject.put("agentId", this.getAgentId());
									userDataObject.put("called", iccsObject.getString("called"));
									userDataObject.put("caller", iccsObject.getString("caller"));
									userDataObject.put("callSerialId", userData.getString("callSerialId"));
									userDataObject.put("busiOrderId", userData.getString("busiOrderId"));
									userDataObject.put("taskId", userData.getString("taskId"));
									userDataObject.put("objId", userData.getString("objId"));
									sql = "select PROD_NAME,FEE_TYPE,FEE,IVR_PREFIX,CRM_PROD_ID from "
											+ this.getTableName("CC_BD_PROD")
											+ " where PROD_STATE = 0 and  PROD_ID = ?";
									JSONObject prodInfo = this.getQuery().queryForRow(sql, new Object[] { orderProdId },
											new JSONMapperImpl());
									if (prodInfo != null) {
										command = "cmdTransferCall";
										cmdObject.put("called",
												entContext.getIvrOrderPrefix() + entContext.getResEntId());
										userDataObject.put("prodName", prodInfo.getString("PROD_NAME"));
										userDataObject.put("feeType", prodInfo.getString("FEE_TYPE"));
										userDataObject.put("fee", prodInfo.getString("FEE"));
										userDataObject.put("crmProdId", prodInfo.getString("CRM_PROD_ID"));
										cmdObject.put("userData", userDataObject.toJSONString());
										cmdObject.put("callType", "2"); // 1:呼叫座席
																		// 2:呼叫IVR
																		// 3:呼叫外线
																		// 4:呼叫技能组
									}

								}
							}
						}
					} catch (Exception ex) {
						CcbarLogger.getLogger().error("ivr order error,cause:" + ex.getMessage(), ex);
					}

				}
			}
		}

		if ("cmdAnswerCall".equalsIgnoreCase(command)) {

			String sql = "select VOX_PATH from CC_ENT_VOX  where ENT_ID = ? and VOX_TYPE = 6";
			String voxPath = null;
			try {
				voxPath = this.getQuery().queryForString(sql, new Object[] { this.getEntId() });
			} catch (SQLException ex) {
				CcbarLogger.getLogger().error(ex, ex);
			}

			JSONObject userDataObject = new JSONObject();
			userDataObject.put("callSerialId", RandomKit.uniqueStr());
			// 设置当前业务订购ID
			userDataObject.put("busiOrderId", this.getBusiOrderId());
			userDataObject.put("busiId", this.getBusiId());
			userDataObject.put("custTempId", agentModel.getCustTempId());
			if (StringUtils.isNotBlank(voxPath)) {
				userDataObject.put("playAgentFile", voxPath);
			}
			cmdObject.put("userData", userDataObject.toJSONString());
			// this.setAnswerCall();
		}

		//全媒体事件
		if("on".equalsIgnoreCase(agentModel.getMultiMediaSwitch())){
			// 全媒体坐席置忙，设置置忙原因
			if ("cmdNotReady".equalsIgnoreCase(command)) {
				agentModel.setNotReadyReasonId(cmdObject.getString("busyType"));
				cmdObject.remove("busyType");
				MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(agentModel.getEntId(), agentId, "cmdNotReady"));
				if (this.checkCommandResult(agentId, "cmdNotReady")) {
					this.eventMessage.getPortalQueue().put(MediaAgentStateEnum.BUSY.getReq(agentModel).toJSONString());
				} else {
					return EasyResult.error(500, "置忙失败！");
				}
			}

			//全媒体坐席置闲，清理置忙原因
			if ("cmdReady".equalsIgnoreCase(command)) {
				agentModel.setNotReadyReasonId("");
				MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(agentModel.getEntId(), agentId, "cmdReady"));
				if (this.checkCommandResult(agentId, "cmdReady")) {
					this.eventMessage.getPortalQueue().put(MediaAgentStateEnum.IDLE.getReq(agentModel).toJSONString());
				} else {
					return EasyResult.error(500, "置闲失败！");
				}
				// this.eventMessage.getPortalQueue().put(MediaRespCmdEnum.NotReady.getReq(agentModel,RespResultEnum.SUCCESS).toJSONString());
				return result;
			}

			if ("cmdLogout".equalsIgnoreCase(command)) {
				//全媒体坐席 签出
				MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(agentModel.getEntId(), agentId, "cmdLogout"));
				String commandResult = this.getCommandResult(agentId, "cmdLogout");
				//目前有在线坐席没有关闭，不允许签出。
				if("001".equals(commandResult)){
					return EasyResult.error(500, "当前还有待处理用户，请处理完成再签出！");
				}
				if ("000".equals(commandResult)) {
					MediaCacheUtil.delete(Constants.BROKER_AGENT_NAME+agentId);
					cache.delete("CCBAR_LOGIN_AGENT_"+agentId );
					this.eventMessage.getPortalQueue().put(MediaRespCmdEnum.Logout.getReq(agentModel, RespResultEnum.SUCCESS).toJSONString());
					this.eventMessage.getPortalQueue().put(MediaAgentStateEnum.LOGOFF.getReq(agentModel).toJSONString());
				} else {
					return EasyResult.error(500, "签出失败！");
				}
				return result;
			}
		}

		//语音坐席事件
		if("on".equals(agentModel.getVoiceSwitch())){
			if ("cmdNotReady".equalsIgnoreCase(command)) {
				agentModel.setNotReadyReasonId(cmdObject.getString("busyType"));
				cmdObject.remove("busyType");
			}

			if ("cmdReady".equalsIgnoreCase(command)) {
				agentModel.setNotReadyReasonId("");
			}

			if ("cmdTransferCall".equalsIgnoreCase(command)) {
				String callType = cmdObject.getString("callType");
				if("1".equals(callType)){
					//如果转坐席，则需要重新判断坐席是否再忙状态。
					String called = cmdObject.getString("called");
					String sql = "select count(*) from   " + Constants.getStatSchema()+ ".CC_RPT_AGENT_MONITOR where AGENT_STATE in ('可呼','空闲') and LOGON_FLAG = 1 and AGENT_ID = ?";
					try {
						if(!this.getQuery().queryForExist(sql, new Object[]{called})){
							return EasyResult.error(403, "转移失败，目标坐席忙");
						}
					} catch (SQLException ex) {
						CcbarLogger.getLogger().error("actionForEvent() error,cause:" + ex.getMessage(), ex);
					}
				}
				//cmdObject.put("", "");
				// 这里再Event的事件中对被叫号码做出局处理。具体看TransferCallEvent
			}

//			if ("cmdLogout".equalsIgnoreCase(command)) {
//			}
		}

		try {
			requestDataModel.setCommand(command);
			requestDataModel.setEntId(getEntId());
			requestDataModel.setAgentId(agentId);
			requestDataModel.setTimestamp(System.currentTimeMillis() + "");
			requestDataModel.setVersion("v1");
			requestDataModel.setData(cmdObject);
			requestDataModel.setBizSessionId(getRequest().getSession().getId());
			this.eventMessage.handleMessage(requestDataModel);
			result.setSuccess(RESULT_SUCC, RESULT_SUCC);
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("actionForEvent() error,cause:" + ex.getMessage(), ex);
			result.setSuccess(RESULT_FAIL, "操作失败,原因:" + ex.getMessage());
		}
		return result;
	}

	private String getPetraCmd(String cmd) {
		if (EventType.isEvent(cmd)) {
			return cmd;
		}
		return "unknow";
	}

	private String getDispalyForAgent() throws Exception {

		String sql = "select PREFIX_NUM,PREFIX_GROUP_ID from  " + this.getTableName("CC_SKILL_GROUP") + "  t1  ,  "
				+ this.getTableName("CC_SKILL_GROUP_USER")
				+ "  t2 , CC_USER t3  where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID  and t2.USER_ID = t3.USER_ID   and t3.ENT_ID = ? and t3.USER_ACCT = ?";
		EasyRow row = this.getQuery().queryForRow(sql, new Object[] { this.getEntId(), this.getAgentId() });

		String displayPhone = StringUtils.trimToEmpty(row.getColumnValue("PREFIX_NUM"));

		if (StringUtils.isBlank(displayPhone)) {
			String prefixGroupId = row.getColumnValue("PREFIX_GROUP_ID");
			if (StringUtils.isBlank(prefixGroupId))
				return null;
			sql = "select PREFIX_NUM from CC_PREFIX_GROUP_PREFIX where PREFIX_GROUP_ID = ?";
			List<EasyRow> list = this.getQuery().queryForList(sql, new Object[] { prefixGroupId });
			if (list.size() == 0)
				return null;
			Random r = new Random();
			displayPhone = list.get(r.nextInt(list.size())).getColumnValue("PREFIX_NUM");

		}
		return displayPhone;
	}

	// /**
	// * 获得外线号码对应的前缀
	// * @param entId
	// * @param agentPhone
	// * @return
	// * @throws Exception
	// */
	// private String getRemotePhoneNum(String displayForAgent,String phoneNum)
	// throws Exception{
	//
	// String sql = "select CALL_PREFIX_CODE from CC_PREFIX where ENT_ID = ? and
	// PREFIX_NUM = ?";
	// String prefixCode = this.getQuery().queryForString(sql, new
	// Object[]{this.getEntId(),displayForAgent});
	// prefixCode = StringUtils.trimToEmpty(prefixCode);
	// return prefixCode + phoneNum;
	// }
	//
	/**
	 * Ccbar的初始化操作，这里只针对已经登录的用户
	 * 
	 * @return
	 */
	public EasyResult actionForCcbarInit() {
		setResHeader();
		if (this.getYCUserPrincipal() == null) {
			return this.getSessionTimeoutResult();
		}

		String cmdJson = getRequest().getParameter("cmdJson");

		this.getRequest().getSession().setAttribute("S_CCBAR_INIT_PARAMS", cmdJson);

		String sql = "select PHONE_NUM,F_PHONE_NUM from CC_USER  where USER_ACCT = ? ";

		String phoneNum = "";

		String fphoneNum = "";

		try {
			EasyRow row = this.getQuery().queryForRow(sql, new Object[] { this.getAgentId() });
			phoneNum = row.getColumnValue("PHONE_NUM");
			fphoneNum = row.getColumnValue("F_PHONE_NUM");
		} catch (SQLException ex) {
			CcbarLogger.getLogger().error("actionForCcbarInit error,cause:" + ex.getMessage(), ex);
		}

		// 这里获取ccbar的样式，针对电销的。1 极简模式 2 标准模式 3全部功能都有。
		String mode = AppContext.getContext("yc-ccbar").getProperty("PORTAL_CCBAR_MODE", "2");
		if (StringUtils.isBlank(mode))
			mode = "2";

		JSONObject config = new JSONObject();
		config.put("ccbarMode", mode);
		config.put("area", Constants.getArea()); //取值 SD 顺德 HF 合肥

		// 这里改成话机号码，如果agentPhones不存在，则手工输入，一个自动签入，多个选择签入
		JSONArray phones = new JSONArray();

		if (StringUtils.isNotBlank(phoneNum)) {
			phones.add(phoneNum);
		}

		if (StringUtils.isNotBlank(fphoneNum)) {
			String[] items = fphoneNum.split(",");
			for (String item : items) {
				phones.add(item);
			}
		}

		config.put("agentPhones", phones);

		sql = "select BUSY_TYPE ,BUSY_TYPE_NAME from " + this.getTableName("CC_AGENT_BUSY_TYPE")
				+ " where STATE = 1 and ENT_ID = ? order by IDX_ORDER ";

		List<EasyRow> list = null;
		try {
			list = this.getQuery().queryForList(sql, new Object[] { this.getEntId() });
		} catch (Exception ex) {
			CcbarLogger.getLogger().error("actionForCcbarInit error,cause:" + ex.getMessage(), ex);
		}

		JSONArray busyTypes = new JSONArray();

		if (list == null)
			list = new ArrayList<EasyRow>();

		if (list.size() == 0) {
			JSONObject busyType = new JSONObject();
			busyType.put("busyType", "1");
			busyType.put("busyTypeName", "小休");
			busyTypes.add(busyType);
			busyType = new JSONObject();
			busyType.put("busyType", "2");
			busyType.put("busyTypeName", "会议");
			busyTypes.add(busyType);
			busyType = new JSONObject();
			busyType.put("busyType", "3");
			busyType.put("busyTypeName", "培训");
			busyTypes.add(busyType);
		} else {
			for (EasyRow row : list) {
				JSONObject busyType = new JSONObject();
				busyType.put("busyType", row.getColumnValue("BUSY_TYPE"));
				busyType.put("busyTypeName", row.getColumnValue("BUSY_TYPE_NAME"));
				busyTypes.add(busyType);
			}
		}

		config.put("busyTypes", busyTypes);

		sql = "SELECT count(1)  FROM   " + this.getTableName("CC_SKILL_GROUP_CHANNEL") + " t1, "
				+ this.getTableName("CC_SKILL_GROUP_USER")
				+ "  t2 where t1.SKILL_GROUP_ID = t2.SKILL_GROUP_ID and  t2.USER_ID =  ?  and t2.BUSI_ORDER_ID = ? ";

		try {
			int channelCount = this.getQuery().queryForInt(sql,
					new Object[] { this.getRemoteUser(), this.getBusiOrderId() });
			config.put("voiceAgent", "on");
			config.put("multiMediaAgent", "on");
			if (channelCount == 0) {
				config.put("multiMediaAgent", "off");
			}
		} catch (SQLException ex) {
			CcbarLogger.getLogger().error("actionForCcbarInit error,cause:" + ex.getMessage(), ex);
		}

		EasyResult result = EasyResult.ok();
		result.setData(config);
		return result;
	}

	private String getAgentStateInfo(String entId, String agentId, String command) {
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("entId", entId);
		jsonObject.put("agentId", agentId);
		jsonObject.put("command", command);
		return jsonObject.toJSONString();
	}

	protected String getResId() {
		return null;
	}

	/**
	 * 
	 * URL:http://*************:9060/AgentEvent?action=AgentProxy
	 * post内容：command=login|logout|getState&phoneNum:话机号码
	 * 
	 * 返回：result=login|logout ,login代表登录状态 logout代表目前坐席状态为登录状态。
	 * 
	 * @return
	 * @throws Exception
	 */

	/**
	 * 处理坐席的签入
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForLogin() throws Exception {
		setResHeader();
		if (this.getYCUserPrincipal() == null) {
			return this.getSessionTimeoutResult();
		}

		String _sessionId = this.cache.get("CCBAR_LOGIN_AGENT_" + this.getAgentId());

		if (StringUtils.isNotBlank(_sessionId)) {
			if (!this.getRequest().getSession().getId().equals(_sessionId)) {
				return getDataResult("Login", RESULT_FAIL, "签入失败[403]，原因:坐席工号已签入", null);
			}
		}

		EntContext entContext = EntContext.getContext(this.getResEntId());

		if (entContext.getLicense() > 0) {
			JSONObject jsonObject = CacheManager.getMemcache().get("ICCS_ENT_" + this.getResEntId());
			if (jsonObject != null) {
				int onlineAgentCoiunt = jsonObject.getIntValue("logonAgentCount");
				if (onlineAgentCoiunt >= entContext.getLicense()) {
					return getDataResult("Login", RESULT_FAIL,
							"签入失败[403]，原因:登录坐席数已达上线[" + entContext.getLicense() + "]", null);
				}
			}
		}

		// 缓存坐席信息
		AgentModel agent = this.cacheAgentInfo();

		// 判断坐席是否已经分配到技能组
		if (StringUtils.isBlank(agent.getSkillGroupId())) {
			return getDataResult("Login", RESULT_FAIL, "签入失败，原因:坐席[" + this.getAgentId() + "]缺少归属技能组", null);
		}

		String cmdJsonString = getRequest().getParameter("cmdJson");

		CcbarLogger.getLogger().info("<CCBAR-Login><" + this.getAgentId() + "> << " + cmdJsonString);

		JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);

		if (cmdJson == null) {
			return getDataResult("Login", RESULT_FAIL, "签入失败，原因:cmdJson is null!", null);
		}

		// 全媒体签入
		String multiMediaSwitch = cmdJson.getString("multiMediaSwitch");
		// 语音签入
		String voiceSwitch = cmdJson.getString("voiceSwitch");
		if (StringUtils.isBlank(multiMediaSwitch)) 	multiMediaSwitch = "off";
		if (StringUtils.isBlank(voiceSwitch))  	voiceSwitch = "off";
		if ("off".equals(multiMediaSwitch) && "off".equals(voiceSwitch)) {
			voiceSwitch = "on";
		}

		agent.setVoiceSwitch(voiceSwitch);
		agent.setMultiMediaSwitch(multiMediaSwitch);
		EasyResult result = null;

		if ("on".equalsIgnoreCase(voiceSwitch)) {
			ICCSLogger.getLogger().info("<CCBAR-Login><" + this.getAgentId() + "> << " + cmdJsonString);
			result = this.actionForVoiceLogin();
		} else {
			result = this.actionForMediaLogin();
		}
		AgentInfos.updateCache(this.getAgentId(),agent);
		return result;
		//return getDataResult("Login", RESULT_SUCC, RESULT_SUCC, result);
	}

	/**
	 * 
	 * URL:http://*************:9060/AgentEvent?action=AgentProxy
	 * post内容：command=login|logout|getState&phoneNum:话机号码
	 * 
	 * 返回：result=login|logout ,login代表登录状态 logout代表目前坐席状态为登录状态。
	 * 
	 * @return
	 * @throws Exception
	 */

	/**
	 * 处理坐席的签入
	 * 
	 * @return
	 * @throws ServletException
	 * @throws IOException
	 */
	public EasyResult actionForMediaLogin() throws Exception {
		setResHeader();
		EasyResult result = null;
		// 缓存坐席信息
		AgentModel agent = AgentInfos.getAgentInfo(this.getAgentId());

		JSONObject data = new JSONObject();
		this.eventMessage.getAgentEvent().clear(this.getAgentId());
		result = getDataResult("Login", RESULT_FAIL, "马上缓存坐席",Constants.getAgentBrokerName());
		MediaCacheUtil.put(Constants.BROKER_AGENT_NAME+this.getAgentId(), Constants.getAgentBrokerName(),3600*24);
		MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(this.getEntId(), this.getAgentId(), "cmdLogin"));
		if (this.checkCommandResult(this.getAgentId(), "cmdLogin")) {
			String agentState = MediaAgentStateEnum.BUSY.getReq(agent).toJSONString();
			String respLogin = MediaRespCmdEnum.Login.getReq(agent, RespResultEnum.SUCCESS).toJSONString();
			MediaLogger.getLogger().info("<"+this.getAgentId()+"> >>"+agentState);
			MediaLogger.getLogger().info("<"+this.getAgentId()+"> >>"+respLogin);
			this.eventMessage.getPortalQueue().put(respLogin);
			this.eventMessage.getPortalQueue().put(agentState);

			//增加视频客服的“虚拟头像地址”，“是否默认开启坐席摄像头”
	        JSONObject videoConfig = getVideoConfig();
	        String headUrl = videoConfig.getString("VIDEO_AGENT_HEAD_URL");
	        if(StringUtils.isNotBlank(headUrl)) {
	        	headUrl = Constants.getOutLink()+headUrl;
	        }
	        String agentId = getAgentId();
	        data.put("videoagentPhone", agentId.substring(0, agentId.indexOf("@")));
	        data.put("videoHeadImgUrl", headUrl);
	        data.put("videoOpenFlag", StringUtils.equals(videoConfig.getString("OPEN_VIDEO_FLAG"), "on")?1:0);//“是否默认开启坐席摄像头”,on 开启，为空 关闭
			result =  getDataResult("Login", RESULT_SUCC, RESULT_SUCC, data);
		} else {
			result = getDataResult("Login", RESULT_FAIL, "签入全媒体坐席失败123", data);
		}

		CcbarLogger.getLogger().info("全媒体坐席签入完成<"+this.getAgentId()+"> >>"+result);
		return result;
	}
	
	
	 private JSONObject getVideoConfig() {
		  String sql = "select CONFIG FROM ycbusi.CC_MEDIA_CONFIG where ENT_ID=?";
		  try {
//			  {"VIDEO_AGENT_HEAD_URL":"/easitline-fileserver/fileview/2021/01/12/83895829555539990469903","OPEN_VIDEO_FLAG":"on","VIDEO_SATISFY_URL":"http://localhost:8080/online/pages/config/media-config.jsp","VIDEO_VIP_LIST":"v1,v2,v3,v4,pro,vip1"}
			String configStr = getQuery().queryForString(sql, new Object[] {this.getEntId()});
			return JSONObject.parseObject(configStr);
		  } catch (SQLException e) {
			  MediaLogger.getLogger().error(e.getMessage(),e);
		  }
		  
		  return new JSONObject();
	  }

}
