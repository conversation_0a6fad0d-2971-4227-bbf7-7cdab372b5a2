package com.yunqu.yc.media.center.model;

import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.log.MediaCenterLogger;

public class AutoConfig {

	/**
	 * AUTO_REPLY_FLAG  开启自动回复：["on"]开启，为空或未定义是关闭
	 */
	private String 	autoReplay = "off";
	/**
	 * WELCOME_MSG  系统欢迎语
	 */
	private String 	welcomeMsg = "";
	/**
	 * AGENT_TIMEOUT  客服回复超时时间（分钟），当客服在此时间内(系统时延15秒内)未回复，系统自动发送客服提示语
	 */
	private int 	agentTimeout = 5 * 60 * 1000;
	/**
	 * VISITOR_TIMEOUT  访客回复超时时间（分钟），当访客在此时间内(系统时延15秒内)未回复，系统自动发送系统询问提示语
	 */
	private int 	userTimeout = 5 * 60 * 1000;
	/**
	 * AGENT_TIMEOUT_MSG  客服回复超时提示语
	 */
	private String 	agentTimeoutMsg = "";
	/**
	 * VISITOR_TIMEOUT_MSG 访客回复超时提示语
	 */
	private String 	userTimeoutMsg = "";
	/**
	 * AUTO_CLOSESESSION_FLAG  开启自动关闭对话功能：["on"]开启，为空或未定义是关闭
	 */
	private String	autoClose = "";
	/**
	 * AUTO_CLOSESESSION_MSG  自动关闭对话提示语
	 */
	private String autoCloseMsg = "";
	/**
	 * AUTO_CLOSESESSION_TIMEOUT  自动关闭对话时间（分钟），访客在此时间内未回复坐席消息(系统时延15秒内)，系统自动关闭对话
	 */
	private int		closeTime = 15 * 60 * 1000;
	
	/**
	 * VISITOR_QUEUE_MSG  访客接入排队时，系统自动回复内容
	 */
	private String 	queueMsg = "";
	
	/**
	 * VISITOR_QUEUE_MSG_LIST  访客接入排队时，系统自动回复内容，按会员等级配置，"{"v1":"您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。vip1","v3":"您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。黄金","v2":"您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。会员"}"
	 */
	private JSONObject 	queueMsgList = new JSONObject() ;
	/**
	 * VISITOR_CANCELQUE_MSG  访客取消排队时，系统自动回复内容
	 */
	private String 	cancelQueueMsg = "";
	
	/**
	 * QUEUE_FULL_MSG访客排队人数到达上限时，系统自动回复内容
	 */
	private String 	queueFullMsg = "";
	
	/**
	 * VISITOR_ACCESS_MSG  访客成功接入客服时，系统自动回复内容
	 */
	private String 	accessMsg = "";
	
	/**
	 * VISITOR_ACCESS_MSG_LIST  访客成功接入客服时，系统自动回复内容,，按会员等级配置，"{"v1":"您好，我是客服XXX，很高兴为你服务！","v3":"您好，我是客服XXX，很高兴为你服务！","v2":"您好，我是客服XXX，很高兴为你服务！"}"
	 */
	private JSONObject 	accessMsgList = new JSONObject();
	
	/**
	 * AGENT_OFFLINE_MSG  无客服在线时，系统自动回复内容
	 */
	private String 	agentOfflineMsg = "";
	/**
	 * VISITOR_QUEUE_TIMEOUT  访客排队超时时间（分钟），当访客排队超过此时间(系统时延15秒内)，系统自动发送超时提示语
	 */
	private long 	queueTimeout = 3 * 60 * 1000;
	/**
	 * QUEUE_TIMEOUT_MSG  超时提示语
	 */
	private String 	queueTimeoutMsg = "";
	/**
	 * SYSTEM_NOTINSERVICE_MSG  非客服工作时间时，系统自动回复内容
	 */
	private String 	notWorkMsg = "";
	/**
	 * GOODBYE_MSG  会话结束时，系统自动回复内容
	 */
	private String 	goodbyeMsg = "";
	/**
	 * BLACKLIST_MSG  访客属于红黑名单时，系统自动回复内容
	 */
	private String 	blacklistMsg = "";
	
	/**
	 * 排队通知间隔时间（秒）
	 */
	private int queueNotifyTime = 0;
	
	/**
	 * IN_AGENT_AFTER_MSG 这里是人工客服，请问有什么可以帮到您？
	 */
	private String inAgentAfterMsg = "";
	/**
	 * IN_QUEUE_MSG 排队过程中，您可先提前输入您的问题或需求
	 */
	private String inQueueAfterMsg = "";
	
	
	
	public String getAutoReplay() {
		return autoReplay;
	}
	public void setAutoReplay(String autoReplay) {
		this.autoReplay = autoReplay;
	}
	public String getWelcomeMsg() {
		return welcomeMsg;
	}
	public void setWelcomeMsg(String welcomeMsg) {
		this.welcomeMsg = welcomeMsg;
	}
	public int getAgentTimeout() {
		return agentTimeout;
	}
	public void setAgentTimeout(String agentTimeout) {
		if(StringUtils.isBlank(agentTimeout)) return ;
		try {
			this.agentTimeout = Integer.parseInt(agentTimeout)*60*1000;
		} catch (Exception ex) {
			// TODO: handle exception
		}
	}
	public int getUserTimeout() {
		return userTimeout;
	}
	

	
	public void setUserTimeout(String userTimeout) {
		if(StringUtils.isBlank(userTimeout)) return ;
		try {
			this.userTimeout = Integer.parseInt(userTimeout)*60*1000;
		} catch (Exception ex) {
			// TODO: handle exception
		}
	}
	
	public String getAgentTimeoutMsg() {
		return agentTimeoutMsg;
	}
	public void setAgentTimeoutMsg(String agentTimeoutMsg) {
		this.agentTimeoutMsg = agentTimeoutMsg;
	}
	public String getUserTimeoutMsg() {
		return userTimeoutMsg.replaceAll("#time#", (this.closeTime/1000/60)+"");
	}
	public void setUserTimeoutMsg(String userTimeoutMsg) {
		this.userTimeoutMsg = userTimeoutMsg;
	}
	
	public String getAutoClose() {
		return autoClose;
	}
	public void setAutoClose(String autoClose) {
		this.autoClose = autoClose;
	}
	
	public String getAutoCloseMsg() {
		return autoCloseMsg;
	}
	public void setAutoCloseMsg(String autoCloseMsg) {
		this.autoCloseMsg = autoCloseMsg;
	}
	public int getCloseTime() {
		return closeTime;
	}
	public void setCloseTime(String closeTime) {
		if(StringUtils.isBlank(closeTime)) return ;
		try {
			this.closeTime = Integer.parseInt(closeTime)*60*1000;
		} catch (Exception ex) {
			// TODO: handle exception
		}
	}
	public String getQueueMsg() {
		return queueMsg;
	}
	public void setQueueMsg(String queueMsg) {
		this.queueMsg = queueMsg;
	}
	public String getCancelQueueMsg() {
		return cancelQueueMsg;
	}
	public void setCancelQueueMsg(String cancelQueueMsg) {
		this.cancelQueueMsg = cancelQueueMsg;
	}
	public String getQueueFullMsg() {
		return queueFullMsg;
	}
	public void setQueueFullMsg(String queueFullMsg) {
		this.queueFullMsg = queueFullMsg;
	}
	public String getAccessMsg() {
		return accessMsg;
	}
	public void setAccessMsg(String accessMsg) {
		this.accessMsg = accessMsg;
	}
	public String getAgentOfflineMsg() {
		agentOfflineMsg = StringUtils.isNotBlank(agentOfflineMsg)?agentOfflineMsg:"当前坐席不在线，请稍后再试";
		return agentOfflineMsg;
	}
	public void setAgentOfflineMsg(String agentOfflineMsg) {
		this.agentOfflineMsg = agentOfflineMsg;
	}
	public long getQueueTimeout() {
		return queueTimeout;
	}
	public void setQueueTimeout(String queueTimeout) {
		if(StringUtils.isBlank(queueTimeout)) return ;
		try {
			this.queueTimeout = Integer.parseInt(queueTimeout)*60*1000;
		} catch (Exception ex) {
			// TODO: handle exception
		}
	}
	public String getQueueTimeoutMsg() {
		return queueTimeoutMsg;
	}
	public void setQueueTimeoutMsg(String queueTimeoutMsg) {
		this.queueTimeoutMsg = queueTimeoutMsg;
	}
	public String getNotWorkMsg() {
		return notWorkMsg;
	}
	public void setNotWorkMsg(String notWorkMsg) {
		this.notWorkMsg = notWorkMsg;
	}
	public String getGoodbyeMsg() {
		return goodbyeMsg;
	}
	public void setGoodbyeMsg(String goodbyeMsg) {
		this.goodbyeMsg = goodbyeMsg;
	}
	public String getBlacklistMsg() {
		return blacklistMsg;
	}
	public void setBlacklistMsg(String blacklistMsg) {
		this.blacklistMsg = blacklistMsg;
	}
	public int getQueueNotifyTime() {
		return queueNotifyTime;
	}
	public void setQueueNotifyTime(int queueNotifyTime) {
		if(queueNotifyTime<=0) {
			queueNotifyTime = 1*60;
		}
		this.queueNotifyTime = queueNotifyTime*60;
	}
	public JSONObject getQueueMsgList() {
		return queueMsgList;
	}
	
	public void setQueueMsgList(String str) {
		if(StringUtils.isBlank(str)) {
			str = "{}";
		}
		try {
			this.queueMsgList = JSONObject.parseObject(str);
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	public String getQueueMsgByLevel(String levelCode) {
		if(StringUtils.isBlank(levelCode)) {
			return this.getQueueMsg();
		}
		String msg = queueMsgList.getString(levelCode);
		if(StringUtils.isBlank(msg)) {
			msg = this.getQueueMsg();
		}
		return msg;
	}
	
	public JSONObject getAccessMsgList() {
		return accessMsgList;
	}
	
	public void setAccessMsgList(String str) {
		if(StringUtils.isBlank(str)) {
			str = "{}";
		}
		try {
			this.accessMsgList = JSONObject.parseObject(str);
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	public String getAccessMsgByLevel(String levelCode) {
		if(StringUtils.isBlank(levelCode)) {
			return this.getAccessMsg();
		}
		String msg = accessMsgList.getString(levelCode);
		if(StringUtils.isBlank(msg)) {
			msg = this.getAccessMsg();
		}
		return msg;
	}
	public String getInAgentAfterMsg() {
		return inAgentAfterMsg;
	}
	public void setInAgentAfterMsg(String inAgentAfterMsg) {
		this.inAgentAfterMsg = inAgentAfterMsg;
	}
	public String getInQueueAfterMsg() {
		return inQueueAfterMsg;
	}
	public void setInQueueAfterMsg(String inQueueAfterMsg) {
		this.inQueueAfterMsg = inQueueAfterMsg;
	}
	
	
}
