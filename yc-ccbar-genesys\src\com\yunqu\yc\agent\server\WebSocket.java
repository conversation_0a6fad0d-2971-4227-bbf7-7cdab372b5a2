package com.yunqu.yc.agent.server;


import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.chat.ChatContext;
import com.yunqu.yc.agent.chat.model.MessageModel;
import com.yunqu.yc.agent.enums.MsgSenderEnum;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.EventLogger;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.mqclient.AgentBroker;
import com.yunqu.yc.agent.mqclient.MediaProducerBroker;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.impl.v1.EventHandlerV1;
import com.yunqu.yc.agent.msg.model.RequestDataV1;
import com.yunqu.yc.agent.server.video.VideoMessage;
import com.yunqu.yc.agent.util.CacheUtil;
import com.yunqu.yc.agent.util.CommonUtil;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.ServerContext;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.MapKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.http.HttpSession;
import javax.websocket.*;
import javax.websocket.server.PathParam;
import javax.websocket.server.ServerEndpoint;
import java.io.IOException;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.ConcurrentHashMap;

@ServerEndpoint(value = "/websocket/{username}", configurator = HttpSessionConfigurator.class)
public class WebSocket {
  
    private static int onlineCount = 0;  
    private static EasyCache cache = CacheManager.getMemcache();
    private static Map<String, WebSocket> clients = new ConcurrentHashMap<String, WebSocket>();
    private Session session;  
    private String username;
    private String agentId;
	private String entId;
    private Integer agentSequence=0;
      
    @OnOpen  
    public void onOpen(@PathParam("username") String username, Session session) throws IOException {
    	//websocket自动断开的问题，tomcat默认最大的文本消息是8192b，超过时自动断开连接，需要在onOpen()时手动设置
    	int maxTextSize = 1024*1024;
    	session.setMaxTextMessageBufferSize(maxTextSize);
    	session.setMaxBinaryMessageBufferSize(maxTextSize);
		MediaLogger.getLogger().info("<websocket> WebSocket.onOpen(),session["+session.getId()+"],agentId["+agentId+"] 已链接:"+session.getQueryString());
        this.username = username;  
        this.session = session;
		YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
		if(principal==null){
			try {
				MediaLogger.getLogger().warn("<websocket> WebSocket.onOpen(),session["+session.getId()+"],agentId["+agentId+"] error,cause:principal is null!");
				subOnlineCount();
				JSONObject result = new JSONObject();
				result.put("error", "session timeout！");
				session.getAsyncRemote().sendText(result.toJSONString());
				session.close();
				if(StringUtils.isBlank(this.agentId)){
					clients.remove(this.agentId);
				}
			}catch (Exception e){
				MediaLogger.getLogger().error(e.getMessage(),e);
			}
			return;
		}
        try {
			this.agentId = principal.getLoginAcct();
			this.entId = principal.getEntId();
			addOnlineCount();
			//先主动关闭历史连接
			WebSocket oldWebSocket = clients.get(this.agentId);
			if(oldWebSocket!=null && StringUtils.equals(session.getId(),oldWebSocket.session.getId())){
				MediaLogger.getLogger().warn("<websocket> WebSocket.onOpen(),agentId["+agentId+"] 内存中已存在连接，直接覆盖，oldSession["+oldWebSocket.session.getId()+"] is open："+oldWebSocket.session.isOpen());
			}
			clients.put(this.agentId, this);

			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);

			//处理全媒体websocket登录
			MediaLogger.getLogger().info("<websocket> WebSocket.onOpen(),session["+session.getId()+"],agentId["+agentId+"] started!");
			if("on".equalsIgnoreCase(agentModel.getMultiMediaSwitch())){
				//fix： 如果websocket重新接入，则重新通知mediacenter激活坐席。
				JSONObject cmdJson = new JSONObject();
				cmdJson.put("entId", principal.getEntId());
				cmdJson.put("agentId", agentId);
				cmdJson.put("command", "cmdOpen");
				MediaProducerBroker.sendMediaCenterMessage(cmdJson.toJSONString());

				//通知助手连接
				AssistantWebSocketClient.connect(this.agentId,null);
			}

		}catch (Exception e){
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
    }

    @OnClose  
    public void onClose() throws IOException {
    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
		try {
			if(StringUtils.isBlank(this.agentId)){
				return;
			}
			WebSocket oldWebSocket = clients.get(this.agentId);
			if(oldWebSocket==null){
				MediaLogger.getLogger().warn("<websocket> WebSocket.onClose(),agentId["+agentId+"] 内存中不存在连接对象：session["+session.getId()+"]");
			}
			clients.remove(this.agentId);

			subOnlineCount();
			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);

			//处理全媒体websocket登出
			if("on".equalsIgnoreCase(agentModel.getMultiMediaSwitch())){
				MediaLogger.getLogger().info("<websocket> WebSocket.onClose(),session["+session.getId()+"],agentId["+agentId+"] 已断开");
				JSONObject  cmdJson = new JSONObject();
				cmdJson.put("entId", agentModel.getEntId());
				cmdJson.put("agentId", agentId);
				cmdJson.put("command", "cmdClose");  //坐席异常关闭
				MediaProducerBroker.sendMediaCenterMessage(cmdJson.toJSONString());
				//通知助手连接
				AssistantWebSocketClient.disConnect(this.agentId);
				return;
			}

			//处理语音websocket登出
			try {
				CcbarLogger.getLogger().info("<websocket> WebSocket.onClose(),session["+session.getId()+"],agentId["+agentId+"] 已断开");

				RequestDataV1 requestDataModel = new RequestDataV1();
				requestDataModel.setCommand("cmdLogout");
				requestDataModel.setEntId(agentModel.getEntId());
				requestDataModel.setAgentId(agentModel.getAgentId());
				requestDataModel.setTimestamp(System.currentTimeMillis() + "");
				requestDataModel.setVersion("v1");
				requestDataModel.setData(new JSONObject());
				EventHandlerV1 eventMessage = (EventHandlerV1) EventFactory.getEventMessage("v1");
				eventMessage.handleMessage(requestDataModel);
			} catch (Exception ex) {
				CcbarLogger.getLogger().error(ex.getMessage(),ex);
			}
		}catch (Exception e){
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
    }
  
    @OnMessage  
    public void onMessage(String message) throws IOException {
		EventLogger.getLogger().info("<websocket> WebSocket.onMessage(),session["+session.getId()+"],agentId["+agentId+"] << "+message );
		try {
			JSONObject jsonObject = JSONObject.parseObject(message);
			JSONObject cmdJson = jsonObject.getJSONObject("cmdJson");
			String event = jsonObject.getString("event");
			String msgType = cmdJson.getString("msgType");
			String sessionId = cmdJson.getString("sessionId");
			cmdJson.put("sender",MsgSenderEnum.getEnumByKey(cmdJson.getString("sender"), MsgSenderEnum.坐席).getKey());
			YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();

			//websocket连接对象是否存在
			WebSocket _webSocket = clients.get(this.agentId);
			if(_webSocket==null) {
				if(this.session!=null&&this.session.isOpen()) {
					if(principal==null){
						MediaLogger.getLogger().error("<websocket> WebSocket.onMessage(),session["+session.getId()+"],agentId["+agentId+"] 内存中WebSocket["+this.agentId+"]不存在，登录信息session.getUserPrincipal() 也不存在!");
						JSONObject result = new JSONObject();
						result.put("error", "登录信息已过期，请重新登录！");
						session.getAsyncRemote().sendText(result.toJSONString());
						session.close();
						if(StringUtils.isBlank(this.agentId)){
							clients.remove(this.agentId);
						}
						return;
					}
					MediaLogger.getLogger().warn("<websocket> WebSocket.onMessage(),session["+session.getId()+"],agentId["+agentId+"] 内存中WebSocket["+this.agentId+"]不存在，重新将当前对象加入到内存中!");
					this.agentId = principal.getLoginAcct();
					addOnlineCount();
					clients.put(this.agentId, this);
				}
			}

			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);

			//心跳事件
			if("heartbeat".equalsIgnoreCase(event)){
				//处理语音坐席心跳
				if("on".equalsIgnoreCase(agentModel.getVoiceSwitch())){
					cache.put("CCBAR_LOGIN_AGENT_" + agentId,agentModel.getSessionId() ,30);
					try {
						JSONObject dataObject = new JSONObject();
						dataObject.put("MsgType", "AgentEventMonitor");
						dataObject.put("event", "ping");
						dataObject.put("agentId", agentModel.getAgentId());
						AgentBroker.sendMessage(dataObject.toJSONString());
					} catch (Exception e) {
						CcbarLogger.getLogger().error(e.getMessage(),e);
					}
					return;
				}

				//处理全媒体坐席心跳
				String httpSessionId = getHttpSessionId();
				String _sessionId = cache.get("CCBAR_LOGIN_AGENT_" + this.agentId);
				if (StringUtils.isNotBlank(_sessionId)) {
					if(StringUtils.equals(httpSessionId,_sessionId)){
						cache.put("CCBAR_LOGIN_AGENT_" + agentId,httpSessionId ,20);
					}else{
						MediaLogger.getLogger().warn("<websocket> WebSocket.onMessage(),session["+session.getId()+"],agentId["+agentId+"] 已在其他地方签入，当前websocket连接需自动断开！_sessionId："+_sessionId+"，当前httpSessionId："+httpSessionId);
//						JSONObject result = new JSONObject();
//						result.put("error", "已在其他地方签入，当前websocket连接需自动断开！");
//						session.getAsyncRemote().sendText(result.toJSONString());
//						session.close();
//						clients.remove(this.agentId);

						//模拟签出接口发出的通知
//						{"agentId":"3020@1000","bizSessionId":"","sysId":"001","entId":"1000","eventSource":"media","messageId":"notify","version":"v1",
//						"cmddata":{"result":"Succ","srcMessageId":"respLogout","resultCode":"000","resultDesc":"成功"},"timestamp":1749619959303}
						JSONObject json = new JSONObject();
						json.put("agentId", agentId);
						json.put("bizSessionId", "");
						json.put("sysId", "001");
						json.put("entId", entId);
						json.put("eventSource", "media");
						json.put("messageId", "notify");
						json.put("version", "v1");
						json.put("timestamp", System.currentTimeMillis());
						JSONObject cmddata = new JSONObject();
						cmddata.put("result", "Succ");
						cmddata.put("srcMessageId", "respLogout");
						cmddata.put("resultCode", "000");
						cmddata.put("resultDesc", "已在其他地方签入，当前websocket连接需自动断开！");
						json.put("cmddata", cmddata);
						session.getAsyncRemote().sendText(json.toJSONString());
						return;
					}
				}

				try {
					sendMessageToAgent(agentId,message);
				} catch (Exception ex) {
					MediaLogger.getLogger().error(ex.getMessage(),ex);
				}

				try {
					sendMonitorInfo();
				} catch (Exception ex) {
					MediaLogger.getLogger().error(ex.getMessage(), ex);
				}
				try {
					JSONObject _jsonObject = new JSONObject();
					_jsonObject.put("entId", principal.getEntId());
					_jsonObject.put("agentId", agentId);
					_jsonObject.put("command", "cmdHeartbeat");
					MediaProducerBroker.sendMediaCenterMessage(_jsonObject.toJSONString());
					MediaCacheUtil.put(Constants.BROKER_AGENT_NAME + agentId, Constants.getAgentBrokerName(),3600*24);
				} catch (Exception ex) {
					MediaLogger.getLogger().error(ex.getMessage(), ex);
				}
				return;
			}

			//全媒体坐席端确认消息事件
			if("MsgConfirm".equalsIgnoreCase(event)) {
				return ;
			}

			//全媒体消息监控事件，只用于检查用户实时输入的内容
			if("MsgMonitor".equalsIgnoreCase(event)){
				return;
			}

			//将坐席需要发送给助手的消息，直接转发给助手
			if("sendToAssistant".equals(event)){
				//同步给助手
				AssistantWebSocketClient.send(event,"sendToAssistant",this.agentId, jsonObject);
				return;
			}

			//全媒体普通消息事件
			if("mediaEvent".equalsIgnoreCase(event)){
				MediaLogger.getLogger().info("<websocket> WebSocket.onMessage(),session["+session.getId()+"],agentId["+agentId+"]event["+event+"] 收到全媒体坐席端消息 << "+message );
				//响应坐席端，标识服务端已收到坐席发送的消息
				JSONObject receivedObj = JSONObject.parseObject(jsonObject.toJSONString());
				JSONPath.set(receivedObj, "$.data.result", "000");
				JSONPath.set(receivedObj, "$.data.msg", "websocket接收消息成功");
				msgReceived(principal.getEntId(),this.agentId,receivedObj);

				//消息类型：msgType=event
				if("event".equals(msgType)){
					this.actionForEvent(cmdJson);
					return;
				}

				//消息类型：msgType=agent，msgContent=""
				if("agent".equals(msgType)&& StringUtils.isBlank(cmdJson.getString("msgContent"))) {
					return;
				}
				//发送消息到客户端
				this.sendMessageToUser(cmdJson);
			}
		}catch (Exception e){
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
    }
    
    private JSONObject  getQueueMonitorInfo(){
    	AgentModel agentModel = AgentInfos.getAgentInfo(this.agentId);
    	String data = cache.get("MEDIA_MONITOR_DATA_"+agentModel.getEntId());
    	if(StringUtils.isBlank(data)) return new JSONObject();
    	JSONObject dataObject =  JSONObject.parseObject(data);
    	JSONObject channelInfo = dataObject.getJSONObject("channelInfo");
    	JSONObject groupInfo = dataObject.getJSONObject("groupInfo");
    	int type1 = 0;
    	int type2 = 0;
    	int type3 = 0;
    	int type5 = 0;
    	JSONArray queueInfo1 = new JSONArray();
    	JSONArray queueInfo2 = new JSONArray();
    	JSONArray queueInfo3 = new JSONArray();
    	JSONArray queueInfo5 = new JSONArray();//新增：APP渠道
    	
    	Set<String> channelIds = agentModel.getChannelIds();
//		MediaLogger.getLogger().info("agentChannelIds->"+channelIds);
    	Set<String> keys = channelInfo.keySet();
    	for(String channelId :keys){
    		JSONObject channelObj = channelInfo.getJSONObject(channelId);
    		String type = channelObj.getString("channelType");
    		int  queueCount = channelObj.getIntValue("queueCount");
    		if(queueCount <=0) continue;
    		if("1".equals(type)) {
    			if(channelIds.contains(channelId)){
    				type1+=queueCount;
    				queueInfo1.add(channelObj);
    			}
    		}
    		if("2".equals(type)){
    			if(channelIds.contains(channelId)){
    				type2+=queueCount;
    				queueInfo2.add(channelObj);
    			}
    		}
    		if("3".equals(type)){
    			if(channelIds.contains(channelId)){
    				type3+=queueCount;
    				queueInfo3.add(channelObj);
    			}
    		}
    		if("5".equals(type)){
    			if(channelIds.contains(channelId)){
    				type5+=queueCount;
    				queueInfo5.add(channelObj);
    			}
    		}
    	}

    	Set<String> agentSkillGroupIds = agentModel.getSkillGroupIdSet();
//    	MediaLogger.getLogger().info("agentSkillGroupIds->"+agentSkillGroupIds);
    	Set<String> skillKeys = groupInfo.keySet();
    	JSONObject newGroupQueueInfo = new JSONObject();
    	for(String skillId :skillKeys){
			JSONObject skillObj = groupInfo.getJSONObject(skillId);
			int  queueCount = skillObj.getIntValue("queueCount");
			if(queueCount <= 0) continue;
    		if(agentSkillGroupIds.contains(skillId)){
    			newGroupQueueInfo.put(skillId,skillObj);
			}
		}

    	JSONObject typeQueyInfo = new JSONObject();
    	typeQueyInfo.put("1", type1);
    	typeQueyInfo.put("2", type2);
    	typeQueyInfo.put("3", type3);
    	typeQueyInfo.put("5", type5);
    	JSONObject channelQueueInfo = new JSONObject();
    	channelQueueInfo.put("1", queueInfo1);
    	channelQueueInfo.put("2", queueInfo2);
    	channelQueueInfo.put("3", queueInfo3);
    	channelQueueInfo.put("5", queueInfo5);
    	JSONObject monitorInfo = new JSONObject();
    	monitorInfo.put("channelQueueInfo", channelQueueInfo);
    	monitorInfo.put("typeQueyInfo", typeQueyInfo);
    	monitorInfo.put("groupQueueInfo", newGroupQueueInfo);
    	
//    	MediaLogger.getLogger().info("getQueueMonitorInfo->"+monitorInfo);
    	return monitorInfo;
    }

    public void sendMonitorInfo(){
    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
    	JSONObject result = new JSONObject();
		result.put("entId", principal.getEntId());
		result.put("agentId", agentId);
		result.put("messageId", "monitorInfo");
		result.put("type", "mediaQueueInfo");
//		String data = cache.get("MEDIA_QUEUE_INFO_"+principal.getEntId());
//		if(StringUtils.isBlank(data)) data = "{}";
//		//增加技能组的排队信息返回
//		JSONObject cmdData = JSONObject.parseObject(data);
//		String monitorData = cache.get("MEDIA_MONITOR_DATA_"+principal.getEntId());
//		if(StringUtils.isNotBlank(data)) {
//			JSONObject dataObject = JSONObject.parseObject(monitorData);
//			JSONObject groupInfo = dataObject.getJSONObject("groupInfo");
//			cmdData.put("groupQueueInfo", groupInfo);
//		}
		JSONObject cmdData = getQueueMonitorInfo();
		result.put("cmddata", cmdData);
		WebSocket.sendMessageToAgent(principal.getLoginAcct(), result.toJSONString());

		result.put("type", "mediaRankInfo");
		String data = cache.get("MEDIA_SERVICE_RANK_"+principal.getEntId());
		if(StringUtils.isBlank(data)) data = "{}";
		JSONObject serviceInfo = JSONObject.parseObject(data);
		EasyCalendar cal = EasyCalendar.newInstance();
		String totalServiceCount  = MediaCacheUtil.get("MEDIA_SERVICE_COUNT_"+cal.getDateInt()+"_"+this.agentId);
		
		if(StringUtils.isBlank(totalServiceCount)){
			totalServiceCount  = "0";
		}
		serviceInfo.put("totalServiceCount", totalServiceCount);
		result.put("cmddata", serviceInfo.toJSONString());

		WebSocket.sendMessageToAgent(principal.getLoginAcct(), result.toJSONString());
	}
    
    private static  void sendMessage(String agentId,String message) throws Exception {  
    	WebSocket  item = clients.get(agentId);
    	if(item == null) {
    		MediaLogger.getLogger().error( "<websocket>["+agentId+"]Send message to agent fail,cause; WebSocket is not exist!");
    		return ;
    	}
    	if(!item.isValidMessage(message)) return;
    	if(!item.session.isOpen()){
    		throw new Exception("websocket is close!");
    	}

		JSONObject msgObj = JSONObject.parseObject(message);

		if(!"heartbeat".equals(msgObj.getString("event"))&& !"monitorInfo".equals(msgObj.getString("messageId"))){
			EventLogger.getLogger().info("<websocket> WebSocket.sendMessage(),session["+item.session.getId()+"],agentId["+agentId+"] >> "+message );
		}
		String type = msgObj.getString("type");
		//对于助手发送的消息，直接通知到坐席
		if("msgAssistant".equals(type)){
			item.session.getBasicRemote().sendText(message);
			return;
		}

		synchronized (item.session) {
    		item.session.getBasicRemote().sendText(message);
			//同步给助手,对于平台发送给坐席的消息，转一份给助手
			AssistantWebSocketClient.send(msgObj.getString("event"),"toAgent",agentId, msgObj);
		}
    }
  
    @OnError
    public void onError(Session session, Throwable error) {
    	MediaLogger.getLogger().error("<websocket> ["+agentId+"] websocket.onError() "+error.getMessage(),error);

    	// 清理连接资源
    	try {
    		if(agentId != null) {
    			WebSocket item = clients.get(agentId);
    			if(item != null && item.session != null && item.session.getId().equals(session.getId())) {
    				MediaLogger.getLogger().warn("<websocket> ["+agentId+"] removing connection due to error, session["+session.getId()+"]");
    				clients.remove(agentId);
    			}
    		}
    	} catch (Exception e) {
    		MediaLogger.getLogger().error("<websocket> ["+agentId+"] error during onError cleanup: "+e.getMessage(), e);
    	}
    }
  
    private boolean  isValidMessage(String message){
    	JSONObject eventObject = JSONObject.parseObject(message);
    	if("agentStateSync".equals(eventObject.getString("messageId"))){
			Integer msgSequence= eventObject.getInteger("sequence");
			if(msgSequence == null) msgSequence = 0;
			if(agentSequence - msgSequence  >  9999999 ) {
				agentSequence = 0;
			}
			if( agentSequence > msgSequence){
				if(ServerContext.isDebug()){
					MediaLogger.getLogger().info("[DEBUG] agentId["+agentId+"]  msgSequence["+msgSequence+"] less then agentSequence["+agentSequence+"].");
				}
				return false;
			}
			agentSequence = msgSequence;
		}
    	return true;
    }
    

    /**
     * 	发送信息给坐席
     * @param agentId
     * @param message
     * @throws Exception
     */
    public static void sendMessageToAgent(String agentId,String message){
		try {
			JSONObject jsonObject = JSONObject.parseObject(message);
			String messageId = jsonObject.getString("messageId");
			WebSocket  item = clients.get(agentId);
			if(item == null) {
				MediaLogger.getLogger().warn("<websocket>["+agentId+"] websocket object not found! "+message+",调用栈:"+CommonUtil.getInvokeStackTrace("sendMessageToAgent"));
				return;
			}
			if(!item.session.isOpen()){
				MediaLogger.getLogger().error("<websocket>["+agentId+"] websocket is close! "+message+",调用栈:"+ CommonUtil.getInvokeStackTrace("sendMessageToAgent"));
				return;
			}
			try {
				sendMessage(agentId,message);
			} catch (Exception ex) {
				MediaLogger.getLogger().warn("<websocket>["+agentId+"] send message failed: "+ex.getMessage(),ex);
			}
			if("respLogout".equalsIgnoreCase(messageId)){
				clients.remove(agentId);
				MediaLogger.getLogger().info("<websocket> WebSocket.sendMessageToAgent(),session["+item.session.getId()+"],agentId["+agentId+"] Close()!,cause: receive iccs respLogout message!");
			}
		} catch (Exception e) {
			MediaLogger.getLogger().warn(e.getMessage(),e);
		}

    }

    public static void sendMessageAll(String message) throws IOException {
        for (WebSocket item : clients.values()) {
            item.session.getAsyncRemote().sendText(message);
        }
    }

    public static synchronized int getOnlineCount() {
        return onlineCount;
    }

    public static synchronized void addOnlineCount() {
        onlineCount++;
    }

    public static synchronized void subOnlineCount() {
        onlineCount--;
    }

    public static synchronized Map<String, WebSocket> getClients() {
        return clients;
    }

    public void actionForEvent(JSONObject cmdJson){

		String sessionId = cmdJson.getString("sessionId");

		//请求事件
		String event = cmdJson.getString("event");
		JSONObject eventData = cmdJson.getJSONObject("eventData");

		if(eventData==null) eventData = new JSONObject();

		//消息撤回
		if("withdraw".equalsIgnoreCase(event)) {
			this.withdraw(cmdJson);
		}
		//发起转移
		if("StartTransfer".equals(event)){
			this.startTransfer(cmdJson);
			return;
		}
		
		//转移确认
		if("ConfirmTransfer".equals(event)){
			this.confirmTransfer(cmdJson);
		}
		
		/**
		 * 坐席关闭会话
		 */
		if("Released".equals(event)){
			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
			JSONObject  jsonObject = new JSONObject();
			jsonObject.put("entId", agentModel.getEntId());
			jsonObject.put("agentId", agentId);
			jsonObject.put("command", "cmdReleased");
			jsonObject.put("sessionId", cmdJson.getString("sessionId"));
			try {
				MediaProducerBroker.sendMediaCenterMessage(jsonObject.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error(ex, ex);
			}
		}
		
		//视频通话相关事件
		if(StringUtils.isNotBlank(event)&&(event.toLowerCase().contains("video") || event.toLowerCase().contains("inviteagent"))) {
			AgentModel agentModel = AgentInfos.getAgentInfo(this.agentId);
			cmdJson.put("entId", agentModel.getEntId());
			cmdJson.put("agentId", this.agentId);
			new VideoMessage().sendMessage(cmdJson);
			return;
		}
		
		/**
		 *视频通话时，文件共享
		 */
		if("shareFile".equals(event)) {
			this.sendMessageToUser(cmdJson);
		}
		
		/**
		 * 视频通话中，打标记
		 * 开始标记：startVideoTag，结束标记：stopVideoTag，删除标记：dropVideoTag
		 * {cmdJson:{msgType:'event',event:'startVideoTag',sessionId:'sssss',chatSessionId:'sssss',recordId:'111',confId:'qqqq'}}
		 */
		if(event.toLowerCase().indexOf("videotag")>-1) {
			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
			JSONObject  jsonObject = new JSONObject();
			jsonObject.put("entId", agentModel.getEntId());
			jsonObject.put("agentId", agentId);
			jsonObject.put("command", event);
			jsonObject.put("sessionId", cmdJson.getString("sessionId"));
			jsonObject.put("chatSessionId", cmdJson.getString("chatSessionId"));
			jsonObject.put("recordId", cmdJson.getString("recordId"));
			jsonObject.put("confId", cmdJson.getString("confId"));
			jsonObject.put("timestamp", System.currentTimeMillis());
			try {
				MediaProducerBroker.sendMediaCenterMessage(jsonObject.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error(ex, ex);
			}
		}
    }

	/**
	 * 会话转移-发起转移
	 * 转移逻辑
	 * 1.如果是转移到技能组（skillGroupId不为空），直接发送到mediacenter查找技能组空闲坐席，目标坐席不需要手动确认或拒绝，直接转移接入到目标坐席中。
	 * 2.1.如果是转移到指定坐席，目标坐席置忙或繁忙，目标坐席工作台弹出“转移确认提示框”。
	 * 2.2.目标坐席空闲状态（置闲且未达到服务上限）下，会话直接转接，无需目标坐席确认，此时yc-media-agent 应用配置-AUTO_TRANSFER_PASS 不生效。
	 * 3.yc-media-agent 应用配置-AUTO_TRANSFER_PASS 被转移坐席置忙或繁忙状态下 自动接受会话转移，无需在被转移坐席界面弹出确认框。
	 * {"event":"StartTransfer","sessionId":"4331f3edba798a4e9758765460af0c52","chatSessionId":"866634435","msgType":"event","eventData":{"type":1,"targetAgentId":"8008@cc","skillGroupId":"122","remark":"","nickname":"小李","transferType":"skillGroup（或者agent）"}}
	 *
	 * @param cmdJson
	 * @return boolean
	 */
	public void startTransfer(JSONObject cmdJson) {
		String sessionId = cmdJson.getString("sessionId");
		String chatSessionId = cmdJson.getString("chatSessionId");
		String transferType = cmdJson.getString("transferType");//转移类型，agent：转坐席，skillGroup：技能组

		//请求事件
		String event = cmdJson.getString("event");
		JSONObject eventData = cmdJson.getJSONObject("eventData");
		String targetAgentId = eventData.getString("targetAgentId");
		String skillGroupId = eventData.getString("skillGroupId");
		sessionId = StringUtils.isNotBlank(sessionId) ? sessionId : eventData.getString("sessionId");
		chatSessionId = StringUtils.isNotBlank(chatSessionId) ? chatSessionId : eventData.getString("chatSessionId");
		transferType = StringUtils.isNotBlank(transferType) ? transferType : eventData.getString("transferType");

		JSONObject newEventData = new JSONObject();
		newEventData.putAll(eventData);
		newEventData.put("srcAgentId", this.agentId);
		JSONObject resultTransferJson = new JSONObject();
		resultTransferJson.put("eventData", newEventData);
		resultTransferJson.put("event", "ResultTransfer");
		resultTransferJson.put("messageId", "ResultTransfer");
		resultTransferJson.put("agentId", this.agentId);
		resultTransferJson.put("msgType", "event");
		resultTransferJson.put("sessionId", sessionId);
		resultTransferJson.put("chatSessionId", chatSessionId);

		String cacheKey = "startTransferChat_" + chatSessionId;
		Object o = CacheUtil.get(cacheKey);
		if (o != null) {
			newEventData.put("code", "006");
			newEventData.put("result", "fail");
			//重复提交，通知来源坐席
			MediaLogger.getLogger().warn("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，重复提交1，通知来源坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
			return;
		}
		//防止重复提交
		synchronized (chatSessionId.intern()) {
			o = CacheUtil.get(cacheKey);
			if (o != null) {
				newEventData.put("code", "006");
				newEventData.put("result", "fail");
				//重复提交，通知来源坐席
				MediaLogger.getLogger().warn("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，重复提交2，通知来源坐席->" + resultTransferJson);
				MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
				return;
			}
			CacheUtil.put(cacheKey, cmdJson.toJSONString(), 60);
		}

		try {
			JSONObject jsonObject = new JSONObject();
			jsonObject.put("entId", this.entId);
			jsonObject.put("agentId", this.agentId);//原agentId
			jsonObject.put("targetAgentId", targetAgentId);//目标坐席ID
			jsonObject.put("skillGroupId", skillGroupId);
			jsonObject.put("command", event);
			jsonObject.put("sessionId", sessionId);
			jsonObject.put("chatSessionId", chatSessionId);
			jsonObject.put("transferType", transferType);
			jsonObject.put("eventData", eventData);
			MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，转移类型["+transferType+"]，发送mediacenter确认是否可转移->" + jsonObject);
			MediaProducerBroker.sendMediaCenterMessage(jsonObject.toJSONString());

			//000 转移成功
			//001 转移失败，当前会话已结束
			//002 转移失败，坐席不在线
			//003 转移失败，该技能组下无空闲坐席
			//004 目标坐席拒绝
			//005 需要目标坐席确认。1.转坐席；2.转组：坐席繁忙，坐席视频通话中
			//006 会话正在转移，请勿重复提交
			String resp = this.getCommandResult(agentId, "StartTransfer");

			MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，mediacenter转移结果：" + resp);

			//转移成功
			if ("000".equals(resp)) {
				newEventData.put("code", "000");
				newEventData.put("result", "succ");
				//转移成功，通知来源坐席
				MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，转移成功，通知来源坐席->" + resultTransferJson);
				MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
				CacheUtil.delete(cacheKey);
				return;
			}

			//转移失败
			if ("001".equals(resp) || "002".equals(resp) || "003".equals(resp) || "004".equals(resp)) {
				newEventData.put("code", resp);
				newEventData.put("result", "fail");
				//转移失败，通知来源坐席
				MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，转移失败，通知来源坐席->" + resultTransferJson);
				MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
				CacheUtil.delete(cacheKey);
				return;
			}

			//需要目标坐席确认
			if ("005".equals(resp)) {
				newEventData.put("code", "005");
				newEventData.put("result", "succ");
				//需要目标坐席确认，通知来源坐席
				MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，需要目标坐席确认，通知来源坐席->" + resultTransferJson);
				MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());

				//转移到技能组时，选中的坐席在 视频通话中 或 繁忙 需要确认
				if(StringUtils.isBlank(targetAgentId)){
					String cacheKey005 = "media_" + event + "_" + this.agentId+"_targetAgentId";
					targetAgentId = CacheUtil.get(cacheKey005);
				}
				if(StringUtils.isBlank(targetAgentId)){
					MediaLogger.getLogger().warn("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，会话转移失败，mediacenter返回005，需要目标坐席确认，但无目标坐席id，targetAgentId为空！！！！！");
					return;
				}
				//发送转移确认（ReceiveTransfer）给目标坐席
				newEventData.put("targetAgentId",targetAgentId);
				resultTransferJson.put("event", "ReceiveTransfer");
				resultTransferJson.put("messageId", "ReceiveTransfer");
				CacheUtil.put("ReceiveTransfer_agentId_" + chatSessionId, targetAgentId, 60);
				MediaLogger.getLogger().info("startTransfer->["+agentId+"]["+chatSessionId+"]会话正在转移，发送转移确认（ReceiveTransfer）给目标坐席->" + resultTransferJson);
				MediaProducerBroker.sendAgentMessage(targetAgentId, resultTransferJson.toJSONString());
			}

		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex.getMessage(), ex);
		}
	}

	/**
	 * 会话转移-目标坐席转移确认
	 * {"event":"ConfirmTransfer","sessionId":"4331f3edba798a4e9758765460af0c52","msgType":"event","eventData":{"nickname":"小李","targetAgentId":"8008@cc","remark":"","srcAgentId":"8003@cc","type":1,"result":"succ"}}
	 *
	 * @param cmdJson
	 */
	//v3.1#20210512-1
	protected void confirmTransfer(JSONObject cmdJson) {
		String sessionId = cmdJson.getString("sessionId");
		String chatSessionId = cmdJson.getString("chatSessionId");
		JSONObject eventData = cmdJson.getJSONObject("eventData");
		String srcAgentId = eventData.getString("srcAgentId");
		String targetAgentId = eventData.getString("targetAgentId");
		String result = eventData.getString("result");

		sessionId = StringUtils.isNotBlank(sessionId) ? sessionId : eventData.getString("sessionId");
		chatSessionId = StringUtils.isNotBlank(chatSessionId) ? chatSessionId : eventData.getString("chatSessionId");
		String cacheKey = "startTransferChat_" + chatSessionId;
		CacheUtil.delete(cacheKey);

		MediaLogger.getLogger().info("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，处理来自坐席[" + srcAgentId + "]的转移会话，sessionId:"+sessionId+"，chatSessionId："+chatSessionId);

		JSONObject newEventData = new JSONObject();
		newEventData.putAll(eventData);
		JSONObject resultTransferJson = new JSONObject();
		resultTransferJson.put("eventData", newEventData);
		resultTransferJson.put("event", "ResultTransfer");
		resultTransferJson.put("messageId", "ResultTransfer");
		resultTransferJson.put("agentId", srcAgentId);
		resultTransferJson.put("msgType", "event");
		resultTransferJson.put("sessionId", sessionId);
		resultTransferJson.put("chatSessionId", chatSessionId);

		//1.目标坐席拒绝，把确认结果返回给原来坐席。
		if ("fail".equalsIgnoreCase(result)) {
			newEventData.put("code", "004");
			newEventData.put("result", "fail");
			//目标坐席拒绝，通知来源坐席
			MediaLogger.getLogger().warn("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，目标坐席拒绝，通知来源坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(srcAgentId, resultTransferJson.toJSONString());
			return;
		}

		//2.目标坐席确认
		//2.1.检查会话是否转移给当前坐席，如果已转移给其他坐席了，前面的转移确认将失效。
		String cacheAgentId = CacheUtil.get("ReceiveTransfer_agentId_" + chatSessionId);
		if (StringUtils.isNotBlank(cacheAgentId) && !StringUtils.equals(this.agentId, cacheAgentId)) {
			newEventData.put("code", "001");
			newEventData.put("result", "fail");
			resultTransferJson.put("agentId", this.agentId);
			//坐席确认失败，通知目标坐席
			MediaLogger.getLogger().warn("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认失败，已经转移给其他坐席[" + cacheAgentId + "]，通知目标坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
			return;
		}

		//2.2.发送给mediacenter 做转移处理
		JSONObject cmdTransferJson = new JSONObject();
		cmdTransferJson.put("entId", this.entId);
		cmdTransferJson.put("agentId", this.agentId);//当前坐席
		cmdTransferJson.put("targetAgentId", targetAgentId);//目标坐席ID
		cmdTransferJson.put("command", "cmdTransfer");
		cmdTransferJson.put("sessionId", sessionId);
		cmdTransferJson.put("chatSessionId", chatSessionId);
		cmdTransferJson.put("eventData", eventData);
		//目标坐席同意转移,发送给mediacenter 做转移处理,由UserSession对象完成转移处理。
		MediaLogger.getLogger().info("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认成功，发送给mediacenter 做转移处理->" + cmdTransferJson);
		MediaProducerBroker.sendMediaCenterMessage(cmdTransferJson.toJSONString());
		String resp = this.getCommandResult(this.agentId, "cmdTransfer");
		//2.3.mediacenter转移成功
		if ("000".equals(resp)) {
			newEventData.put("code", "000");
			newEventData.put("result", "succ");
			resultTransferJson.put("agentId", srcAgentId);
			//2.3.1.mediacenter转移成功，通知原坐席，目标坐席
			MediaLogger.getLogger().info("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认成功，mediacenter转移成功，通知来源坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(srcAgentId, resultTransferJson.toJSONString());
			resultTransferJson.put("agentId", this.agentId);
			MediaLogger.getLogger().debug("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认成功，mediacenter转移成功，通知目标坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
			return;
		}

		//2.3.2.mediacenter转移失败
		try {
			newEventData.put("code", resp);
			newEventData.put("result", "fail");
			resultTransferJson.put("agentId", srcAgentId);
			//mediacenter转移失败，通知原坐席
			MediaLogger.getLogger().warn("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认成功，mediacenter转移失败，通知来源坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(srcAgentId, resultTransferJson.toJSONString());

			//mediacenter转移失败，通知目标坐席
			resultTransferJson.put("agentId", this.agentId);
			MediaLogger.getLogger().warn("confirmTransfer->["+agentId+"]["+chatSessionId+"]坐席处理转移，坐席确认成功，mediacenter转移失败，通知目标坐席->" + resultTransferJson);
			MediaProducerBroker.sendAgentMessage(this.agentId, resultTransferJson.toJSONString());
			CacheUtil.delete("ReceiveTransfer_agentId_" + chatSessionId);
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex.getMessage(), ex);
		}
	}

	/**
	 * 发送用户
	 * @param cmdJson
	 */
	private void sendMessageToUser(JSONObject cmdJson){
	
		YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
		String sessionId = cmdJson.getString("sessionId");
		String msgContent = cmdJson.getString("msgContent");  //这里必须修改
		String serialId = cmdJson.getString("serialId");//消息流水id，每条消息唯一
		serialId = StringUtils.isNotBlank(serialId)?serialId:RandomKit.randomStr();
		String msgType = cmdJson.getString("msgType");

		//从缓存中读取用户信息userInfo----yc-mediagw写入
		JSONObject visitorObject = getMediagwVisitor(sessionId);
		String chatSessionId = visitorObject.getString("chatSessionId");
		if (StringUtils.isBlank(chatSessionId)) {
			MediaLogger.getLogger().warn("<websocket> WebSocket.sendMessageToUser(),session["+session.getId()+"],agentId["+agentId+"] not found chatSessionId by cache["+Constants.USER_VISITOR+sessionId+"]！");
			// 注意：这里不设置默认值，让OracleMessage.save()方法处理，保持单一职责
		}

		MsgSenderEnum sendEnum = MsgSenderEnum.getEnumByKey(cmdJson.getString("sender"),MsgSenderEnum.坐席);

		MessageModel messageModel = new MessageModel();
		messageModel.setSerialId(serialId);
		messageModel.setInitialEvent(cmdJson.getString("event"));
		messageModel.setAgentId(principal.getLoginAcct());
		messageModel.setEntId(principal.getEntId());
		messageModel.setSessionId(sessionId);
		messageModel.setChatSessionId(chatSessionId);
		messageModel.setMsgType(msgType);
		messageModel.setSender(sendEnum.getKey());
		messageModel.setSenderCode(sendEnum.getCode());
		messageModel.setMsgContent(msgContent);
		messageModel.setChannelId(visitorObject.getString("channelId"));
		messageModel.setChannelKey(visitorObject.getString("channelKey"));

		//消息引用逻辑：读取坐席端发送的随路数据（引用消息）
		JSONObject userData = cmdJson.getJSONObject("userData");
		if (userData == null) {
			userData = new JSONObject();
		}
		messageModel.setUserInfo(userData);

		try {
			MediaProducerBroker.sendUserMessage(sessionId,messageModel.toString());
			//更新当前用户和坐席的信息通知
			JSONObject  notifyJson = new JSONObject();
			notifyJson.put("entId", principal.getEntId());
			notifyJson.put("agentId", principal.getLoginAcct());
			notifyJson.put("sessionId", sessionId);
			notifyJson.put("command", "cmdMsgNotify");
			notifyJson.put("sender", sendEnum.getKey());
			notifyJson.put("msgContent", msgContent);
			notifyJson.put("msgType", msgType);
			notifyJson.put("serialId", messageModel.getSerialId());
			MediaProducerBroker.sendMediaCenterMessage(notifyJson.toJSONString());
			//保存会话记录
			ChatContext.getMessage(principal.getEntId()).save(messageModel);
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex.getMessage(),ex);
		}

		//同步给助手
		AssistantWebSocketClient.send("agent","toUser",this.agentId, JSONObject.parseObject(messageModel.toString()));
	}
	
	/**
     * 消息已读未读
     */
    public static void msgConfirm(String entId,String agentId,JSONObject jsonObject) {
    	String event = jsonObject.getString("event");
    	JSONObject cmdJson = jsonObject.getJSONObject("cmdJson");
    	String serialId = cmdJson.getString("serialId");
    	String sendResult = (String) JSONPath.eval(jsonObject, "$.data.result");
    	JSONObject result = new JSONObject();
    	result.put("entId", entId);
    	result.put("agentId", agentId);
    	result.put("messageId", "msgConfirm");
    	result.put("type", "msgConfirm");
    	EasyCalendar cal = EasyCalendar.newInstance();
    	MapKit cmddata = MapKit.create()
    			.set("fromSerialId", serialId)	//来源消息流水id
    			.set("fromEvent", event)		//来源消息event
    			.set("sessionId", JSONPath.eval(jsonObject, "$.data.sessionId"))	//来源消息所属sessionId
    			.set("chatSessionId", JSONPath.eval(jsonObject, "$.data.chatSessionId"))	//来源消息所属chatSessionId
    			.set("timeStr", cal.getDateTime("-"))
    			.set("timeMillis", cal.getTimeInMillis())
    			.set("msg", JSONPath.eval(jsonObject, "$.data.msg"))
    			.set("result", JSONPath.eval(jsonObject, "$.data.result"));
    	result.put("cmddata", cmddata);
		WebSocket.sendMessageToAgent(agentId, result.toJSONString());

		//发送到客户端的消息改为已读
    	try {
    		if("000".equals(sendResult)) {
    			JSONObject data = new JSONObject();
    			data.put("CHAT_ID", serialId);
    			data.put("READ_STATE", 1);
    			ChatContext.getMessage(entId).update(data);
    		}
    	} catch (Exception e) {
    		MediaLogger.getLogger().error(e.getMessage(),e);
    	}
    }
    
    /**
     * 回复消息时间msgReceived
     * 每次坐席发送消息到服务端，服务端都必须返回fromSerialId对应的消息时间
     */
    public static void msgReceived(String entId,String agentId,JSONObject jsonObject) {
    	String event = jsonObject.getString("event");
    	JSONObject cmdJson = jsonObject.getJSONObject("cmdJson");
    	String sessionId = cmdJson.getString("sessionId");
    	String serialId = cmdJson.getString("serialId");
    	String sendResult = (String) JSONPath.eval(jsonObject, "$.data.result");
    	String sendResultMsg = (String) JSONPath.eval(jsonObject, "$.data.msg");
    	JSONObject result = new JSONObject();
    	result.put("entId", entId);
    	result.put("agentId", agentId);
    	result.put("messageId", "msgReceived");
    	result.put("type", "msgReceived");
    	EasyCalendar cal = EasyCalendar.newInstance();
    	MapKit cmddata = MapKit.create()
    			.set("fromSerialId", serialId)	//来源消息流水id
    			.set("fromEvent", event)		//来源消息event
    			.set("sessionId", sessionId)	//来源消息所属sessionId
    			.set("timeStr", cal.getDateTime("-"))
    			.set("timeMillis", cal.getTimeInMillis())
    			.set("msg", sendResultMsg)
    			.set("result", sendResult);
    	result.put("cmddata", cmddata);
		WebSocket.sendMessageToAgent(agentId, result.toJSONString());

		//消息发送失败，更新聊天记录为发送失败
		try {
			if(!"000".equals(sendResult)) {
				MediaLogger.getLogger().warn("[Result:"+sendResult+"]消息发送到客户端失败，返回消息 << " +jsonObject);
				JSONObject data = new JSONObject();
				data.put("CHAT_ID", serialId);
				data.put("SEND_STATE", 0);
				data.put("SEND_FIAL_DESC", sendResultMsg);
				ChatContext.getMessage(entId).update(data);
			}
		} catch (Exception e) {
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
    }

    /**
	 * 坐席撤回消息
	 * @Description :坐席发送消息给客户端，2分钟内可撤回该消息
	 * <AUTHOR>
	 * @Datetime 2021/10/13 14:57
	 * @Param cmdJson:
	 * @return: void
	 */
	public void withdraw(JSONObject cmdJson){
		try {
			AgentModel agentInfo = AgentInfos.getAgentInfo(this.agentId);
			String msgType = cmdJson.getString("msgType");
			String serialId = cmdJson.getString("serialId");
			JSONObject eventData = cmdJson.getJSONObject("eventData");
			if(eventData==null) eventData = new JSONObject();
			String sessionId = cmdJson.getString("sessionId");
			sessionId = StringUtils.isNotBlank(sessionId)?sessionId:eventData.getString("sessionId");
			String event = cmdJson.getString("event");
			String entId = agentInfo.getEntId();

			JSONObject result = new JSONObject();
			result.put("entId", entId);
			result.put("agentId", agentId);
			result.put("messageId", event);
			result.put("type", event);

			JSONObject cmddata = new JSONObject();
			cmddata.put("serialId", serialId);
			cmddata.put("sessionId", sessionId);
			cmddata.put("state", 1);
			cmddata.put("msg", "撤回成功");
			result.put("cmddata", cmddata);

			MessageModel message = ChatContext.getMessage(entId).getMessage(serialId);
			if(message==null) {
				MediaLogger.getLogger().warn("消息撤回失败，消息不存在！");
				cmddata.put("state", 0);
				cmddata.put("msg", "消息撤回失败，消息不存在！");
				result.put("cmddata", cmddata);
				WebSocket.sendMessageToAgent(agentId, result.toJSONString());
				return;
			}
			//超过2分钟的消息不能撤回
			if(System.currentTimeMillis()>message.getMsgTimestamp()+ 60 * 1000) {
				MediaLogger.getLogger().warn("消息撤回失败，截止消息发送时间已超过1分钟，不允许撤回！");
				cmddata.put("state", 0);
				cmddata.put("msg", "消息撤回失败，截止消息发送时间已超过1分钟，不允许撤回！");
				result.put("cmddata", cmddata);
				WebSocket.sendMessageToAgent(this.agentId, result.toJSONString());
				return;
			}

			MsgSenderEnum sendEnum = MsgSenderEnum.getEnumByKey(cmdJson.getString("sender"),MsgSenderEnum.坐席);

			//从缓存中读取用户信息userInfo----yc-mediagw写入
			JSONObject visitorObject = getMediagwVisitor(sessionId);
			String chatSessionId = visitorObject.getString("chatSessionId");
			if (StringUtils.isBlank(chatSessionId)) {
				MediaLogger.getLogger().warn("<websocket> WebSocket.withdraw(),session["+session.getId()+"],agentId["+agentId+"] not found chatSessionId by cache["+Constants.USER_VISITOR+sessionId+"]！");
			}
			MessageModel messageModel = new MessageModel();
			messageModel.setSerialId(serialId);
			messageModel.setAgentId(this.agentId);
			messageModel.setAgentPhone(agentInfo.getAgentPhone());
			messageModel.setAgentName(agentInfo.getAgentName());
//			messageModel.setAgentNickName(agentInfo.getAgentNickName());
			messageModel.setEntId(entId);
			messageModel.setSessionId(sessionId);
			messageModel.setChatSessionId(chatSessionId);
			messageModel.setMsgType(msgType);
			messageModel.setEvent("agent");
			messageModel.setInitialEvent(event);
			messageModel.setWithdraw(1);
			messageModel.setSender(sendEnum.getKey());
			messageModel.setSenderCode(sendEnum.getCode());
			messageModel.setMsgContent("坐席["+agentInfo.getAgentPhone()+"]撤回了一条消息");
			//20210316坐席发送给用户的消息时间以服务器时间为准
			messageModel.setMsgTime(EasyDate.getCurrentTimeStampString());
			MediaProducerBroker.sendUserMessage(sessionId,messageModel.toString());
			WebSocket.sendMessageToAgent(this.agentId, result.toJSONString());

			//更新被撤回的消息
			JSONObject data = new JSONObject();
			data.put("CHAT_ID", serialId);
			data.put("WITHDRAW", 1);
			ChatContext.getMessage(entId).update(data);
		} catch (Exception e) {
			MediaLogger.getLogger().error(e.getMessage(),e);
		}
	}

	protected String  getCommandResult(String agentId,String command){
		String cacheKey = "media_"+command+"_"+agentId;
		String    result =  "";
		for(int i = 0 ;i<20 ;i++){
			try {
				Thread.sleep(200l);
				result  = CacheUtil.get(cacheKey);
				if(StringUtils.isNotBlank(result)){
					break;
				}
			} catch (Exception ex) {
				MediaLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
		MediaLogger.getLogger().info("getCommandResult("+agentId+","+command+"):"+cacheKey+" -> "+result);
		return result;
	}

	/**
	 * 获取当前HTTPSession的ID
	 * @return
	 */
	protected String getHttpSessionId(){
		// 获取存储的 HttpSession
		HttpSession httpSession = (HttpSession) session.getUserProperties().get(HttpSession.class.getName());
		String sessionId = null;
		if (httpSession != null) {
			sessionId = httpSession.getId();
//			MediaLogger.getLogger().info("<websocket> WebSocket.getHttpSessionId(),session["+session.getId()+"],agentId["+agentId+"] HttpSession ID:"+sessionId);
		}
		return sessionId;
	}

	/**
	 * 从缓存中读取用户信息userInfo----yc-mediagw写入
	 * @param sessionId
	 * @return
	 */
	protected JSONObject getMediagwVisitor(String sessionId){
		JSONObject visitorObject = null;
		try {
			String cacheVisotorStr = MediaCacheUtil.get(Constants.USER_VISITOR + sessionId);
			if (StringUtils.isBlank(cacheVisotorStr)) {
				cacheVisotorStr = MediaCacheUtil.get(Constants.CACHE_VISITOR_NAME + sessionId);
			}
			if (StringUtils.isNotBlank(cacheVisotorStr)) {
				visitorObject = JSONObject.parseObject(cacheVisotorStr);
			}

		} catch (Exception e) {
			MediaLogger.getLogger().error("getMediagwVisitor() error for sessionId[" + sessionId + "]: " + e.getMessage(), e);
		}
		if(visitorObject == null){
			visitorObject = new JSONObject();
		}
		return visitorObject;
	}
}