package com.yunqu.cc.robotgw.service.impl.aigc;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.cc.robotgw.base.Constants;
import com.yunqu.cc.robotgw.base.GWConstants;
import com.yunqu.cc.robotgw.log.RobotLogger;
import com.yunqu.cc.robotgw.service.RobotService;
import com.yunqu.cc.robotgw.util.CacheUtil;
import com.yunqu.cc.robotgw.util.Proxy;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.log4j.Logger;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.net.URLEncoder;
import java.util.HashMap;
import java.util.Map;
import java.util.TreeMap;
import java.util.Optional;
import java.util.stream.Collectors;

/**
 * AIGC机器人能力对接
 */
public class AigcRobotService extends RobotService {

    //初始化接口
    private static final String INIT_URL = "/robot/noLogin/out/init/session";

    //问答接口
    private static final String ASK_URL = "/robot/noLogin/out/askQuestion/stream";

    //查询胶囊列表接口-与问答接口一起请求
    private static final String TIPS_URL = "/robot/noLogin/out/api/tips/list";

    //输入引导接口
    private static final String INPUT_GUIDE_URL = "/robot/noLogin/out/api/linkThink";

    //问答评价接口
    private static final String EVAL_URL = "/robot/noLogin/out/accessMes";

    //人机辅助接口
    private static final String ARTIFCIAL_URL = "/robot/noLogin/out/customer/askQuestion/assistance";

    //埋点接口
    protected String CLICK_URL = "/robot/noLogin/out/click/event/save";

    //人机小结接口（暂时无用）
    protected String SUMMARY_URL = "/robot/noLogin/out/chat/summary";

    public AigcRobotService(String channelKey) {
        this.setChannelKey(channelKey);
    }

    private String getChannelCode(){
        String aigcChannelCode = Constants.getAigcChannelCode();
        if(StringUtils.isNotBlank(aigcChannelCode)){
            return aigcChannelCode;
        }
        return getChannelKey();
    }

    @Deprecated
    @Override
    public JSONObject refuseToken(JSONObject json) {
        return null;
    }

    @Override
    public JSONObject robotLogin(JSONObject json) {
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");
        String url = getFullUrl(INIT_URL);
        JSONObject result = createResult(json);
        if(StringUtils.isAnyBlank(userId)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId不能为空！");
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 访客上线接口，请求参数clientId不能为空！");
            return result;
        }

        try {
            JSONObject apiData = getApiJson(json);
//            apiData.put("userId",userId);
//            apiData.put("userName",StringUtils.isNotBlank(json.getString("nickname"))? URLEncoder.encode(json.getString("nickname"), GWConstants.ENCODE_UTF8):"");
//            apiData.put("skillId",json.getOrDefault("keyCode",""));
//            JSONPath.set(apiData,"$.extraInfo.yunqu.outSkillId",json.getOrDefault("keyCode",""));
//            JSONPath.set(apiData,"$.extraInfo.yunqu.user.openId",json.getOrDefault("openId",""));
//            JSONPath.set(apiData,"$.extraInfo.yunqu.user.uin",json.getOrDefault("uin",""));
//            JSONPath.set(apiData,"$.extraInfo.yunqu.user.membership",json.getOrDefault("userRoleId",""));

            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 访客上线接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 访客上线接口，返回："+rJson);
            JSONObject respData = (JSONObject) JSONPath.eval(rJson, "$.data");
            if(respData==null){
                result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
                result.put("respDesc", "上线失败，接口返回data为空！");
                this.getLogger().error("Thread["+thid+"] userId["+userId+"] 访客上线接口，接口返回data为空！");
                return result;
            }

            JSONArray welcomeCardList = new JSONArray();
            JSONArray topQuestions = respData.getJSONArray("topQuestions");
            if(topQuestions!=null && !topQuestions.isEmpty()){
                for (int i = 0; i < topQuestions.size(); i++) {
                    JSONObject cardObj = topQuestions.getJSONObject(i);

                    JSONObject newCardObj = new JSONObject();
                    JSONArray newQuestions = new JSONArray();
                    newCardObj.put("id",cardObj.getString("categoryId"));
                    newCardObj.put("name",cardObj.getString("categoryName"));
                    newCardObj.put("pic",cardObj.getString("categoryIconUrl"));
                    newCardObj.put("questions",newQuestions);
                    welcomeCardList.add(newCardObj);
                    //分类下的问题列表
                    JSONArray questions = cardObj.getJSONArray("questions");
                    if(questions!=null && !questions.isEmpty()){
                        for (int j = 0; j < questions.size(); j++) {
                            JSONObject questionObj = questions.getJSONObject(j);
                            JSONObject newQuestionObj = new JSONObject();
                            newQuestionObj.put("knowledgeId",questionObj.getString("questionId"));
                            newQuestionObj.put("id",questionObj.getString("questionId"));
                            newQuestionObj.put("question",questionObj.getString("content"));
                            newQuestionObj.put("type","1");
                            newQuestions.add(newQuestionObj);
                        }
                    }
                }
                respData.remove("topQuestions");
                respData.put("welcomeCardList",welcomeCardList);
            }

            //组装底部胶囊
            JSONArray tipList = respData.getJSONArray("tips");
            if(tipList==null){
                tipList = respData.getJSONArray("tipList");
            }
            respData.put("welcomeCapsuleList",parseCapsuleList(tipList));
            respData.remove("tipList");
            respData.remove("tips");

            //组装答案不满意原因条目（answerUselessItems）
            JSONArray answerDissatisfiedItems = respData.getJSONArray("answerDissatisfiedItems");
            if(answerDissatisfiedItems!=null && !answerDissatisfiedItems.isEmpty()){
                for (int i = 0; i < answerDissatisfiedItems.size(); i++) {
                    JSONObject itemObj = answerDissatisfiedItems.getJSONObject(i);
                    itemObj.put("id",itemObj.remove("itemId"));
                    itemObj.put("cause",itemObj.remove("itemName"));
                }
                respData.remove("answerDissatisfiedItems");
                respData.put("answerUselessItems",answerDissatisfiedItems);
            }

            //缓存访客接入的keyCode
            String keyCode = json.getString("keyCode");
            if(StringUtils.isNotBlank(keyCode)){
                respData.put("keyCode",keyCode);
            }
            this.addIntProperty(userId, respData);

            //初始化成功后返回欢迎语列表到全媒体，用户显示猜你想问
            result.put("welcomeMsgContent", respData.getString("welcomeClause"));
            result.put("welcomeCardList", respData.get("welcomeCardList"));
            result.put("welcomeCapsuleList", respData.get("welcomeCapsuleList"));
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "处理失败："+e.getMessage());
        }
        return result;
    }

    @Override
    public JSONObject robotLogout(JSONObject json) {
        String userId = json.getString("clientId");
        this.delIntProperty(userId);
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Override
    public JSONObject robotAsk(JSONObject json) {
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");
        String url = getFullUrl(ASK_URL);
        JSONObject result = createResult(json);
        if(StringUtils.isAnyBlank(userId)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId不能为空！");
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 问答接口，请求参数clientId不能为空！");
            return result;
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
//            apiData.put("questionId",json.getOrDefault("serialId",RandomKit.uniqueStr()));
            apiData.put("questionContent",json.getOrDefault("question",""));
            //引导胶囊-要异步请求接口
            final String tipsKey = "getTips:" + json.getString("clientId") + ":" + json.getString("serialId");
            new Thread(() -> {
                JSONArray tips = getCapsuleList(json);
                CacheUtil.put(tipsKey,JSONObject.toJSONString(tips),10);
            }).start();

            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 问答接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 问答接口，返回："+rJson);
            JSONObject respData = (JSONObject) JSONPath.eval(rJson, "$.data");

            //answerEvent:  robot -机器人回复   seat-要求转人工
            String answerEvent = (String) respData.getOrDefault("answerEvent","robot");
            rJson.put("serviceType", answerEvent);

            //转人工，直接返回
            if("seat".equals(answerEvent)){
                result.put("serviceType", answerEvent);
                result.put("respCode", GWConstants.RET_CODE_SUCCESS);
                result.put("respDesc", "问答交互中触发转人工！");
                return result;
            }

            //主线程最多保持2秒钟，获取异步请求引导胶囊接口响应结果
            JSONArray capsuleList = null;
            int count = 0;
            while (count < 100){
                String o = CacheUtil.get(tipsKey);
                if(o!=null){
                    capsuleList = JSONObject.parseArray(o);
                    break;
                }
                count++;
                Thread.sleep(20);
            }

            //每种消息的基本信息
            String aId = respData.getString("aiQAId");//答案ID
            String cluid = respData.getString("sessionId");
            boolean backFill = respData.getBoolean("backFill");//是否兜底话术，true 兜底，false 机器人正常匹配
            String msgType = respData.getString("answerTextType"); //返回的消息类型,text,html,richText,json
            String answerTextStr = respData.getString("answerText");//返回的消息内容，当answerTextType=json是，内容是一个json格式字符串

            JSONObject answer = new JSONObject();
            result.put("answer", answer);
            answer.put("aid", aId);
            answer.put("cluid", cluid);
            answer.put("content", answerTextStr);

            JSONObject answerTextObj = new JSONObject();
            if ("json".equals(msgType)) {
                answerTextObj.putAll(JSONObject.parseObject(answerTextStr));
                msgType = answerTextObj.getString("type");
            }

            //是否显示 点赞、点踩，兜底话术 和 反问引导(msgtype=guide)答案类型不需要显示
            if(StringUtils.isNotBlank(aId) && !backFill && !"guide".equals(msgType)) {
                answer.put("evaluation", "Y");
                answer.put("answerUselessItems", intProperty.get("answerUselessItems"));
            }else {
                answer.put("evaluation", "N");
            }

            //引导胶囊
            if(capsuleList!=null&& !capsuleList.isEmpty()) {
                answer.put("capsules", capsuleList);
            }

            answer.put("msgType", "html".equals(msgType)?"richText":msgType);//html类型转换成richText，与云问保持一致


            // 优化消息类型处理结构
            switch(msgType) {
                case "text":
                    handleTextMessage(answer, answerTextObj.getJSONObject("data"), answerTextStr);
                    break;
                case "richText":
                    handleRichTextMessage(answer, answerTextObj.getJSONObject("data"), answerTextStr);
                    break;
                case "image":
                case "voice":
                case "video":
                case "file":
                    handleMediaMessage(msgType, answer, answerTextObj.getJSONObject("data"));
                    break;
                case "graphic":
                    handleGraphicMessage(answer, answerTextObj,answerTextStr);
                    break;
                case "doc":
                case "document":
                    handleDocumentMessage(answer, answerTextObj.getJSONObject("data"));
                    break;
                case "guide":
                case "guidance":
                    handleGuideMessage(answer, answerTextObj.getJSONObject("data"), answerTextStr);
                    break;
                case "cssData":
                    handleCssDataMessage(answer, answerTextObj.getJSONObject("data"));
                    break;
                default:
                    handleUnknownMessage(thid, userId, msgType, answer);
                    break;
            }

            //组装推荐问题列表
            JSONArray suggestedList = respData.getJSONArray("suggestedList");
            if(suggestedList!=null && !suggestedList.isEmpty()){
                JSONObject recommendQuestion = new JSONObject();
                JSONArray questions = new JSONArray();
                recommendQuestion.put("beforeWord","您可能还关心以下问题：");
                recommendQuestion.put("afterWord","");
                recommendQuestion.put("list",questions);
                for (int i = 0; i < suggestedList.size(); i++) {
                    JSONObject item = new JSONObject();
                    item.put("question",suggestedList.get(i));
                    questions.add(item);
                }
                answer.put("recommendQuestion",recommendQuestion);
            }

            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
//            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//            result.put("respDesc", "处理失败："+e.getMessage());
            //处理异常转人工
            result.put("serviceType", "seat");
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "问答交互接口处理失败触发转人工！");
        }
        return result;
    }

    /**
     * 格式化胶囊列表
     * @param tips
     * @return
     */
    private JSONArray parseCapsuleList(JSONArray tips){
        JSONArray welcomeCapsuleList = new JSONArray();
        if(tips==null || tips.isEmpty()){
            return welcomeCapsuleList;
        }
        for (int i = 0; i < tips.size(); i++) {
            JSONObject tipObj = tips.getJSONObject(i);
            //分类下的问题列表
            JSONArray tipContentList = tipObj.getJSONArray("tipContentList");
            if(tipContentList==null || tipContentList.isEmpty()){
                continue;
            }
            for (int j = 0; j < tipContentList.size(); j++) {
                JSONObject tipItemObj = tipContentList.getJSONObject(j);
                JSONObject newTipItemObj = new JSONObject();
                //胶囊分类信息
                newTipItemObj.put("tipId",tipObj.getString("tipId"));
                newTipItemObj.put("tipName",tipObj.getString("tipName"));
                newTipItemObj.put("tipType",tipObj.getString("tipType"));
                newTipItemObj.put("tipIcon",tipObj.getString("tipIcon"));
                newTipItemObj.put("tipGroupName",tipObj.getString("tipGroupName"));
                //胶囊信息
                newTipItemObj.put("id",tipItemObj.getString("tipQuestionId"));
                newTipItemObj.put("questionName",tipItemObj.getString("tipQuestionContent"));
                newTipItemObj.put("name",tipItemObj.getString("tipQuestionContent"));
                welcomeCapsuleList.add(newTipItemObj);
            }
        }
        return welcomeCapsuleList;
    }

    /**
     * 获取胶囊列表
     * @param json
     * @return
     */
    private JSONArray getCapsuleList(JSONObject json){
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");
        String url = getFullUrl(TIPS_URL);
        if(StringUtils.isAnyBlank(userId)){
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 获取胶囊列表接口，请求参数clientId不能为空！");
            return new JSONArray();
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
            apiData.put("questionContent",json.getOrDefault("question",""));
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 获取胶囊列表接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 获取胶囊列表接口，返回："+rJson);
            //组装底部胶囊
            return parseCapsuleList(rJson.getJSONArray("data"));
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
        }
        return new JSONArray();
    }


    @Override
    public JSONObject robotAgentAsk(JSONObject json) {
        return robotAgentArtificial(json);
    }

    @Override
    public JSONObject robotInputGuide(JSONObject json) {
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");
        String question = json.getString("question");
        String url = getFullUrl(INPUT_GUIDE_URL);
        JSONObject result = createResult(json);
        if(StringUtils.isAnyBlank(userId,question)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId不能为空！");
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 输入引导接口，请求参数clientId,question不能为空！");
            return result;
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
            apiData.put("question",question);

//            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 输入引导接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
//            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 输入引导接口，返回："+rJson);
            JSONArray jsonArray = rJson.getJSONArray("data");
            JSONArray newInputGuide = new JSONArray();
            if(jsonArray!=null&&!jsonArray.isEmpty()){
                for (int i = 0; i < jsonArray.size(); i++) {
                    JSONObject obj = jsonArray.getJSONObject(i);
                    obj.put("itemId",obj.getString("questionId"));
                    obj.put("showContent",obj.getString("questionContent"));
                    obj.put("content",obj.getString("questionContent"));
                    obj.put("type",obj.getString("questionType"));
                    newInputGuide.add(obj);
                }
            }
            result.put("inputGuide", newInputGuide);
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "处理失败："+e.getMessage());
        }
        return result;
    }

    /**
     * 机器人答案评价
     * @param json
     * @return
     */
    @Override
    public JSONObject robotEval(JSONObject json){
        long thid = Thread.currentThread().getId();
        String evaluation = json.getString("evaluation"); //0-满意 1-不满意

        String userId = json.getString("clientId");
        String question = json.getString("question");
        String url = getFullUrl(EVAL_URL);
        JSONObject result = createResult(json);
        String serialId = json.getString("serialId");
        String answerId = json.getString("answerId");//答案id，答案响应结果中的aid
        String cluid = json.getString("cluid");//问答机器人返回答案中的 cluid
        String causeId = json.getString("causeId");//不满意原因唯一标识
        String content = json.getString("content");//评价内容

        //判断参数
        if(StringUtils.isAnyBlank(userId,answerId,cluid)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId、answerId、sourceId、cluid不能为空！");
            this.getLogger().error("Thread["+thid+"] clientId["+userId+"]问答评价接口：请请求参数clientId、answerId、sourceId、cluid不能为空,"+json.toJSONString());
            return result;
        }

        //点踩时会触发埋点接口，会提交不满意评价，aigc目前不支持这种方式提交满意度
        if("6".equals(json.getString("tagType"))){
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            this.getLogger().warn("Thread["+thid+"] clientId["+userId+"]问答评价接口：点踩触发提交不满意评价，不处理！");
            result.put("respDesc", "操作成功！");
            return result;
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
            apiData.put("aiQAId",answerId);
            apiData.put("satisfiedFlag","0".equals(evaluation) ? "SATISFIED" : "NOT_SATISFIED");//类型
            apiData.put("feedBackTag",causeId);
            apiData.put("feedBackContent",content);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 问答评价接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  userId, apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 问答评价接口，返回："+rJson);
            JSONObject respData = new JSONObject();

//            String answerEvent = respData.getString("answerEvent");
//            if(StringUtils.isNotBlank(answerEvent)){
//                respData.put("serviceType", answerEvent);
//            }
            respData.put("message", rJson.getOrDefault("data","谢谢您的支持~我会更努力哒！"));
            result.put("answer",respData);
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "处理失败："+e.getMessage());
        }
        return result;
    }

    @Override
    public JSONObject robotSummary(JSONObject json) {
//        long thid = Thread.currentThread().getId();
//        String userId = json.getString("clientId");
//        String url = getFullUrl(SUMMARY_URL);
//        JSONObject result = createResult(json);
//        if(StringUtils.isAnyBlank(userId)){
//            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
//            result.put("respDesc", "请求参数clientId不能为空！");
//            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 机器人小结接口，请求参数clientId不能为空！");
//            return result;
//        }
//
//        try {
//            JSONObject intProperty = getIntProperty(userId);
//            JSONObject apiData = getApiJson(json);
//            apiData.put("sessionId",intProperty.getString("sessionId"));
//
//            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 机器人小结接口，body参数："+apiData);
//            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
//            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 机器人小结接口，返回："+rJson);
//            JSONObject respData = rJson.getJSONObject("data");
//            if(respData!=null && respData.containsKey("summary")){
//                result.put("robotSummary", "客户咨询机器人小结信息:\n" + respData.getString("summary"));
//            }
//            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
//            result.put("respDesc", "处理成功");
//        } catch (Exception e) {
//            this.getLogger().error("处理失败："+e.getMessage(),e);
//            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
//            result.put("respDesc", "处理失败："+e.getMessage());
//        }
//        return result;

        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Override
    public JSONObject matchScores(JSONObject json) {
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Override
    public JSONObject bottomNavDetail(JSONObject json) {
        return robotAsk(json);
    }

    @Deprecated
    @Override
    public JSONObject welcomeCardDetail(JSONObject json) {
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Override
    public Logger getLogger() {
        return RobotLogger.getAigcLogger();
    }

    /**
     * 人机辅助入口
     * @param json
     * @return
     */
    @Override
    public JSONObject robotAgentArtificialEntrance(JSONObject json){
        long thid = Thread.currentThread().getId();
        String clientId = json.getString("clientId");

        JSONObject result = null;
        //坐席辅助-机器人类型
        String robotAgentType = getRobotAgentType();

        if(StringUtils.equals("midea", robotAgentType)) {//美的机器人
            result = robotAgentAsk2(json);
            result.put("robotAgentType", robotAgentType);

//        }else if(StringUtils.equals("yunwen", robotAgentType)) {//云问机器人
//            result = robotAgentArtificial(json);
        }else if(StringUtils.equals("aigc", robotAgentType)) {//AIGC
            result = robotAgentArtificial(json);
            result.put("robotAgentType", "yunwen");
        }else {
            result = new JSONObject();
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "渠道未配置机器人类型");
            this.getLogger().error("Thread["+thid+"] clientId["+clientId+"]人机辅助接口参数错误：渠道未配置机器人类型,"+json.toJSONString());
        }
        return result;

    }
    /**
     * 对接美的上海AI在线机器人-坐席辅助接口
     * @param json
     * @return
     */
    @Deprecated
    public JSONObject robotAgentAsk2(JSONObject json){
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }


    @Override
    public JSONObject robotAgentArtificial(JSONObject json) {
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");
        String question = json.getString("question");
        String url = getFullUrl(ARTIFCIAL_URL);
        JSONObject result = createResult(json);
        if(StringUtils.isAnyBlank(userId,question)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId,question不能为空！");
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 人机辅助接口，请求参数clientId,question不能为空！");
            return result;
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
            apiData.put("questionContent",question);

            String artificialId = getArtificialId();
            this.getLogger().info("Thread["+thid+"] userId["+userId+"]人机辅助接口 artificialId :"+artificialId);
            if(StringUtils.isNotBlank(artificialId)){
                apiData.put("channelCode",artificialId);
            }

            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 人机辅助接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  json.getString("clientId"), apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 人机辅助接口，返回："+rJson);
            JSONObject respData = rJson.getJSONObject("data");
//            组装前
//            {
//                "code": 0,
//                "data": {
//                      "aiQAId": "IOP_QA_xOtH_28602942296",
//                      "answerText": "亲亲，此款KFR-51LW/N8MJA3智行II 新三级能效立式柜机 1、节能新升级，新能效，更省电 2、一键"智控温"，达到舒适温度，自动升温至25°-28°，呵护一时贪凉的你，不着凉； 3、高频快速冷暖，以专利支撑，40s速冷，80s速热，回个信息的功夫，温度就舒服了； 4、55°C高温制冷，-32°C低温制热，在恶劣环境下仍然能保持优良的状态； 5、APP智能控制，身在何处都能掌控自如",
//                      "answerTextType": "richText",
//                      "backFill": false,
//                      "noAuthSources": [],
//                      "sessionId": "IOP_SESSION_HHzG_28077644313",
//                      "sources": [],
//                      "stream": false
//                },
//                "msg": "success",
//                "success": true
//            }
//            组装后
//            {
//                "code": 1,
//                {
//                    "data": {
//                        "directAnswer": {
//                            "question": "冰箱不制冷或制冷效果差",
//                            "answerType": 0,
//                            "className": "故障类1149",
//                            "classId": 77007,
//                            "knowledgeId": 811725,
//                            "faqAnswer": [
//                                {
//                                    "knowledgeLabels": [],
//                                    "answerString": "<p style=\"text-wrap-mode: wrap;\"><strong>您好，</strong><strong>冰箱/冰柜不制冷或效果差的原因及处理方法可点击→<a text=\"【冰箱不制冷】\" href=\"https://ccrobot.midea.com:8082/upload/material/1/20220607/C518955446DE4798AB3B10AEBFED83DA.mp4\" target=\"_blank\" faqtitle=\"【冰箱不制冷】\" rel=\"noreferrer\" referrerpolicy=\"no-referrer\"><strong><span style=\"color: rgb(0, 112, 192);\">【冰箱不制冷】</span></strong></a>&nbsp;<strong>查看视频</strong>或参考以下<strong>排除</strong>方法：</strong></p><p style=\"text-wrap-mode: wrap;\"><span style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑;\">1.确认冰箱的温度是否调节好，根据环境和里面放的食物来调节，可参考视频</span><a text=\"只支持选中一个链接时生效\" href=\"https://ccrobot.midea.com:8082/upload/material/1/20240920/C8B18168B0DC4B1C937491CCB41065EF.mp4\" target=\"_blank\" title=\"冰箱按键档位调节方法\" rel=\"noreferrer\" referrerpolicy=\"no-referrer\" style=\"color: rgb(0, 112, 192);\"><strong><span style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑;\">【冰箱按键档位调节方法】</span></strong></a><span style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑;\">或<a text=\"【冰箱温度调节方法】\" href=\"https://ccrobot.midea.com:8082/upload/material/1/20220317/5E0384DBA49146908B33FBE1A2C4F35D.mp4\" target=\"_self\" title=\"冰箱温度调节方法\" faqtitle=\"【冰箱温度调节方法】\" rel=\"noreferrer\" referrerpolicy=\"no-referrer\"><span style=\"color: rgb(0, 112, 192);\"><strong>【冰箱温度调节方法】</strong></span></a>；</span><br/></p><p style=\"text-wrap-mode: wrap;\">2.<span style=\"font-family: &quot;Microsoft YaHei&quot;, 微软雅黑;\">冬季，冰箱所处环境温度低于10℃时，如果冬季补偿开关未打开，也会影响制冷效果，</span>带补偿开关的机型需打开补偿开关哦。</p><p style=\"text-wrap-mode: wrap;\"><br/></p><p style=\"text-wrap-mode: wrap;\">※小tips：如仍有异常，需要联系售后检测，您可点击→<span style=\"color: rgb(0, 112, 192);\"><strong>【美的官方自助报修链接】</strong></span>填写信息提交后，由售后联系您处理。</p>",
//                                    "channelNames": ["H5"],
//                                    "answerType": 0,
//                                    "channelIds": [4],
//                                    "roleNames": []
//                                }
//                            ]
//
//                        },
//                        "uuid": "fb872ed1-5b54-4bdb-b921-252c1fd33679",
//                        "answerMode": 0
//                    }
//                },
//                "message": "请求成功!",
//                "debug": null,
//                "success": true
//            }

            JSONObject resultObj = new JSONObject();
            resultObj.put("code",1);
            resultObj.put("success",true);
            resultObj.put("message","请求成功!");
            JSONObject data = new JSONObject();
            resultObj.put("data",data);

            JSONObject directAnswer = new JSONObject();
            data.put("directAnswer",directAnswer);
            data.put("answerMode",0);

            directAnswer.put("answerType",0);
            directAnswer.put("className","");
            directAnswer.put("classId","");
            directAnswer.put("question",question);
            directAnswer.put("knowledgeId",respData.getString("aiQAId"));

            JSONArray faqAnswer = new JSONArray();
            directAnswer.put("faqAnswer",faqAnswer);
            JSONObject faqAnswerItem = new JSONObject();
            faqAnswer.add(faqAnswerItem);
            faqAnswerItem.put("answerType",respData.getString("answerTextType"));
            faqAnswerItem.put("answerString",respData.getString("answerText"));

            result.put("answer", resultObj);
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 人机辅助接口处理后返回："+result);

        } catch (Exception e) {
            this.getLogger().error("Thread["+thid+"] userId["+userId+"] 人机辅助接口处理失败："+e.getMessage());
            this.getLogger().error(e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "处理失败："+e.getMessage());
        }
        return result;
    }

    @Override
    public JSONObject doTrigger(JSONObject json) {
        long thid = Thread.currentThread().getId();
        String userId = json.getString("clientId");

        String url = getFullUrl(CLICK_URL);
        JSONObject result = createResult(json);
        String tagType = json.getString("tagType");//点击类型：1：引导胶囊，2：猜你想问，3：机器人问题列表，4：服务专区，5：机器人答案点赞，6：机器人答案点踩，7：机器人答案中的链接，8：换一批，9：猜你想问卡片，10：服务专区卡片，11：点击加载历史，12：功能-麦克风，
        //13：功能-表情，14：功能-扩展，15：输入框，21：工单滑动，22：展开工单产品，23：工单进度，24：催促上门，25：联系售后，26：补充信息，27：取消服务，28：修改时间，99其他。
        String tagName = json.getString("tagName");//目标名称
        String ptagName = json.getString("ptagName");//目标父级级名称

        //判断参数
        if(StringUtils.isAnyBlank(userId,tagType,tagName)){
            result.put("respCode", GWConstants.RET_CODE_NOT_DATA);
            result.put("respDesc", "请求参数clientId,tagType,tagName不能为空！");
            this.getLogger().error("Thread["+thid+"] clientId["+userId+"]埋点接口：请请求参数clientId,tagType,tagName不能为空,"+json.toJSONString());
            return result;
        }
//        猜你想问（HOT_QUESTION）、知识锦囊（KNOWLEDGE_KIT）、输入联想（LINK_PROBLEM）、快捷通道（FAST_TRACK）等等。
        String clickType = "HOT_QUESTION";
        if("1".equals(tagType)){
            clickType = "KNOWLEDGE_KIT";
        }else if("2".equals(tagType)){
            clickType = "HOT_QUESTION";
        }else if("3".equals(tagType)){
            clickType = "LINK_PROBLEM";
        }else if("4".equals(tagType)){
            clickType = "FAST_TRACK";
        }

        try {
            JSONObject intProperty = getIntProperty(userId);
            JSONObject apiData = getApiJson(json);
            apiData.put("sessionId",intProperty.getString("sessionId"));
            apiData.put("type",clickType);//埋点类型：猜你想问（HOT_QUESTION）、知识锦囊（KNOWLEDGE_KIT）、输入联想（LINK_PROBLEM）、快捷通道（FAST_TRACK）等等。
            apiData.put("questionContent",tagName);//问题名称，对应欢迎语卡片列表数据中：data.property.welcomeCardList[n].questions[y].question
            apiData.put("questionCategory",ptagName);//问题卡片名称，对应欢迎语卡片列表数据中：data.property.welcomeCardList[n].name
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 埋点接口，body参数："+apiData);
            JSONObject rJson = requestRoobt(url,  userId, apiData);
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] 埋点接口，返回："+rJson);
            result.put("respCode", GWConstants.RET_CODE_SUCCESS);
            result.put("respDesc", "处理成功");
        } catch (Exception e) {
            this.getLogger().error("处理失败："+e.getMessage(),e);
            result.put("respCode", GWConstants.RET_CODE_OTHER_EXCEPTION);
            result.put("respDesc", "处理失败："+e.getMessage());
        }
        return result;
    }

    @Deprecated
    @Override
    public JSONObject chatUserRoleSave(JSONObject json) {
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Deprecated
    @Override
    public JSONObject chatUserRoleDel(JSONObject json) {
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    @Deprecated
    @Override
    public JSONObject cssOrderCmd(JSONObject json) {
        JSONObject result = createResult(json);
        result.put("respCode", GWConstants.RET_CODE_SUCCESS);
        result.put("respDesc", "处理成功");
        return result;
    }

    /**
     * 创建请求参数对象
     * @param json
     * @return
     * @throws Exception
     */
    protected JSONObject getApiJson(JSONObject json) throws Exception {
        String clientId = json.getString("clientId");
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("questionId",json.getOrDefault("serialId",RandomKit.uniqueStr()));//请求流水id
        jsonObject.put("channelCode",getChannelCode());
        jsonObject.put("multiLang","zh-Hans");//语种(默认中文)-枚举：中文：zh-Hans，英文：en，越南语：vi，马来语：ms，印尼语：id，阿拉伯语：ar，泰语：th，西班牙语：es，葡萄牙语：pt，俄语：ru， 法语：fr，德语：de，意大利语：it，荷兰语：nl，瑞典语：sv，希腊语：el，波兰语：pl，匈牙利语：hu，捷克语：cs
        jsonObject.put("userId", clientId);
        jsonObject.put("userName",StringUtils.isNotBlank(json.getString("nickname"))? URLEncoder.encode(json.getString("nickname"), GWConstants.ENCODE_UTF8):"");
//        jsonObject.put("skillId",json.getOrDefault("skillGroupId",""));//技能id
        String keyCode = (String) json.getOrDefault("keyCode", "");
        if(StringUtils.isBlank(keyCode)){
            keyCode = getIntProperty(clientId).getString("keyCode");
        }
        JSONPath.set(jsonObject,"$.extraInfo.yunqu.outSkillId", keyCode);//按键
        JSONPath.set(jsonObject,"$.extraInfo.yunqu.user.openId",json.getOrDefault("clientId",""));
        JSONPath.set(jsonObject,"$.extraInfo.yunqu.user.uin",json.getOrDefault("uin",""));
        JSONPath.set(jsonObject,"$.extraInfo.yunqu.user.membership",json.getOrDefault("userRoleId",""));
        return jsonObject;
    }

    protected String getFullUrl(String api){
        return getHostName() + api;
    }

    /**
     * 获取签名（请求头不带 signVersion(不建议)）
     * 每次请求都要生成对应的签名
     * @param bodyData 请求body参数
     * @return
     */
    protected String generateSign(Map<String,String> bodyData){
        long thid = Thread.currentThread().getId();
        getLogger().debug("Thread["+thid+"] 生成签名，body参数："+JSONObject.toJSONString(bodyData));
        try {
            TreeMap<String, String> tmp = new TreeMap<>(bodyData);
            StringBuilder sb = new StringBuilder();
            for (Map.Entry<String, String> entry : tmp.entrySet()) {
                Object value = entry.getValue();
                // 参数值为空，不加入验签
                if (value == null || StringUtils.isEmpty(value.toString())) {
                    continue;
                }
                sb.append(entry.getKey()).append("=").append(value).append("&");
            }
            // 明文
            String plaintext = sb.length() > 1 ? sb.deleteCharAt(sb.length()- 1).toString() : "";
            // md5
            String data = DigestUtils.md5Hex(plaintext);
            // 密文
            return SignUtil.sign(data.getBytes());
        } catch (Exception e) {
            getLogger().error("Thread["+thid+"] 生成签名失败！"+e.getMessage());
            getLogger().error(e,e);
        }
        return "";
    }
    /**
     * 获取签名（请求头参数signVersion=2）
     * 每次请求都要生成对应的签名
     * @param bodyStr 请求body字符串
     * @return
     */
    protected String generateSign2(String bodyStr){
        long thid = Thread.currentThread().getId();
        getLogger().debug("Thread["+thid+"] 生成签名，body参数："+bodyStr);
        try {
            // md5
            String data = DigestUtils.md5Hex(bodyStr);
            // 密文
            return SignUtil.sign(data.getBytes());
        } catch (Exception e) {
            getLogger().error("Thread["+thid+"] 生成签名失败！"+e.getMessage());
            getLogger().error(e,e);
        }
        return "";
    }

    /**
     * 请求头
     * @param bodyStr 请求body参数
     * @return
     */
    protected Map<String,String> getReqHeaders(String bodyStr){
        Map<String,String> headers = new HashMap<>();
        headers.put("sign", generateSign2(bodyStr));//本次请求的签名
        headers.put("appId",getAppId());
        headers.put("signType",Constants.getAigcSignType());//inner ，outer
        headers.put("signVersion","2");//建议：2
        return headers;
    }

    protected JSONObject requestRoobt(String url,String userId,JSONObject reqData) throws Exception{
        long thid = Thread.currentThread().getId();
        long timeMillis = System.currentTimeMillis();

        String bodyStr = reqData.toJSONString();
        Map<String,String> headers = getReqHeaders(bodyStr);
        getLogger().debug("Thread["+thid+"] 请求头："+JSONObject.toJSONString(headers));
        getLogger().info("Thread["+thid+"] 请求地址："+url);
        String responseStr = Proxy.doPostJson(url,reqData,headers,5000,5000);
        long reqTime = System.currentTimeMillis() - timeMillis;
        if(reqTime > 2000){
            this.getLogger().warn("Thread["+thid+"] userId["+userId+"] AIGC接口响应时间大于2秒（"+reqTime+"ms），"+url+" ！");
        }
        if(StringUtils.isBlank(responseStr)){
            throw new Exception("Thread["+thid+"] userId["+userId+"] AIGC接口返回结果为空！");
        }

        JSONObject rJson = JSONObject.parseObject(responseStr);

        String code = rJson.getString("code");
        if("0".equals(code)) {
            return rJson;
        }
        this.getLogger().warn("Thread["+thid+"] userId["+userId+"] url["+url+"] AIGC接口返回结果：" + responseStr);
        throw new Exception("Thread["+thid+"] userId["+userId+"] AIGC接口返回失败：" + rJson.getString("msg"));
    }

    // 新增工具方法
    private void handleMediaMessage(String msgType, JSONObject answer, JSONObject data) {
        long thid = Thread.currentThread().getId();
        String userId = answer.getString("cluid");

        answer.put("content", data.getString("content"));
        answer.put("format", data.getString("format"));
        answer.put("url", data.getString("url"));

        if("voice".equals(msgType)) {
            answer.put("bakup", data.getString("recognition"));
        }

        answer.put("mediaContent", data);
        this.getLogger().info("Thread["+thid+"] userId["+userId+"] 处理" + msgType + "类型消息");
    }

    private void handleGraphicMessage(JSONObject answer, JSONObject data, String defaultContent) {
        JSONArray graphicList = data.getJSONArray("data");
        if(graphicList != null && !graphicList.isEmpty()) {
            JSONObject articlesJson = graphicList.getJSONObject(0);
            String description = articlesJson.getString("description");
            String picurl = articlesJson.getString("picurl");
            String title = articlesJson.getString("title");
            String url2 = articlesJson.getString("url");
            answer.put("content", title);
            answer.put("picurl", picurl);
            answer.put("url", url2);
            answer.put("bakup", description);
//            answer.put("mediaContent", graphicList);
        }else{
            answer.put("content", defaultContent);
            answer.put("msgType", "text");
        }
    }

    private void handleDocumentMessage(JSONObject answer, JSONObject data) {
        String docUrl = data.getString("url");
        if(StringUtils.isNotBlank(docUrl)) {
            String linkText = String.format("<a href=\"%s\">%s%s</a>",
                    docUrl,
                    data.getString("name"),
                    data.getString("format"));
            answer.put("content", linkText);
            answer.put("msgType", "text");
        }
        answer.put("mediaContent", data);
    }

    private void handleGuideMessage(JSONObject answer, JSONObject data, String answerText) {
        JSONObject guideObj = new JSONObject();
        guideObj.put("beforeWord", data.getString("beforeWord"));
        guideObj.put("afterWord", data.getString("afterWord"));

        JSONArray list = Optional.ofNullable(data.getJSONArray("list"))
                .orElseGet(JSONArray::new);

        JSONArray formattedList = list.stream()
                .map(JSONObject.class::cast)
                .map(item -> {
                    JSONObject newItem = new JSONObject();
                    newItem.put("seq", item.getOrDefault("seq", "1"));
                    newItem.put("question", item.getString("question"));
                    return newItem;
                })
                .collect(Collectors.toCollection(JSONArray::new));

        guideObj.put("list", formattedList);
        answer.put("recommendQuestion", guideObj);
        answer.put("msgType", "text");
        answer.put("content", buildGuideContent(guideObj, answerText));
    }

    private String buildGuideContent(JSONObject guideObj, String defaultContent) {
        StringBuilder content = new StringBuilder();
        if(guideObj.containsKey("beforeWord")) {
            content.append(guideObj.getString("beforeWord")).append("\n");
        }

        JSONArray list = guideObj.getJSONArray("list");
        for(int i=0; i<list.size(); i++) {
            JSONObject item = list.getJSONObject(i);
            content.append(item.getString("seq"))
                    .append(". ")
                    .append(item.getString("question"))
                    .append("\n");
        }

        if(guideObj.containsKey("afterWord")) {
            content.append(guideObj.getString("afterWord"));
        }
        return content.length() > 0 ? content.toString() : defaultContent;
    }

    // 补充缺失的消息处理方法
    private void handleTextMessage(JSONObject answer, JSONObject data, String defaultContent) {
        String content = data!=null && data.containsKey("content") ? data.getString("content") : defaultContent;
        answer.put("content", content);
    }

    private void handleRichTextMessage(JSONObject answer, JSONObject data, String defaultContent) {
        if(data==null||data.isEmpty()) {
            answer.put("content", defaultContent);
            answer.put("mediaContent", new JSONObject());
            return;
        }
        String content = data.containsKey("content") ? data.getString("content")
                : data.containsKey("original") ? data.getString("original")
                : defaultContent;
        answer.put("content", content);
        answer.put("mediaContent", data);

        // 处理推荐问题
//        if(data.containsKey("recommendQuestion")) {
//            JSONObject recommend = data.getJSONObject("recommendQuestion");
//            StringBuilder recommendContent = new StringBuilder();
//            if(recommend.containsKey("beforeWord")) {
//                recommendContent.append(recommend.getString("beforeWord")).append("\n");
//            }
//
//            JSONArray list = recommend.getJSONArray("list");
//            for(int i=0; i<list.size(); i++) {
//                JSONObject item = list.getJSONObject(i);
//                recommendContent.append(item.getString("seq"))
//                               .append(".")
//                               .append(item.getString("question"))
//                               .append("\n");
//            }
//            if(recommend.containsKey("afterWord")) {
//                recommendContent.append(recommend.getString("afterWord"));
//            }
//            answer.put("content", answer.getString("content") + "\n" + recommendContent);
//        }
    }

    private void handleCssDataMessage(JSONObject answer, JSONObject data) {
        long thid = Thread.currentThread().getId();
        String userId = answer.getString("cluid");

        String defContent = "抱歉，系统出现异常，您可尝试重新操作或用文字描述一下您的问题！";
        answer.put("content", data.getOrDefault("title", defContent));

        if("true".equals(data.getString("status"))) {
            JSONArray orderList = Optional.ofNullable(data.getJSONArray("list"))
                    .orElse(data.getJSONArray("rows"));
            if(orderList != null && !orderList.isEmpty()) {
                data.put("list", orderList);
                answer.put("cssData", data);
            } else {
                this.getLogger().info("Thread["+thid+"] userId["+userId+"] CSS工单数据为空");
                answer.put("msgType", "text");
            }
        } else {
            this.getLogger().info("Thread["+thid+"] userId["+userId+"] CSS工单状态异常：" + data);
            answer.put("msgType", "text");
        }
    }

    private void handleUnknownMessage(long thid, String userId, String msgType, JSONObject answer) {
        this.getLogger().info("Thread["+thid+"] userId["+userId+"] 无法识别的消息类型：" + msgType);
        answer.put("content", "系统暂时无法处理该类型的消息");
        answer.put("msgType", "text");
    }

}
