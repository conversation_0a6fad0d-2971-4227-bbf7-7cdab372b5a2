package com.yunqu.yc.media.center.msg.impl;


import com.yunqu.yc.media.center.util.CacheUtil;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.Agent;
import com.yunqu.yc.media.center.model.AutoConfig;
import com.yunqu.yc.media.center.model.Channel;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.model.UserSession;
import com.yunqu.yc.media.center.model.VideoSession;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;

/**
 * 客户端发送的视频事件消息处理
 * <AUTHOR>
 *
 */
public class UserVideoMessage extends Message{

	@Override
	public void onMessage(MessageModel messageModel){
		
		try {
			EntContext entContext = EntContext.getContext(messageModel.getEntId());
			Channel channel = entContext.getChannel(messageModel.getChannelId());
			AutoConfig autoConfig = channel.getAutoConfig();
			String sessionId = messageModel.getSessionId();
			String agentId = messageModel.getAgentId();
			String command = messageModel.getCommand();

			if(StringUtils.isBlank(agentId)) {
				UserSession user = entContext.getUser(sessionId);
				agentId = user.getAgentId();
				messageModel.setAgentId(agentId);
			}
				
			messageModel.setMsgType("event");
			messageModel.setEvent(command);
			//找对应的坐席，判断是否在线
			Agent agent = entContext.getAgentInfo(agentId);
			if(agent==null) {
				String msg = autoConfig.getAgentOfflineMsg();
				messageModel.setMsgContent(msg);
				messageModel.setEvent("end");
				messageModel.setSender("system");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				return;
			}
			
			//找到当前用户
			UserSession serviceUser = agent.getServiceUser(sessionId);
			
			if (serviceUser == null) {
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> agent service user is null");
				serviceUser = agent.getVideoUser();
			}
			if (serviceUser == null) {
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> UserSession is null");
				messageModel.setMsgContent("会话失效，请重新咨询人工客服");
				messageModel.setSender("system");
				String msg = messageModel.toString(RandomKit.randomStr());
				ProducerBroker.sendUserMessage(sessionId,msg);
				//系统回复语直接入库
				UserSession.getInstance(sessionId).saveMessage(JSONObject.parseObject(msg),4);
				return;
			}
			//更新用户信息
			JSONObject userInfo = messageModel.getUserInfo();
			if(userInfo!=null){
				serviceUser.setMessageModel(messageModel);
				serviceUser.setUserInfo(messageModel.getUserInfo());
			}
			
			JSONObject channelConfig = serviceUser.getChannel().getChannelConfig();
			String videoApiType = channelConfig.getString("VIDEO_API_TYPE");//yq-webrtc 云趣自研，tx-trtc 腾讯云TRTC
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserVideoMessage<"+command+","+sessionId+"> videoApiType["+videoApiType+"] << "+messageModel.toString());
			if("tx-trtc".equals(videoApiType)) {
				this.txtrtc(messageModel, agent, serviceUser, command);
			}else {
				this.yqwebrtc(messageModel, agent, serviceUser, command);
			}
			
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	
	/**
	 *  使用云趣视频SDK，美居app
	 * @param messageModel
	 * @param agent
	 * @param serviceUser
	 * @param command
	 * @throws Exception
	 */
	public void yqwebrtc(MessageModel messageModel,Agent agent,UserSession serviceUser,String command) throws Exception {
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		String agentId = agent.getAgentId();
		String sessionId = serviceUser.getSessionId();
		String defMsgType = "videoCall";//为了兼容美居app
		String videoMsgId = messageModel.getVideoMsgId();
		//用户发送视频邀请
		if("inviteVideo".equalsIgnoreCase(command)) {
			if(!agent.online()) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：当前坐席不在线！");
				messageModel.setMsgContent("客服不在线，请稍后……");
				messageModel.setEvent("disagreeInviteVideo");//当作坐席拒绝处理
				messageModel.setMsgType(defMsgType);
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				return;
			}
			UserSession videoUser = agent.getVideoUser();
			if (videoUser!=null) {
				if(StringUtils.equals(sessionId, videoUser.getSessionId())) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：重复邀请！");
					return;
				}
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：当前坐席正在视频通话中，通话用户："+videoUser);
				messageModel.setMsgContent("客服繁忙，请稍后……");
				messageModel.setEvent("disagreeInviteVideo");//当作坐席拒绝处理
				messageModel.setMsgType(defMsgType);
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				return;
			}
			if(StringUtils.isNotBlank(videoMsgId)){
				String cancelVideoFlag = CacheUtil.get("cancelVideo_" + videoMsgId);
				if(cancelVideoFlag!=null){
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：用户已经提前取消了视频邀请，videoMsgId："+videoMsgId);
					return;
				}
			}
			//锁定坐席，坐席被邀请期间不接受其他邀请
			boolean setVideoUser = agent.setVideoUser(serviceUser);
			if(!setVideoUser) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：当前坐席正在视频通话中！");
				messageModel.setEvent("disagreeInviteVideo");//当作坐席拒绝处理
				messageModel.setMsgContent("客服正在视频通话中，请稍后……");
				messageModel.setMsgType("videoCall");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				return;
			}

			boolean inviteFalg = serviceUser.inviteVideo(1);
			if(!inviteFalg) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频邀请失败：当前用户正在视频通话中，请勿重复发起邀请！");
				agent.removeVideoUser(sessionId);
				messageModel.setMsgContent("您正在视频通话中，请勿重复发起邀请");
				messageModel.setEvent("disagreeInviteVideo");//当作坐席拒绝处理
				messageModel.setMsgType(defMsgType);
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				return;
			}
			
			//发送视频邀请给坐席
			messageModel.setEvent("inviteVideo");
			messageModel.setMsgContent("用户邀请你视频聊天");
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			//回复用户正在接入
			messageModel.setEvent("videoCalling");//视频连接中
			messageModel.setMsgContent("正在接入视频聊天，请稍后……");
			messageModel.setMsgType(defMsgType);
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			return;

		//用户取消邀请
		}else if("cancelInviteVideo".equalsIgnoreCase(command)) {
			JSONObject clearMsg = null;
			try {
				clearMsg = serviceUser.cancelInviteVideo(14);
			}catch (Exception e){
				if(StringUtils.isNotBlank(videoMsgId)) CacheUtil.put("cancelVideo_"+videoMsgId,"cancelInviteVideo",5*60);
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> "+e.getMessage()+"，videoMsgId："+videoMsgId);
				clearMsg = new JSONObject();
				clearMsg.put("chatId",RandomKit.randomStr());
				clearMsg.put("callType",1);
				clearMsg.put("userText","已取消");
				clearMsg.put("agentText","用户已取消视频邀请");
			}
			int callType = clearMsg.getIntValue("callType");
			String chatId = clearMsg.getString("chatId");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(chatId);
			messageModel.setEvent("System");
			messageModel.setMsgType(defMsgType);
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(chatId));
			
			messageModel.setMsgType("event");
			messageModel.setEvent("cancelInviteVideo");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
		// errorInviteVideo 用户端异常，包括：1.初始化SDK失败，2.初始化成功回调，但mediaInfo为空或者rtcAccountID,userId为空
		}else if("errorInviteVideo".equalsIgnoreCase(command)) {
			VideoSession videoSession = serviceUser.getVideoSession();
			if(videoSession!=null) {
				JSONObject clearMsg = serviceUser.cancelInviteVideo(14);
				int callType = clearMsg.getIntValue("callType");
				//通知坐席
				messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
				messageModel.setSerialId(clearMsg.getString("chatId"));

				if(callType==1) {
					messageModel.setMsgType("event");
					messageModel.setEvent("cancelInviteVideo");
					messageModel.setMsgContent("用户端异常，请联系管理员！");
					ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
				}else {
					//坐席发起的视频聊天，需要先发送一条event=closeVideo,msgContent=""，再发送一条event=System,msgContent="通话时长 00:35"
					messageModel.setEvent("closeVideo");
					messageModel.setMsgContent("");
					ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));

					messageModel.setEvent("System");
					messageModel.setMsgContent("用户端异常，请联系管理员！");
					ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));
				}
				return;
			}
			messageModel.setMsgType("event");
			messageModel.setEvent("cancelInviteVideo");
			messageModel.setMsgContent("用户端异常，请联系管理员！");
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));

		//客户同意视频邀请
		}else if("agreeInviteVideo".equalsIgnoreCase(command)){
			VideoSession videoSession = serviceUser.getVideoSession();
			if(videoSession==null||!videoSession.isInvite()) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 客户同意视频邀请，当前视频会话非邀请状态！");
				return;
			}
			messageModel.setEvent("agreeInviteVideo");
			messageModel.setMsgContent("用户同意视频邀请");
			serviceUser.startVideo();
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			return;
		//客户拒绝视频邀请
		}else if("disagreeInviteVideo".equalsIgnoreCase(command)){
			JSONObject clearMsg = serviceUser.cancelInviteVideo(12);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setEvent("disagreeInviteVideo");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			return ;
			
		}else if("videoSuccess".equalsIgnoreCase(command)) {
			
		//取消或关闭视频通知坐席和用户
		}else if("closeVideo".equalsIgnoreCase(command)||"videoFail".equalsIgnoreCase(command)) {
			VideoSession videoSession = serviceUser.getVideoSession();
			if(videoSession==null) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+","+sessionId+") error  -> 视频会话不存在,sessionId:"+sessionId);
				return;
			}
			boolean videoChat = videoSession.isVideoChat();
			JSONObject clearMsg = serviceUser.closeVideo(11);
			int callType = clearMsg.getIntValue("callType");
			//通知坐席
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			
			if(callType==1) {
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			}else {
				//坐席发起的视频聊天，需要先发送一条event=closeVideo,msgContent=""，再发送一条event=System,msgContent="通话时长 00:35"
				messageModel.setEvent("closeVideo");
				messageModel.setMsgContent("");
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
				
				messageModel.setEvent("System");
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			}
			
			//发送视频满意度
			if(videoChat) {
//				/yc-mediagw/pages/appraise/appraise.jsp
				JSONObject videoConfig = entContext.getVideoConfig();
				String videoSatisfyUrl = videoConfig.getString("VIDEO_SATISFY_URL");
				if(StringUtils.isNotBlank(videoSatisfyUrl)) {
					String channelKey = serviceUser.getChannel().getChannelKey();
					if(videoSatisfyUrl.indexOf("?")>=0) {
						videoSatisfyUrl = videoSatisfyUrl+"&serialId="+serviceUser.getChatSessionId()+"&agentPhone="+agent.getAgentPhone()+"&channelKey="+channelKey+"&sessionId="+sessionId;
					}else {
						videoSatisfyUrl = videoSatisfyUrl+"?serialId="+serviceUser.getChatSessionId()+"&agentPhone="+agent.getAgentPhone()+"&channelKey="+channelKey+"&sessionId="+sessionId;
					}
					messageModel.setVideoSatisfyUrl(videoSatisfyUrl);
				}
			}
			//通知用户
			messageModel.setMsgType(defMsgType);
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			messageModel.setVideoSatisfyUrl("");
			return ;
		}else if("timeoutVideo".equalsIgnoreCase(command)) {
			JSONObject clearMsg = serviceUser.cancelInviteVideo(13);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setEvent("timeoutVideo");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			messageModel.setEvent("System");
			messageModel.setMsgType(defMsgType);
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(clearMsg.getString("chatId")));
			return ;
		}
	}
	
	/**
	 * 使用腾讯云视频SDK，美的服务号视频小程序
	 * @param messageModel
	 * @param agent
	 * @param serviceUser
	 * @param command
	 * @throws Exception
	 */
	public void txtrtc(MessageModel messageModel,Agent agent,UserSession serviceUser,String command) throws Exception {
		String agentId = agent.getAgentId();

		//客户拒绝视频邀请
		//需要先发送一条event=disagreeInviteVideo,msgContent=""，再发送一条event=System,msgContent="通话时长 00:35"
		if("disagreeInviteVideo".equalsIgnoreCase(command)){
			JSONObject clearMsg = serviceUser.cancelInviteVideo(12);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setEvent("disagreeInviteVideo");

			messageModel.setMsgContent("");
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			messageModel.setEvent("System");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			//通知客户端
			messageModel.setEvent("System");
			messageModel.setMsgType("text");
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(serviceUser.getSessionId(), messageModel.toString());
			
			return ;
		}
	}
	
	@Override
	public String getCommandName() {
		return "UserVideoMessage";
	}
}