package com.yunqu.yc.media.center.msg.impl;


import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.Channel;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.model.UserSession;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

/**
 * 获得可服务的坐席
 * <AUTHOR>
 *
 */
public class ByeMessage extends Message{

	@Override
	public void onMessage(MessageModel messageModel) {
		
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		Channel channel = entContext.getChannel(messageModel.getChannelId());
		String sessionId = messageModel.getSessionId();
		//是否正在会话中
		UserSession userSession = entContext.getUser(sessionId);
		
		//是否正在排队
		if(userSession == null){
			userSession = entContext.getQueueUser(sessionId);
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Thread["+Thread.currentThread().getId()+"] <"+this.getCommandName()+"> 用户是否正在排队 ->"+userSession);
		}
		
		
		//88后设置欢迎语
		
		//如果session为空，则不做任何处理
		if(userSession == null){
			messageModel.setEvent("end");
			messageModel.setMsgType("text");
			messageModel.setMsgContent(channel.getAutoConfig().getGoodbyeMsg());
			messageModel.setSender("system");
			String msg = messageModel.toString(RandomKit.randomStr());
			ProducerBroker.sendUserMessage(sessionId,msg);
			//系统回复语直接入库
//			userSession.saveMessage(JSONObject.parseObject(msg),4);
			return ;
		}
		//坐席id为空，代表未接入人工，只进入排队
		if(StringUtils.isBlank(userSession.getAgentId())){
			try {
//				MessageModel messageModel2 = userSession.getMessageModel();
				messageModel.setEvent("close");
				messageModel.setMsgType("text");
				messageModel.setClearCause("4");
				messageModel.setSender("system");
				messageModel.setMsgContent(userSession.getChannel().getAutoConfig().getCancelQueueMsg());
				String msg = messageModel.toString(RandomKit.randomStr());
				ProducerBroker.sendUserMessage(sessionId,msg);
				//系统回复语直接入库
				userSession.saveMessage(JSONObject.parseObject(msg),4);

				userSession.getSkillGroup().stopQueue(userSession,2);

			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
		userSession.logout(1);
	}

	@Override
	public String getCommandName() {
		// TODO Auto-generated method stub
		return "ByeMessage";
	}
}