package com.yunqu.yc.mediagw.base;

import org.easitline.common.core.context.AppContext;

import com.yunqu.yc.mediagw.util.CacheUtil;

public class Constants {
	
	public final static String GW_LOGGER_NAME = "yc-gateway";
	
	public final static String APP_NAME = "mediagw";
	
	public final static String WRITER_DS_NAME_ONE = "yc-write-ds-1";
	
	public final static String WRITER_DS_NAME_TOW = "yc-write-ds-2";
	
	public final static String READ_DS_NAME = "yc-read-ds";
	public final static String YW_DS_NAME = "yw-ds";

	public final static String GATE_SERVER  = "<GateServer> ";
	
	public final static String GATE_CLIENT  = "<GateClient> ";
	
	public final static String MQ_LOGGER_NAME = "yc-mq";
	
	private final static String STAT_DB = "stat";
	
	public static boolean isRun = false;

	/**
	 * 分隔符|| 表达式
	 */
	public final static String DELIMITER_REGEX = "\\|\\|";

	public final static String UINTERFACE_PASSWORD = "YQ_85521717";

	public static AppContext getContext(){
		return AppContext.getContext(APP_NAME);
	}
	public static String getStatSchema(){
		return getContext().getProperty("STAT_DB",STAT_DB);
	}



	/**
	 * memcache缓存中的用户信息名称前缀
	 */
	public final static String CACHE_VISITOR_NAME = "VisitorModel_";
	private static ThreadGroup threadGroup = new ThreadGroup(Constants.APP_NAME);

	public static ThreadGroup getDefThreadGroup(){
		return threadGroup;
	}


	/**
	 * AIGC大模型-机器人小结接口
	 * 生产：https://iop.midea.com/aiTools/chat/summary/callBack/CC/dialogue
	 */
	public static String getAigcSummaryUrl() {
		return AppContext.getContext(APP_NAME).getProperty("AIGC_SUMMARY_URL", "");
	}
	
	/**
	 * mediacenter应用是否正在被销毁
	 * @return
	 */
	public static boolean getMediaCenterIsDestroiing() {
		Object object = CacheUtil.get("MEDIACENTER_IS_DESTROIING");
		if(object==null) {
			return false;
		}
		if("1".equals(object)) {
			return true;
		}
		return false;
	}
	

	public static String getClientPath() {
		return AppContext.getContext(APP_NAME).getProperty("CLIENT_PATH", "/pages/web-chat.jsp");
	}
	
	/**
	 * 网页授权同时获取用户信息接口
	 * @return
	 */
	public static String getDefWXApiAuthUrl() {
		return AppContext.getContext(APP_NAME).getProperty("WX_AUTH_URL", "https://weixincs.midea.com/wcp/sic/api/WCP_MP_OAUTH_MAKE_REDIRECT_URL/d3cd5a215b7447f986eaef2364b9ef40/949c6fe9-8a43-48fc-8662-b838b22c48ba");
	}
	
	/**
	 * 查询用户信息接口
	 * @return
	 */
	public static String getDefWXApiUserInfoUrl() {
		return AppContext.getContext(APP_NAME).getProperty("WX_USER_INFO_URL", "https://weixincs.midea.com/wcp/sic/api/WCP_MP_GET_USER/d3cd5a215b7447f986eaef2364b9ef40/949c6fe9-8a43-48fc-8662-b838b22c48ba");
	}
	
	/**
	 * 获得Jsapi签名接口
	 * @return
	 */
	public static String getDefWXApiJsapiUrl() {
		return AppContext.getContext(APP_NAME).getProperty("WX_JSAPI_URL", "https://weixincs.midea.com/wcp/sic/api/WCP_MP_MAKE_SIGNATURE/d3cd5a215b7447f986eaef2364b9ef40/949c6fe9-8a43-48fc-8662-b838b22c48ba");
	}
	/**
	 * 获得生成小程序链接接口地址
	 * @return
	 */
	public static String getDefWXGenerateSchemeUrl() {
		return AppContext.getContext(APP_NAME).getProperty("WX_GENERATE_SCHEME_URL", "https://weixincs.midea.com/wcp/sic/api/WCP_MA_GENERATE_SCHEME/d3cd5a215b7447f986eaef2364b9ef40/meimeics");
	}

	/**
	 * 文件服务器根路径
	 * @return
	 */
	public static String getFileSysRootPath() {
		return AppContext.getContext(APP_NAME).getProperty("ATTACHMENT_ROOT_PAHT", "E:\files");
	}
	/**
	 * 获得asr语音转写接口
	 * @return
	 */
	public static String getAsrUrl() {
		return AppContext.getContext(APP_NAME).getProperty("ASR_URL", "");
	}
	/**
	 * 满意度链接地址
	 */
	public static String getSatisfyUrl(){
		return getContext().getProperty("SATISFY_CONFIG_URL", "");
	}

	/**
	 * 获取接口地址
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2022/3/29 11:23
	 * @return: java.lang.String
	 */
	public static String getInReceiveUrl(){
		return getContext().getProperty("PORAL_SERVICE_URL", "http://************:8080/uinterface/InReceive.do");
	}

	/**
	 * 获取查询用户提醒列表接口地址
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2022/3/29 11:23
	 * @return: java.lang.String
	 */
	public static String getReceiveUrl(){
		return getContext().getProperty("RECEIVE_URL", "http://************:8080/uinterface/Receive.do");
	}

	public static String getAnomalOrderUrl(){
		return getContext().getProperty("ANOMAL_ORDER_URL", "http://************:8080/uinterface/Receive.do");
	}

	public static String getAnomalOrderChannelKey(){
		return getContext().getProperty("ANOMAL_ORDER_CHANNEL_KEY", "");
	}


	/**
	 * 正在人工会话（bizType=zxkf）的用户超时时间，单位分钟
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2022/3/29 11:23
	 * @return: java.lang.String
	 */
	public static int getZxkfTimeout(){
		return Integer.parseInt(getContext().getProperty("ZXKF_TIMEOUT", "120"));
	}

	/**
	 * CSS接口鉴权参数，格式：iss###secret
	 * @return
	 */
	public static String getCssAuthKeys(){
//		SIT：
//		f7b61508a1a44a779e38081d1ce31e6b
//		920deb9cceb0459ba9f9d6f71e6974e0
//		UAT：
//		707d78b805524ab8b74ada668a5d2d32
//		e8094a7a1a82473eb6da8107fb7e3f80
//		707d78b805524ab8b74ada668a5d2d32###e8094a7a1a82473eb6da8107fb7e3f80
		return getContext().getProperty("CSS_AUTH_KEYS", "707d78b805524ab8b74ada668a5d2d32###e8094a7a1a82473eb6da8107fb7e3f80");
	}

	/**
	 * CSS上传图片接口
	 * @return
	 */
	public static String getCssUploadUrl() {
//		SIT：https://apisit.midea.com/c-css/c-css-ipms/mdfw/api/mmp/doupload
//		UAT：https://apiuat.midea.com/c-css/c-css-ipms/csscc/api/mmp/doupload
		return getContext().getProperty("CSS_UPLOAD_URL", "https://apiuat.midea.com/c-css/c-css-ipms/csscc/api/mmp/doupload");
	}

	public static String getCssUploadWhiteList() {
		return getContext().getProperty("CSS_UPLOAD_WHITE_LIST", ".gif,.png,.jpg,.jpeg,.bmp,.raw");
	}
	public static int getImgDebugIndex() {
		try {
			String cssimgDebugIndex = getContext().getProperty("CSSIMG_DEBUG_INDEX", "0");
			return Integer.parseInt(cssimgDebugIndex);
		} catch (Exception e) {
		}
		return 0;
	}

	public static boolean getCssDebugAddsup() {
		try {
			return "1".equals(getContext().getProperty("cssDebugAddsup", "0"));
		} catch (Exception e) {
		}
		return false;
	}

	public static String getAIGCOcrUrl() {
		return getContext().getProperty("AIGC_OCR_URL", "https://aimpapi.midea.com/t-aigc/aimp-multimedia-content-recognition/v1/inference");
	}

	public static String getAIGCApiKey() {
		return getContext().getProperty("AIGC_API_KEY", "msk-0a9844378a114952b32d16898bfb6e29a19275cfdb8490ca7b2e19a3718b3103");
	}

	public static String getImageDomain() {
		return getContext().getProperty("IMAGE_DOMAIN", "https://cc.midea.com");
	}

	public static String[] getSelfFilterConfig(){
		String selfFilterConfig = getContext().getProperty("selfFilterConfig", "");//格式：入口名称1##角色名称1||入口名称2##角色名称1
		return selfFilterConfig.split(DELIMITER_REGEX,selfFilterConfig.length()+1);
	}

	/**
	 * 全媒体接入是否校验签名
	 * @return
	 */
	public static boolean isCheckSgin(){
		return "true".equals(getContext().getProperty("IS_CHECK_SIGN", "false"));
	}

	/**
	 * 获取visitor缓存超时时间，单位：秒
	 * @return
	 */
	public static int getVisitorCacheTimeOut() {
		String cacheTimeOut = getContext().getProperty("CACHE_TIME_OUT", "24");
		try {
			return Integer.parseInt(cacheTimeOut) * 3600;
		} catch (Exception e) {
		}
		return 24*3600;
	}

	public static boolean isDevelopSwitch() {
		return "1".equals(getContext().getProperty("DEVELOP_SWITCH", "0"));
	}

	public static String getXssCode(){
		return getContext().getProperty("XSS_FILTER_CODE","");
	}


}