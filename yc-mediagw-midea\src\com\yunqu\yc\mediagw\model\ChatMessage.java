package com.yunqu.yc.mediagw.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.EntContext;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.event.impl.SQLExecutor;
import com.yunqu.yc.mediagw.event.impl.UserEventDispatcher;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.util.CacheLongTxtUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

public class ChatMessage {
	/**
	 * 发送者类型 ，1 客户
	 */
	public final static int MSG_SENDER_CUST = 1;
	/**
	 * 发送者类型 ，2 坐席
	 */
	public final static int MSG_SENDER_AGENT = 2;
	/**
	 * 发送者类型 ，3 机器人
	 */
	public final static int MSG_SENDER_ROBOT = 3;
	/**
	 * 发送者类型 ，4 系统
	 */
	public final static int MSG_SENDER_SYS = 4;
	
	public static EasyQuery query = QueryFactory.getQuery();
	
	
	/**
	 * 保存会话消息
	 * @param channelKey
	 * @param jsonObject
	 * @param sender 发送者类型 ，1 客户 2 坐席 3 机器人 4 系统
	 */
	public static void saveMessage(String channelKey,JSONObject jsonObject,int sender){
		long thid = Thread.currentThread().getId();

		try {
			EasyCalendar cal = EasyCalendar.newInstance();
			JSONObject data = jsonObject.getJSONObject("data");
			String sessionId = data.getString("sessionId");
			String msgType = data.getString("msgType");
			String senderStr = data.getString("sender");
			String msgContent = data.getString("msgContent");
			if("agent".equals(senderStr)) {
				sender = 2;
			}
			String serialId = jsonObject.getString("serialId");
			if(StringUtils.isBlank(serialId)) {
				serialId = RandomKit.uniqueStr();
			}
			if(StringUtils.isBlank(sessionId)) {
				MediagwLogger.getLogger().warn("Thread["+thid+"] 保存会话消息失败，sessionId不能为空，serialId："+serialId+"，sessionId："+serialId+"，senderStr："+senderStr);
				return;
			}
			EntContext  entContext = EntContext.getContext(channelKey);
			VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
			String chatSessionId = visitor==null?sessionId:visitor.getChatSessionId();
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_CHAT_RECORD"),"CHAT_ID");
			record.setPrimaryValues(serialId);
			record.set("DATE_ID",cal.getDateInt());
			record.set("ENT_ID",entContext.getEntId());
			record.set("CHAT_SESSION_ID",chatSessionId);
			record.set("CUST_SESSION_ID",sessionId);
			record.set("MSG_TIME",cal.getDateTime("-"));
			record.set("MSG_TIMESTAMP",System.currentTimeMillis());
			record.set("MSG_TYPE",msgType);
			record.set("SENDER",sender);

			// 处理机器人消息
			String robotDataStr = data.getString("robotData");
			if(StringUtils.isNotBlank(robotDataStr)) {
				try {
					JSONObject robotDataObj = JSONObject.parseObject(robotDataStr);
					robotDataObj.remove("welcomeCardList");//数据库中不需要保存，刷新页面会单独查询接口
					record.set("ROBOT_DATA",robotDataObj.toJSONString());
					String evaluation = robotDataObj.getString("evaluation");
					int eval = 3;
					if("Y".equals(evaluation)) {
						eval = 2;
					}
					//'会话点评标识，0 点赞 1 点踩，2 未点评，3 无需点评';
					record.set("EVALUATION_FLAG",eval);

					if(StringUtils.isBlank(msgContent)) {
						msgContent = robotDataObj.getString("content");
					}
				} catch (Exception e) {
					MediagwLogger.getLogger().error(e.getMessage(),e);
				}

			}

			if(StringUtils.isBlank(msgContent)) {
				MediagwLogger.getLogger().warn("Thread["+thid+"] 保存会话消息失败，msgContent不能为空，serialId："+serialId+"，sessionId："+sessionId+"，senderStr："+senderStr);
				return;
			}
			record.set("MSG_CONTENT",msgContent);

			String tempConfig = data.getString("tempConfig");
			if(StringUtils.isNotBlank(tempConfig)) {
				record.set("TEMP_CONFIG",tempConfig);
			}

			if(sender == 3){//机器人消息默认已读
				record.set("READ_STATE",1);
			}
			int readState = data.getIntValue("readState");
			if(readState>0){
				record.set("READ_STATE", readState);//消息已读状态，0：未读，1：已读 3：仅限坐席端显示
			}

			CacheLongTxtUtil.set(entContext.getSchemaId(),record ,serialId, "ROBOT_DATA","MSG_CONTENT","TEMP_CONFIG");

			SQLExecutor sqlExecutor = new SQLExecutor(sessionId, record, "insert");
			UserEventDispatcher.addEvent(sqlExecutor);

			//消息引用逻辑：保存用户发送消息中的引用数据到数据库中（CC_MEDIA_CHAT_RECORD_EXT.QUOTE_DATA）
			JSONObject userInfo = data.getJSONObject("userInfo");
			if(userInfo!=null){
				String quoteData = userInfo.getString("quoteData");
				if(StringUtils.isBlank(quoteData) && userInfo.containsKey("userData")){
					quoteData = userInfo.getJSONObject("userData").getString("quoteData");
				}
				if(StringUtils.isBlank(quoteData)){
					return;
				}
				MediagwLogger.getLogger().info("Thread["+thid+"] 保存会话消息-引用消息，serialId："+serialId+"，quoteData："+quoteData);
				EasyRecord record2 = new EasyRecord(entContext.getTableName("CC_MEDIA_CHAT_RECORD_EXT"),"CHAT_ID");
				record2.setPrimaryValues(serialId);
				record2.set("SESSION_ID", sessionId);
				record2.set("CHAT_SESSION_ID", chatSessionId);
				record2.set("MSG_TIME", cal.getDateTime("-"));
				record2.set("QUOTE_DATA", quoteData);
				CacheLongTxtUtil.set(entContext.getSchemaId(), record2, serialId, "QUOTE_DATA");
				SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "insert");
				UserEventDispatcher.addEvent(sqlExecutor2);
			}

		} catch (Exception ex) {
			MediagwLogger.getLogger().error(ex.getMessage(),ex);
		}
	}


	
	/**
	 * 保存留言
	 */
	public static void saveWord(VisitorModel visitor,JSONObject data){
		EasyCalendar cal = EasyCalendar.newInstance();
		int today = cal.getDateInt();
		EntContext entContext = EntContext.getContext(visitor.getChannelKey());
//		MediagwLogger.getLogger().info("saveWord -> VisitorModel:"+visitor.toJSONString()+",JSONObject:"+data.toJSONString()+",entContext:"+entContext.toString());
		String sessionId = data.getString("sessionId");
		String msgContent = data.getString("msgContent");
		if(StringUtils.isAnyBlank(sessionId,msgContent)) {
			MediagwLogger.getLogger().warn("保存会话消息失败，sessionId和msgContent不能为空");
			return;
		}
		try {
			EasySQL sql = new EasySQL("select WORD_ID from "+entContext.getTableName("CC_MDEIA_WORD")+" where 1=1");
			sql.append(entContext.getEntId()," and ENT_ID=?");
			sql.append(today," and DATE_ID=?");
			sql.append(visitor.getSessionId()," and SESSION_ID=?");
			sql.append(entContext.getChannelId()," and CHANNEL_ID=?");
			String wordId = query.queryForString(sql.getSQL(), sql.getParams());
			
			if(StringUtils.isBlank(wordId)){
//				wordId = RandomKit.uniqueStr();
				wordId = visitor.getChatSessionId();//1.01#20210513-1
				EasyRecord record = new EasyRecord(entContext.getTableName("CC_MDEIA_WORD"),"WORD_ID");
				record.setPrimaryValues(wordId);
				record.set("ENT_ID", entContext.getEntId());
				record.set("DATE_ID", today);
				record.set("SESSION_ID", visitor.getSessionId());
				record.set("WORD_TIME", cal.getDateTime("-"));
				record.set("CHANNEL_ID", entContext.getChannelId());
				record.set("CHANNEL_TYPE", entContext.getChannelType());
				record.set("CHANNEL_NAME", entContext.getChannelName());
				record.set("CHANNEL_KEY", entContext.getChannelKey());
				record.set("CUST_CODE", visitor.getUserInfo().getString("account"));
				record.set("CUST_NAME", visitor.getUserInfo().getString("nickname"));
				record.set("CHANNEL_CUST_INFO", visitor.getSimpleUserInfo().toJSONString());
				JSONObject cKey = entContext.getCKey(visitor.getUserInfo().getString("keyCode"));
				if(cKey != null){
					record.set("GROUP_ID", cKey.getIntValue("SKILL_GROUP_ID"));
					record.set("GROUP_NAME", cKey.getString("SKILL_GROUP_NAME"));
				}

				query.save(record);
			}
			String msgId = RandomKit.uniqueStr();
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MDEIA_WORD_MSG"),"MSG_ID");
			record.setPrimaryValues(msgId);
			record.set("WORD_ID", wordId);
			record.set("MSG_TIME", cal.getDateTime("-"));
			record.set("MSG_TYPE", data.getString("msgType"));
			record.set("MSG_CONTENT", data.getString("msgContent"));
			CacheLongTxtUtil.set(entContext.getSchemaId(),record ,msgId, "MSG_CONTENT");
			query.save(record);
		} catch (Exception e) {
			MediagwLogger.getLogger().error(e.getMessage(),e);
		}
	}
}
