package com.yunqu.yc.media.center.base;

import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.EasyQuery;
import com.yunqu.yc.media.center.log.MediaCenterLogger;

/**
 * 
 * <AUTHOR>
 */
public class QueryFactory {
	private static EasyQuery writeQuery1 = EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
	private static EasyQuery writeQuery2 = EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_TOW);
	private static EasyQuery readQuery = EasyQuery.getQuery(Constants.APP_NAME, Constants.READ_DS_NAME);
	
	public static EasyQuery getWriteQuery(String entId){
		if(StringUtils.isBlank(entId)){
			EasyQuery easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
			easyQuery.setLogger(MediaCenterLogger.getLogger());
			return easyQuery;
		}
		int _entId = Integer.parseInt(entId);
		if(_entId%2==0){  
			EasyQuery easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_TOW);
			easyQuery.setLogger(MediaCenterLogger.getLogger());
			return easyQuery;
		}else{
			EasyQuery easyQuery =  EasyQuery.getQuery(Constants.APP_NAME, Constants.WRITER_DS_NAME_ONE);
			easyQuery.setLogger(MediaCenterLogger.getLogger());
			return easyQuery;
		}
	}

	public static EasyQuery getQuery(){
		return writeQuery1;
	}

	public static EasyQuery getReadQuery(){
		return readQuery;
	}
	public static EasyQuery getStatQuery(){
		return writeQuery1;
	}
	
}
