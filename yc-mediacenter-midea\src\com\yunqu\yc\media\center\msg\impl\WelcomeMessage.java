package com.yunqu.yc.media.center.msg.impl;


import com.yunqu.yc.media.center.util.MediaCacheUtil;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.model.Agent;
import com.yunqu.yc.media.center.model.Channel;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.model.UserSession;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;
import com.yunqu.yc.media.center.util.CacheUtil;

public class WelcomeMessage extends Message{

	@Override
	public void onMessage(MessageModel messageModel) {
		
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		
		Channel channel = entContext.getChannel(messageModel.getChannelId());
		
		messageModel.setMsgType("text");
		messageModel.setEvent("selectKey");
		messageModel.setMsgContent(channel.getAutoConfig().getWelcomeMsg());
		messageModel.setSender("system");
		String sessionId = messageModel.getSessionId();
		String agentId = MediaCacheUtil.get("MEDIA_USER_AGENT_" + sessionId);
		//如果进入欢迎页，先清空原来用户和坐席的绑定信息。
		if(StringUtils.isNoneBlank(agentId)){
			Agent agent = entContext.getAgentInfo(agentId);
			if(agent != null) {
				agent.removeUser(sessionId);
				UserSession.delCacheMediaUserAgent(sessionId);				
			}
		}
		//发送欢迎语
		ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
		
	}

	@Override
	public String getCommandName() {
		// TODO Auto-generated method stub
		return "WelcomeMessage";
	}
}
