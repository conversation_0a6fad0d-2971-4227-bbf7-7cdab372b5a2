package com.yunqu.yc.media.center.model;


import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.util.CacheUtil;
import com.yunqu.yc.media.center.util.MediaCacheUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;


/**
 * 全媒体用户信息
 * <AUTHOR>
 *
 */
public class UserSession {
	public static Map<String, UserSession> userMaps = new HashMap<String, UserSession>();
	private static Set<String> levelSet = new HashSet<>();
	
	
	static {
		levelSet.add("level7");
		levelSet.add("level6");
		levelSet.add("level5");
		levelSet.add("level4");
		levelSet.add("level3");
		levelSet.add("level2");
		levelSet.add("level1");
	}
	
	
	/**
	 * 服务请求时间
	 */
	String requestTime;
	
	/**
	 * 服务开始时间
	 */
	String beginTime;
	
	/**
	 * 服务结束时间
	 */
	
	String endTime;
	
	long endTimemillis;
	
	/**
	 * 坐席最后消息发送时间
	 */
	long  agentTime = System.currentTimeMillis();
	
	/**
	 * 用户最后发送时间
	 */
	long  userTime = System.currentTimeMillis();
	
	long queueTime = 0;
	
	/**
	 * sessionId,用户唯一标志
	 */
	String sessionId;
	
	MessageModel messageModel ;
	
	/**
	 * 用户信息,自定义，其中：custCode 客户号码，custName 客户姓名 idcard 身份证为固定字段，其他为自定义字段。
	 */
	JSONObject userInfo;
	
	/**
	 * 渠道类型。
	 */
	String channelType = "";
	
	/**
	 * 企业ID
	 */
	String entId ;
	
	/**
	 * 服务坐席Id
	 */
	String  agentId;
	
	/**
	 * 转接前的服务坐席id
	 */
	String  oldAgentId;
	
	Channel channel;
	
	ChannelKey channelKey;
	
	private String chatSessionId;
	
	/**
	 * 关联会话id,转移会话，新的会话中要记录上一个会话id
	 */
	//#v2.0#20191128-1 fix by lijianping 20191128
	private String refChatSessionId;
	
	/**
	 * 发送用户超时消息标志，true 代表已经发送，就不再发送回复超时
	 */
	private boolean sendUserTimeoutFlag = false;
	
	/**
	 * 保存当前是否处于用户回复消息标志
	 */
	private boolean  userReplayFlag = true;
	
	/**
	 * 用户当前登录来源类型
	 *  1 排队中 2 服务中 3 转移
	 */
	private int loginType; 
	
	/**
	 * 会员等级,vip1用户>美的PRO会员=美的钻石会员>美的黄金会员>美的会员>普通用户
	 */
	//2.0#20200214-1
	private String level = "level1";

	/**
	 * 通知时间
	 */
	private long notifyTime;
	
	private VideoSession videoSession;
	
	private SkillGroup skillGroup;
	
	/**
	 * 用户状态，0初始，1排队，2正在接入人工，3 人工
	 */
	//2.0#20210513-1
	private int state;

	private long agentTimeTimeout;

	/**
	 * 转移技能组
	 */
	@JSONField(serialize = false)
	private SkillGroup transferGroup;

	private String memberId;//匹配身份策略的会员id
	private String memberGroupId;//身份策略转人工技能组id

	/**
	 * 转移随路数据
	 */
	@JSONField(serialize = false)
	private JSONObject transferData;

	@JSONField(serialize = false)
	public static UserSession getInstance(String sessionId) {
		synchronized (sessionId.intern()){
			UserSession user = userMaps.get(sessionId);
			if(user != null) {
				return user;
			}
			user = new UserSession();
			userMaps.put(sessionId, user);
			return user;
		}
	}

	public int getLoginType() {
		return loginType;
	}

	public void setLoginType(int loginType) {
		this.loginType = loginType;
	}
	
	//2.0#20200214-1
	public String getLevel() {
		return level;
	}
	//2.0#20200214-1
	public void setLevel(String level) {
		if(StringUtils.isBlank(level)) {
			level = "level1";
		}
		//等级标识不对，设置默认等级标识"level1"
		if(!levelSet.contains(level)) {
			level = "level1";
		}
		
		this.level = level;
	}

	public boolean isQueueTimeout(){
		if(System.currentTimeMillis() - this.getQueueTime() > channel.getAutoConfig().getQueueTimeout()){
			 return true;
		}
		return false;
	}
	
	public long getNotifyTime() {
		return notifyTime;
	}

	public void setNotifyTime(long notifyTime) {
		this.notifyTime = notifyTime;
	}

	public String getRefChatSessionId() {
		return refChatSessionId;
	}
	public void setRefChatSessionId(String refChatSessionId) {
		this.refChatSessionId = refChatSessionId;
	}

	public long getQueueTime() {
		return queueTime;
	}


	public void setQueueTime(long queueTime) {
		this.queueTime = queueTime;
	}




	public boolean isSendUserTimeoutFlag() {
		return sendUserTimeoutFlag;
	}

	public void setSendUserTimeoutFlag(boolean sendUserTimeoutFlag) {
		this.sendUserTimeoutFlag = sendUserTimeoutFlag;
	}

	public MessageModel getMessageModel() {
		return messageModel;
	}

	public void setMessageModel(MessageModel messageModel) {
		this.messageModel = messageModel;
	}

	public ChannelKey getChannelKey() {
		return channelKey;
	}

	public void setChannelKey(ChannelKey channelKey) {
		this.channelKey = channelKey;
	}

	public String getChatSessionId() {
		return chatSessionId;
	}

	public void setChatSessionId(String chatSessionId) {
		this.chatSessionId = chatSessionId;
	}

	public Channel getChannel() {
		return channel;
	}

	public void setChannel(Channel channel) {
		this.channel = channel;
		this.channelType = channel.getChannelType();
	}

	public String getMemberId() {
		return memberId;
	}

	public void setMemberId(String memberId) {
		this.memberId = memberId;
	}

	public String getMemberGroupId() {
		return memberGroupId;
	}

	public void setMemberGroupId(String memberGroupId) {
		this.memberGroupId = memberGroupId;
	}

	/**
	 * 用户退出, // '挂机原因， 1 用户主动结束服务  2  坐席结束服务  3  超时结束  4 用户排队结束  5 用户登录超时 6 转移结束 9 系统重启',
	 */
	public void logout(int clearCause){

		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserSession.logout("+clearCause+","+this.getAgentId()+")->"+this);
		if(getState()==2){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserSession.logout("+clearCause+") 当前状态--->“正在接入人工” 未完成，不允许执行用户退出操作->");
		}
		userMaps.remove(sessionId);
		EntContext entContext = EntContext.getContext(entId);
		//2.0#20191227-1
		delCacheMediaUserAgent(this.sessionId);
		entContext.removeUser(this.sessionId);
		entContext.removeQueueUser(this.sessionId);
		//2.0#20200109-1
		this.getChannelKey().removeUser(this);
		this.setEndTimemillis(System.currentTimeMillis());
		this.logoutVideo(clearCause);
		//如果坐席不存在，则代表只进入了排队
		if(StringUtils.isBlank(this.agentId)){
			try {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserSession.logout("+clearCause+") 坐席id不存在，则代表只进入了排队->"+this);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}
			this.setState(0);
			return ;
		}

		if(getState()==2){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserSession.logout("+clearCause+") 当前状态正在接入人工未完成，->");
		}
		
		Agent agent = entContext.getAgentInfo(agentId);
		//2.0#20191227-1
		if(agent!=null) {
			agent.removeUser(sessionId);
			//2.0#20200214-1
			agent.removeVideoUser(sessionId);
		}
		
		EasyCalendar  cal = EasyCalendar.newInstance();
		this.setEndTime(cal.getDateTime("-"));
		int serviceTime = cal.diff(this.getBeginTime(),this.getEndTime(), "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
		
		try {
			EasyQuery easyQuery = QueryFactory.getWriteQuery(this.entId);
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_RECORD"),"SERIAL_ID");
			record.setPrimaryValues(this.getChatSessionId());  // '服务状态，1 排队中 2 服务中  3 服务结束',
			record.set("END_TIME", this.getEndTime());
			record.set("CLEAR_CAUSE", clearCause);
			record.set("SERVER_TIME", serviceTime);
			record.set("SERVER_STATE", "3");
			easyQuery.update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}

		MessageModel newMessageModel = this.getMessageModel();
		newMessageModel.setSender("system");
		
		String goodbyeMsg = this.channel.getAutoConfig().getGoodbyeMsg();
		
		try {

			//通知用户结束会话
			newMessageModel.setMsgType("text");
			newMessageModel.setEvent("close");
			newMessageModel.setClearCause(clearCause+"");
			newMessageModel.setMsgContent(goodbyeMsg);
			String msg = newMessageModel.toString(RandomKit.randomStr());
			ProducerBroker.sendUserMessage(sessionId, msg);
			//系统回复语直接入库，20221027-优化，yc-medigw 模块也会将该条信息写入到数据库中，此处不需要写入。
//			UserSession.getInstance(sessionId).saveMessage(JSONObject.parseObject(msg),4);
			//发送系统消息通知坐席会话结束
			if(clearCause == 1) {
				newMessageModel.setMsgType("event");
				newMessageModel.setMsgContent("用户已主动断开会话");
				newMessageModel.setEvent("System");
				String chatId = this.saveMessage(JSONObject.parseObject(newMessageModel.toAgentString()), 4);
				newMessageModel.setSerialId(chatId);
				ProducerBroker.sendAgentMessage(newMessageModel.getAgentId(), newMessageModel.toAgentString());

//				Thread.sleep(50);
				//通知坐席会话结束
				newMessageModel.setMsgType("event");
				newMessageModel.setEvent("System");
				newMessageModel.setMsgContent(goodbyeMsg);
				ProducerBroker.sendAgentMessage(newMessageModel.getAgentId(), newMessageModel.toAgentString());
			}

		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}

		//2.0#20210602-1
		//发送结束事件通知坐席关闭会话
		try {
//			Thread.sleep(50);
			newMessageModel.setSerialId(RandomKit.randomStr());
			newMessageModel.setMsgType("event");
			newMessageModel.setMsgContent("");
			newMessageModel.setEvent("Closed");//通知坐席会话关闭
			ProducerBroker.sendAgentMessage(newMessageModel.getAgentId(), newMessageModel.toAgentString());
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
		
		//清空messageModel中的坐席信息
		messageModel.setAgentId(null);
		messageModel.setAgentName(null);
		messageModel.setAgentPhone(null);
		this.setState(0);

		//20221130 缓存上次接待的坐席
		String cacheKey = "LAST_AGENT_" + getChannelKey().getChannelKeyId() + "_" + getSessionId();
		MediaCacheUtil.put(cacheKey,getSkillGroup().getSkillGroupId()+"&&"+getAgentId(),48*3600);

	}

	/**
	 * 转移
	 * @param targetAgentId 目标坐席id
	 * @param srcChatSessionId 目标会话id
	 *  @param transferData 转移时携带的随路数据，{\"type\":1,\"targetAgentId\":\"fanhuayu\",\"remark\":\"\",\"nickname\":\"小李",\"chatSessionId\":\"83091458429536596437452\",\"transferType\":\"agent\",\"skillGroupId\":\"17\",\"skillGroupName\":\"在线一组\"}
	 */
	public boolean transfer(String targetAgentId,String srcChatSessionId,JSONObject transferData){

		if(!StringUtils.equals(this.chatSessionId, srcChatSessionId)) {
			MediaCenterLogger.getLogger().error("UserSession.transfer() agentId<"+agentId+"> -> targetAgentId<"+targetAgentId+"> UserSession["+this.sessionId+"]转移失败，会话不存在!!!!!");
			return false;
		}
		EntContext entContext = EntContext.getContext(entId);
		Agent agent = entContext.getAgentInfo(agentId);
		EasyCalendar  cal = EasyCalendar.newInstance();
		Agent tagAgent = entContext.getAgentInfo(targetAgentId);
		if(tagAgent==null||!tagAgent.online()) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserSession.transfer()-> 转移失败，目标坐席["+targetAgentId+"]不在线！！！！");
			return false;
		}
		this.setEndTime(cal.getDateTime("-"));
		this.setEndTimemillis(System.currentTimeMillis());
		int serviceTime = cal.diff(this.getBeginTime(),this.getEndTime(), "yyyy-MM-dd HH:mm:ss",EasyCalendar.SECOND );
		int clearCause = 6;
		try {
			EasyQuery easyQuery = QueryFactory.getWriteQuery(this.entId);
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_RECORD"),"SERIAL_ID");
			record.setPrimaryValues(this.getChatSessionId());  
			record.set("END_TIME", this.getEndTime());
			record.set("CLEAR_CAUSE", clearCause);  //挂机原因， 1 用户主动结束服务  2  坐席结束服务  3  超时结束  4 用户排队结束  5 用户登录超时 6 转移结束 9 系统重启
			record.set("SERVER_TIME", serviceTime);
			record.set("SERVER_STATE", "3");  //服务状态，1 排队中 2 服务中  3 服务结束
			easyQuery.update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		//通知原坐席关闭用户会话
		MessageModel newMessageModel = this.getMessageModel();
		try {
			newMessageModel.setAgentId(agent.getAgentId());
			newMessageModel.setSessionId(this.getSessionId());
			newMessageModel.setChannelId(this.channel.getChannelId());
			newMessageModel.setChannelType(this.channel.getChannelType());
			newMessageModel.setChannelName(this.channel.getChannelName());
			newMessageModel.setChannelKey(this.channel.getChannelKey());
			newMessageModel.setUserInfo(this.getUserInfo());
			newMessageModel.setMsgType("event");
			newMessageModel.setEvent("Closed");
			newMessageModel.setMsgContent("");
			newMessageModel.setSender("system");
			ProducerBroker.sendAgentMessage(agent.getAgentId(), newMessageModel.toAgentString(RandomKit.randomStr()));
			
			//先关闭和坐席的链接，在发送移除命令
//			Thread.sleep(50);
			newMessageModel.setEvent("Removed");
			ProducerBroker.sendAgentMessage(agent.getAgentId(), newMessageModel.toAgentString(RandomKit.randomStr()));
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		//移除在线用户
		agent.removeUser(sessionId);
		entContext.removeUser(this.getSessionId());
		entContext.removeQueueUser(this.getSessionId());
		//删除用户和原来坐席的对应关系
		delCacheMediaUserAgent(this.sessionId);
		this.logoutVideo(clearCause);
		this.oldAgentId = this.agentId;
		this.refChatSessionId = this.getChatSessionId();//#v2.0#20191128-1 fix by lijianping 20191128
		this.agentId = targetAgentId;
		//生成新的会话
		String newChatSessionId = RandomKit.uniqueStr();
		this.messageModel.setChatSessionId(newChatSessionId);
		this.setChatSessionId(newChatSessionId);
		this.setLoginType(3);
		tagAgent.addUser(this, 2);
		this.setTransferData(transferData);
		this.login();
		return true;
	}
	
	
	/**
	 * 用户登录,  1 排队中 2 服务中 3 服务结束
	 */
	public synchronized void login(){

		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserSession.login("+this.getLoginType()+","+this.getAgentId()+")->"+this);

		EntContext entContext = EntContext.getContext(entId);
		Agent agent = entContext.getAgentInfo(agentId);
		MediaCacheUtil.put("MEDIA_USER_AGENT_" + this.sessionId,this.agentId,24*3600);
		//计算排队时长
		long queueStateTime = 0;
		EasyCalendar  cal = EasyCalendar.newInstance();
		long curTime = System.currentTimeMillis();
		if(this.getQueueTime()>0) {
			queueStateTime = curTime - this.getQueueTime();
		}
		this.setBeginTime(cal.getDateTime("-"));
		this.setUserTime(curTime);
		this.setAgentTime(curTime);
		this.updateUserTime(curTime);
		this.setQueueTime(0);
		// 绑定用户和坐席的关系
		this.setBeginTime(cal.getDateTime("-"));
		EasyQuery easyQuery = QueryFactory.getWriteQuery(this.entId);

		EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_RECORD"),"SERIAL_ID");

		String agentNickName = agent.getNickName();

		if(StringUtils.isBlank(agentNickName)) {
			agentNickName = channel.getStyleConfig().getString("H5_AGENT_NAME");
		}

		this.selectGroup();

		try {
			entContext.addUser(sessionId, this);
			//关闭原来还没有结束的会话。
			int onCloseCount = easyQuery.queryForInt("select count(1) from " + entContext.getTableName("CC_MEDIA_RECORD") + " where SERVER_STATE = 2 and SESSION_ID = ? ", new Object[]{this.getSessionId()});
			if(onCloseCount>0){
				String sql = "update "+entContext.getTableName("CC_MEDIA_RECORD")+" set SERVER_STATE = 3 where SERVER_STATE = 2 and SESSION_ID = ?";
				int executeUpdate = easyQuery.executeUpdate(sql, this.getSessionId());
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserSession.login("+this.getLoginType()+","+this.getAgentId()+")->关闭原来还没有结束的会话["+executeUpdate+"]："+this);
			}
			//生成本次的会话ID
			this.chatSessionId = this.messageModel.getChatSessionId();
			
//		CacheUtil.put(Constants.BROKER_USER_NAME+"_VISITOR_"+sessionId,this.v, 3600 * 8);
			
			String custName = this.getUserInfo().getString("nickname");
			record.setPrimaryValues(this.getChatSessionId()); 
			record.set("SESSION_ID", this.getSessionId());
			record.set("MONTH_ID", cal.getFullMonth());
			record.set("DATE_ID", cal.getDateInt());
			record.set("ENT_ID", this.entId);
			record.set("ENT_NAME", entContext.getEntName());
			record.set("CHANNEL_ID", channel.getChannelId());
			record.set("CHANNEL_TYPE", channel.getChannelType());
			record.set("CHANNEL_KEY", channel.getChannelKey());
			record.set("CHANNEL_NAME", channel.getChannelName());
			record.set("CUST_CODE", this.getUserInfo().getString("account"));
			record.set("CUST_NAME", custName);
			record.set("KEY_ID",  this.channelKey.getChannelKeyId());
			record.set("KEY_NAME", this.channelKey.getKeyName());
			record.set("KEY_CODE", this.channelKey.getKeyCode());
			record.set("REQ_TIME", this.getRequestTime());
			record.set("CALL_TIMESTAMP", System.currentTimeMillis()/1000);
			record.set("CREATE_CAUSE", this.getLoginType());
			record.set("CHANNEL_CUST_INFO", this.getUserInfo().toJSONString());
			record.set("AGENT_ID", agent.getAgentId());
			record.set("AGENT_NAME", agent.getAgentName());
			record.set("AGENT_NICK_NAME", agentNickName);
			record.set("QUEUE_STAY_TIME", queueStateTime);
			record.set("BEGIN_TIME", cal.getDateTime("-"));
			record.set("SERVER_STATE", "2");  // '服务状态，1 排队中 2 服务中  3 服务结束',
			record.set("CALLBACK_URL", this.messageModel.getCallbackService());
			record.set("BROKER_NAME", this.messageModel.getBrokerName());
			record.set("REF_SERIAL_ID", this.getRefChatSessionId());//转移会话，原始坐席id //#v2.0#20191128-1 fix by lijianping 20191128
			record.set("MOBILE", this.getUserInfo().getString("mobile"));//手机号 2.0#20200218-1
			record.set("ITEM_ID", this.getUserInfo().getString("itemid"));//商品id 2.0#20200218-1
			try {
				SkillGroup skillGroup = this.getSkillGroup();
				record.set("GROUP_ID", skillGroup.getSkillGroupId());
				record.set("GROUP_NAME", skillGroup.getSkillGroupName());
			} catch (Exception e) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
			}
			
			easyQuery.save(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserSession.login("+this.getLoginType()+","+this.getAgentId()+")-> sessionId["+sessionId+"]会话记录写入失败："+record);
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		MessageModel messageModel = this.getMessageModel();
		messageModel.setChannelId(this.channel.getChannelId());
		messageModel.setChannelType(this.channel.getChannelType());
		messageModel.setChannelName(this.channel.getChannelName());
		messageModel.setChannelKey(this.channel.getChannelKey());
		messageModel.setChatSessionId(this.getChatSessionId());
		messageModel.setAgentId(agent.getAgentId());
		messageModel.setAgentName(agent.getAgentName());
		messageModel.setAgentPhone(agent.getAgentPhone());
		messageModel.setAgentNickName(agentNickName);
		messageModel.setEventCause(this.getLoginType()+"");
		messageModel.setSender("system");
		//20210412 用户信息中增加区分视频sdk类型
		JSONObject channelConfig = channel.getChannelConfig();
		userInfo.put("videoApiType", channelConfig.getString("VIDEO_API_TYPE"));//yq-webrtc 云趣自研，tx-trtc 腾讯云TRTC
		userInfo.put("miniprogramPage", channelConfig.getString("MINIPROGRAM_PAGE"));//小程序页面地址
		userInfo.put("miniprogramAppId", channelConfig.getString("MINIPROGRAM_APPID"));//小程序appId
		userInfo.put("thumbUrl", channelConfig.getString("MINIPROGRAM_THUMB_URL"));//小程序缩略图地址

		//用户主动接入
		if(this.getLoginType() == 1){
			try {
				//1.保存接入人工提示语
				String accessMsg = channel.getAutoConfig().getAccessMsgByLevel(this.getUserInfo().getString("levelCode"));
				accessMsg = accessMsg.replaceAll("#agentName#", agent.getAgentPhone());
				messageModel.setMsgType("text");
				messageModel.setEvent("start");
				messageModel.setSender("agent");
				messageModel.setMsgContent(accessMsg);
				String userMsg1 = messageModel.toString(RandomKit.randomStr());
				this.saveMessage(JSONObject.parseObject(userMsg1),4);

				//2.保存用户接入人工客服后系统回复语 IN_AGENT_AFTER_MSG 例如：这里是人工客服，请问有什么可以帮到您？
				String userMsg2 = "";
				String afterMsg = channel.getAutoConfig().getInAgentAfterMsg();
				if(StringUtils.isNotBlank(afterMsg)) {
					messageModel.setMsgType("text");
					messageModel.setEvent("System");
					messageModel.setSender("agent");
					messageModel.setMsgContent(afterMsg);
					userMsg2 = messageModel.toString(RandomKit.randomStr());
					this.saveMessage(JSONObject.parseObject(userMsg2),2);
				}

				//1.发送会话接入事件到坐席端，event=Connected
				messageModel.setSerialId(RandomKit.randomStr());
				messageModel.setMsgType("event");
				messageModel.setEvent("Connected");
				messageModel.setSender("system");
				messageModel.setMsgContent("");
				ProducerBroker.sendAgentMessage(messageModel.getAgentId(), messageModel.toAgentString());

				//2.发送event:start给网关，接入人工提示语
				ProducerBroker.sendUserMessage(sessionId, userMsg1);

				//3.发送用户接入人工客服后系统回复语
				if(StringUtils.isNotBlank(userMsg2)) ProducerBroker.sendUserMessage(sessionId,userMsg2);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}

			String videoMsgId = messageModel.getVideoMsgId();
			if(StringUtils.isNotBlank(videoMsgId)) {
				String cancelVideoFlag = CacheUtil.get("cancelVideo_" + videoMsgId);
				if(cancelVideoFlag!=null){
					MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserSession.login("+this.getLoginType()+","+this.getAgentId()+") error  -> 接入文本会话，用户已经提前取消了视频邀请，videoMsgId："+videoMsgId);
					JSONObject clearMsg = new JSONObject();
					clearMsg.put("chatId",RandomKit.randomStr());
					clearMsg.put("callType",1);
					clearMsg.put("userText","已取消");
					clearMsg.put("agentText","用户已取消视频邀请");
					messageModel.setMsgType("event");
					messageModel.setEvent("cancelInviteVideo");
					messageModel.setMsgContent(clearMsg.getString("agentText"));
					ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
					CacheUtil.delete("cancelVideo_" + videoMsgId);
				}
			}
		//转接成功，给转移坐席发送一条系统消息，同时通知mediagw
		}else if(this.getLoginType() == 3) {
			try {
				//2.给mediagw发送一个“transfer”事件
				messageModel.setMsgType("event");
				messageModel.setEvent("transfer");
				messageModel.setMsgContent("");
				messageModel.setSender("system");
				String transferMsg = messageModel.toString(RandomKit.randomStr());
				ProducerBroker.sendUserMessage(sessionId, transferMsg);

				//1.发送会话接入事件到坐席端，event=Connected
				messageModel.setSerialId(RandomKit.randomStr());
				messageModel.setMsgType("event");
				messageModel.setEvent("Connected");
				messageModel.setMsgContent("");
				ProducerBroker.sendAgentMessage(messageModel.getAgentId(), messageModel.toAgentString());

				//设置为坐席已回复
				this.updateAgentTime(curTime);
				messageModel.setMsgType("text");
				messageModel.setEvent("System");
				String transferChatGetMsg = "来自坐席["+this.oldAgentId+"]的转移会话";
				JSONObject transferData1 = this.getTransferData();
				if(StringUtils.isNotBlank(transferData1.getString("remark"))){
					transferChatGetMsg += "，备注：" + transferData1.getString("remark");
				}
				messageModel.setMsgContent(transferChatGetMsg);
				String chatId = this.saveMessage(JSONObject.parseObject(messageModel.toAgentString()), 5);
				messageModel.setSerialId(chatId);
				ProducerBroker.sendAgentMessage(messageModel.getAgentId(), messageModel.toAgentString());
				
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
		
		//2.0#20201014-1 进入人工 更新接入记录的进入人工时间
		JSONObject accessObj = new JSONObject();
		accessObj.put("ENT_ID", this.entId);
		accessObj.put("CHAT_SESSION_ID", this.getChatSessionId());
		accessObj.put("IS_IN_AGENT", 1);
		accessObj.put("IN_AGENT_TIME", cal.getDateTime("-"));
		accessObj.put("CLEAR_CAUSE", 5);//5.成功转人工结束
		accessObj.put("END_TIME", cal.getDateTime("-"));
		AccessRecord.getInstance().updateAccessRecord(accessObj);

		this.setState(3);
	}
	
//	1、会话开始，系统回复 接入人工提示语，此时 坐席属于未回复状态，用户属于已回复状态，开始计算坐席超时。
//	2、坐席回复之后，用户属于未回复状态，从坐席最后一句话开始计算用户回复超时。
//	3、用户回复之后，坐席属于未回复状态，从用户回复的第一句话开始计算坐席回复超时。
	//20210513
//	视频客服会话超时计算逻辑
//	1.客户端视频邀请，重置客户回复时间，开始计算坐席回复超时。
//	2.坐席端发送视频邀请，重置坐席回复时间，开始计算用户回复超时。
//	3.视频通话中，不计算超时
//	4.视频挂断，重置坐席、客户回复时间，开始计算坐席回复超时。

	public boolean checkUserReplyMsgTimeout(){
		//当前用户已回复，不做超时处理。
		if(this.userReplayFlag) return false;
		int  configTime = channel.getAutoConfig().getUserTimeout();
		return System.currentTimeMillis() - this.userTime > configTime;
	}
	
	public boolean checkAgentMsgTimeout(){
		int configTime = channel.getAutoConfig().getAgentTimeout();
		return checkAgentMsgTimeout(configTime);
	}

	public boolean checkAgentMsgTimeout(long timeout){
		//当前用户未回复，不检查坐席超时
		if(!this.userReplayFlag) return false;
		return System.currentTimeMillis() - this.agentTime > timeout;
	}

	public boolean checkAgentMsgTimeoutReal(long timeout){
		return System.currentTimeMillis() - this.agentTimeTimeout > timeout;
	}
	
	
	public boolean checkUserTimeout(){
		if(this.userReplayFlag) return false;
		int  closeConfigTime = channel.getAutoConfig().getCloseTime();
		int  msgConfigTime = channel.getAutoConfig().getUserTimeout();
		MediaCenterLogger.getLogger().debug("ckUserTimeout->System.currentTimeMillis("+System.currentTimeMillis()+") - userTime("+this.userTime+") = "+(System.currentTimeMillis()- this.userTime)+" > "+msgConfigTime);
		return System.currentTimeMillis() - this.userTime > (closeConfigTime + msgConfigTime);
	}
	
	/**
	 *
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2021/9/22 11:25
	 * @Param sessionId: 
	 * @return: void
	 */
	//2.0#20191227-1
	public static void delCacheMediaUserAgent(String sessionId) {
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] delCacheMediaUserAgent -> sessionId:"+sessionId);
		MediaCacheUtil.delete("MEDIA_USER_AGENT_" + sessionId);
	}

	/**
	 * 更新坐席回复时间，坐席超时未回复被系统检测到，需记录坐席回复时间超时开始时间，用于监控显示超时未回复
	 * @param agentTime
	 */
	public void sysUpdateAgentTime(long agentTime) {
		this.agentTimeTimeout = this.agentTime;//记录坐席回复时间超时开始时间
		this.agentTime = agentTime;
		if(this.userReplayFlag) {
			this.userTime = agentTime;
		}
		this.userReplayFlag = false;
	}
	/**
	 * 更新坐席回复时间，同时设置userReplayFlag=false,如果此时用户是已回复状态（userReplayFlag=true）需要重新设置用户回复时间为当前时间，以便计算用户回复超时。
	 * @param agentTime
	 */
	public void updateAgentTime(long agentTime) {
		this.agentTime = agentTime;
		this.agentTimeTimeout = agentTime;
		if(this.userReplayFlag) {
			this.userTime = agentTime;
		}
		this.userReplayFlag = false;
	}
	/**
	 * 更新用户回复时间，同时设置userReplayFlag=true,如果此时用户是未回复状态（userReplayFlag=false）需要重新设置坐席回复时间为当前时间，以便计算坐席回复超时。
	 * @param userTime
	 */
	public void updateUserTime(long userTime) {
		this.userTime = userTime;
		this.setSendUserTimeoutFlag(false);
		if(!this.userReplayFlag) {
			this.agentTime = userTime;
			this.agentTimeTimeout = agentTime;
		}
		this.userReplayFlag = true;
	}

	public long getAgentTime() {
		return agentTime;
	}

	public void setAgentTime(long agentTime) {
		this.agentTime = agentTime;
		this.agentTimeTimeout = agentTime;
	}

	public long getUserTime() {
		return userTime;
	}

	public void setUserTime(long userTime) {
		this.userTime = userTime;
	}

	public String getRequestTime() {
		return requestTime;
	}

	public void setRequestTime(String requestTime) {
		this.requestTime = requestTime;
	}

	public String getBeginTime() {
		return beginTime;
	}

	public void setBeginTime(String beginTime) {
		this.beginTime = beginTime;
	}

	public String getEndTime() {
		return endTime;
	}

	public void setEndTime(String endTime) {
		this.endTime = endTime;
	}

	public long getEndTimemillis() {
		return endTimemillis;
	}

	public void setEndTimemillis(long endTimemillis) {
		this.endTimemillis = endTimemillis;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}



	public JSONObject getUserInfo() {
		return userInfo;
	}

	public void setUserInfo(JSONObject userInfo) {
		this.userInfo = userInfo;
	}


	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getEntId() {
		if(StringUtils.isBlank(entId)){
			entId = "1000";
		}
		return entId;
	}

	public void setEntId(String entId) {
		if(StringUtils.isBlank(entId)){
			this.entId  = "1000";
			return;
		}
		this.entId = entId;
	}

	public String getAgentId() {
		return agentId;
	}

	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	
	public boolean isUserReplayFlag() {
		return userReplayFlag;
	}

	public void setUserReplayFlag(boolean userReplayFlag) {
		this.userReplayFlag = userReplayFlag;
	}
	
	public VideoSession getVideoSession() {
		return videoSession;
	}

	public void setVideoSession(VideoSession videoSession) {
		this.videoSession = videoSession;
	}

	public SkillGroup getSkillGroup() {
		return skillGroup;
	}

	public void setSkillGroup(SkillGroup skillGroup) {
		this.skillGroup = skillGroup;
	}

	public int getState() {
		return state;
	}

	public synchronized void setState(int state) {
		this.state = state;
	}

	public SkillGroup getTransferGroup() {
		return transferGroup;
	}

	public void setTransferGroup(SkillGroup transferGroup) {
		this.transferGroup = transferGroup;
	}

	public JSONObject getTransferData() {
		return transferData;
	}

	public void setTransferData(JSONObject transferData) {
		if(transferData==null){
			transferData = new JSONObject();
		}
		this.transferData = transferData;
	}

	/**
	 * 是否空闲
	 * @return
	 */
	//2.0#20210518-1
	public synchronized boolean isFee() {
		if(state==0) return true;
		return false;
	}

	/**
	 * 视频邀请
	 * @param callType 发起者， 1 客户端   2 坐席端
	 * @throws Exception
	 */
	public synchronized boolean inviteVideo(int callType){
		if(isVideoChat()) {
			return false;
		}
		
		//保存一条消息记录
		JSONObject jsonObject = new JSONObject();
		JSONObject data = new JSONObject();
		data.put("sessionId", sessionId);
		data.put("chatSessionId", chatSessionId);
		data.put("msgContent", "视频通话");
		data.put("msgType", "event");
		jsonObject.put("data", data);
		String chatId = this.saveMessage(jsonObject, callType);
		this.videoSession = new VideoSession();
		JSONObject channelConfig = this.getChannel().getChannelConfig();
		String videoApiType = channelConfig.getString("VIDEO_API_TYPE");
		this.videoSession.inviteVideo(entId, agentId, sessionId, chatSessionId, chatId, callType,videoApiType);
		if(callType ==1) this.updateUserTime(System.currentTimeMillis());
		if(callType ==2) this.updateAgentTime(System.currentTimeMillis());
		
		return true;
	}
	/**
	 * 取消视频邀请
	 * @param clearCause 挂机原因， 11 用户挂断，12 用户拒接，13 用户未接，14用户已取消，21 坐席关闭会议，22 坐席拒接，23 坐席未接，24坐席已取消，31 系统异常
	 */
	public synchronized JSONObject cancelInviteVideo(int clearCause) throws Exception {
		if(this.videoSession!=null) {
//		if(this.videoSession!=null&&this.videoSession.isInvite()) {//为什么要判断是否在邀请中
			EntContext entContext = EntContext.getContext(entId);
			Agent agent = entContext.getAgentInfo(agentId);
			agent.removeVideoUser(sessionId);
			JSONObject clearMsg = this.videoSession.cancelInviteVideo(clearCause);
			this.setVideoSession(null);
			return clearMsg;
		}
		
//		throw new Exception("取消视频邀请失败，当前无视频邀请，sessionId:"+sessionId);
		throw new Exception("取消视频邀请失败，视频不存在，sessionId:"+sessionId);
	}
	
	public void startVideo() {
		if(this.videoSession!=null) {
			this.videoSession.start();
		}
	}
	
	/**
	 * 关闭视频会话
	 * @param clearCause 挂机原因， 11 用户挂断，12 用户拒接，13 用户未接，14用户已取消，21 坐席关闭会议，22 坐席拒接，23 坐席未接，24坐席已取消，31 系统异常
	 */
	public synchronized JSONObject closeVideo(int clearCause) throws Exception{
		if(this.videoSession==null) {
			throw new Exception("关闭视频会话失败，视频会话不存在,sessionId:"+sessionId);
		}
		EntContext entContext = EntContext.getContext(entId);
		Agent agent = entContext.getAgentInfo(agentId);
		agent.removeVideoUser(sessionId);
		JSONObject clearMsg = this.videoSession.close(clearCause);
		this.updateAgentTime(System.currentTimeMillis());
		this.updateUserTime(System.currentTimeMillis());
		this.setVideoSession(null);
		return clearMsg;
	}

	/**
	 * 是否在视频通话中
	 * @return
	 */
	public boolean isVideoChat() {
		return this.videoSession!=null&&this.videoSession.isVideoChat();
	}
	
	/**
	 * 是否有视频通话和视频邀请
	 * @return
	 */
	public boolean hasVideo() {
		if(this.videoSession==null) return false;
		if(this.videoSession.isInvite()) return true;
		if(this.videoSession.isVideoChat()) return true;
		return false;
	}
	
	/**
	 * 退出视频
	 * '挂机原因， 1 用户主动结束服务  2  坐席结束服务  3  超时结束  4 用户排队结束  5 用户登录超时 6 转移结束 9 系统重启',
	 */
	public void logoutVideo(int clearCause) {
//		if(!this.isVideoChat()) {
//			return;
//		}
		try {
			int flag = clearCause==1||clearCause==3?11:31;
			flag = clearCause==2||clearCause==6?21:flag;
			this.closeVideo(flag);
			MessageModel tm = this.getMessageModel();
			tm.setMsgType("event");
			tm.setEvent("closeVideo");
			tm.setCommand("closeVideo");
			tm.setMsgContent("会话结束，视频通话强制结束");
			
//			MessageModel _messageModel = new MessageModel();
//			_messageModel.setAgentId(tm.getAgentId());
//			_messageModel.setBrokerName(tm.getBrokerName());
//			_messageModel.setCallbackService(tm.getCallbackService());
//			_messageModel.setChannelId(tm.getChannelId());
//			_messageModel.setChannelKey(tm.getChannelKey());
//			_messageModel.setChannelName(tm.getChannelName());
//			_messageModel.setChannelType(tm.getChannelType());
//			_messageModel.setChatSessionId(tm.getChatSessionId());
//			_messageModel.setClearCause(tm.getClearCause());
//			_messageModel.setEntId(tm.getEntId());
//			_messageModel.setEventCause(tm.getEventCause());
//			_messageModel.setMsgContent(tm.getMsgContent());
//			_messageModel.setRequestTime(tm.getRequestTime());
//			_messageModel.setSessionId(tm.getSessionId());
//			_messageModel.setSessionTime(tm.getSessionTime());
//			_messageModel.setUserInfo(tm.getUserInfo());
//			_messageModel.setSerialId(RandomKit.randomStr());
//			_messageModel.setMsgType("event");
//			_messageModel.setEvent("closeVideo");
//			_messageModel.setCommand("closeVideo");
//			_messageModel.setMsgContent("会话结束，视频通话强制结束");
			//发送event="closeVideo"给用户关闭视频会话
			ProducerBroker.sendUserMessage(this.getSessionId(), tm.toString(RandomKit.randomStr()));


			//发送event="closeVideo"给坐席关闭视频会话
			ProducerBroker.sendAgentMessage(this.getAgentId(), tm.toAgentString(RandomKit.randomStr()));
			
			//发送event="closeVideo"给三方坐席关闭视频会话
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserSession.logoutVideo("+clearCause+")----->会话主动结束，退出视频");
			
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	
	/**
	 * 检查视频邀请是否超时
	 */
	public void checkInviteVideoTimeOut() {
		if(videoSession==null) {
			return;
		}
		if(!videoSession.isInviteTimeOut()) {
			return;
		}
		int callType = videoSession.getCallType();
		MediaCenterLogger.getLogger().warn("<VideoInviteTimeOutJob> sessionId["+sessionId+"],callType["+callType+"],agentId["+this.agentId+"] >>>>>inviteVideo is timeout!!!!");
		
		MessageModel messageModel = this.getMessageModel();
		messageModel.setAgentId(this.agentId);
		String userMsgType = "event";
		if(videoSession.istxtrtc()) {
			userMsgType = "text";
		}
		//用户邀请坐席，坐席超时未接听
		try {
			if(callType==1) {
				JSONObject clearMsg = this.cancelInviteVideo(23);
				messageModel.setMsgContent(clearMsg.getString("userText"));
				messageModel.setMsgType(userMsgType);
				messageModel.setEvent("timeoutVideo");
				String msg = messageModel.toString(RandomKit.randomStr());
				ProducerBroker.sendUserMessage(sessionId,msg);
				//系统回复语直接入库
				this.saveMessage(JSONObject.parseObject(msg),4);

				messageModel.setMsgType("event");
				messageModel.setEvent("timeoutVideo");
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));
				
				messageModel.setEvent("System");
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));
			}
			
			//坐席邀请用户，用户超时未接听
			if(callType==2) {
				JSONObject clearMsg = this.cancelInviteVideo(13);
				messageModel.setMsgContent(clearMsg.getString("userText"));
				messageModel.setMsgType(userMsgType);
				messageModel.setEvent("timeoutVideo");
				String msg = messageModel.toString(RandomKit.randomStr());
				ProducerBroker.sendUserMessage(sessionId,msg);
				//系统回复语直接入库
				this.saveMessage(JSONObject.parseObject(msg),4);

				messageModel.setMsgType("event");
				messageModel.setEvent("timeoutVideo");
				messageModel.setMsgContent("");
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));
				
				messageModel.setMsgType("event");
				messageModel.setEvent("System");
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString(RandomKit.randomStr()));
			}
			
			this.updateAgentTime(System.currentTimeMillis());
			this.updateUserTime(System.currentTimeMillis());
			
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
		
		this.setVideoSession(null);
		EntContext.getContext(this.getEntId()).getAgentInfo(agentId).removeVideoUser(sessionId);
	}

	@Override
	public boolean equals(Object o ){
		if(o instanceof UserSession) {
			UserSession _user = (UserSession)o;
			return this.getSessionId().equals(_user.getSessionId());
		}
		return false;
	}
	@Override
	public int hashCode(){
		return this.sessionId.hashCode();
	}
	
	public String  toString(){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sessionId", this.getSessionId());
		jsonObject.put("agentId", this.getAgentId());
		jsonObject.put("userInfo", this.getUserInfo());
		jsonObject.put("chatSessionId", this.getChatSessionId());
		jsonObject.put("userReplayFalg", this.userReplayFlag);
		jsonObject.put("agentTime", this.getAgentTime());
		jsonObject.put("userTime", this.getUserTime());
		if(this.getChannelKey() !=null){
			jsonObject.put("channelId", this.getChannel().getChannelId());
			jsonObject.put("channelName", this.getChannel().getChannelName());
			jsonObject.put("channelKey", this.getChannelKey().getChannelKeyId());
			jsonObject.put("channelKeyName", this.getChannelKey().getKeyName());
		}
		return jsonObject.toJSONString();
	}

	/**
	 * 保存会话消息
	 * @param jsonObject
	 * @param sender 发送者类型 ，1 客户 2 坐席 3 机器人 4 系统
	 */
	public String saveMessage(JSONObject jsonObject,int sender){
		String chatId = RandomKit.uniqueStr();
		try {
			EasyCalendar cal = EasyCalendar.newInstance();
			JSONObject data = jsonObject.getJSONObject("data");
			String sessionId = data.getString("sessionId");
			String msgContent = data.getString("msgContent");
			String msgType = data.getString("msgType");
			EntContext  entContext = EntContext.getContext(getEntId());
			String chatSessionId = data.getString("chatSessionId");
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_CHAT_RECORD"),"CHAT_ID");
			record.setPrimaryValues(chatId);
			record.set("DATE_ID",cal.getDateInt());
			record.set("ENT_ID",getEntId());
			record.set("AGENT_ID","");
			record.set("CHAT_SESSION_ID",chatSessionId);  //如果是机器人的信息，则直接保存为当前的sessionId
			record.set("CUST_SESSION_ID",sessionId);
			record.set("MSG_TIME",cal.getDateTime("-"));
			record.set("MSG_TIMESTAMP",System.currentTimeMillis());
			record.set("MSG_TYPE",msgType);
			record.set("MSG_CONTENT",msgContent);
			record.set("SENDER",sender);
			QueryFactory.getWriteQuery(entId).save(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		return chatId;
	}

	/**
	 * 从渠道按键-队列-选择技能组，如果坐席关联多个技能组则取第一个
	 */
	private void selectGroup() {
		try {
			//转移的目标技能组
			if(transferGroup!=null){
				this.setSkillGroup(transferGroup);
				transferGroup = null;
			}
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
}
