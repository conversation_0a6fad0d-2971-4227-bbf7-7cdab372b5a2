package com.yunqu.yc.mediagw.base;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.log.VisitorInfosLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.*;
import com.yunqu.yc.mediagw.util.CacheUtil;
import com.yunqu.yc.mediagw.util.CommonUtil;
import com.yunqu.yc.mediagw.util.MediaCacheUtil;
import com.yunqu.yc.mediagw.util.SyncLockUtil;
import org.easitline.common.db.DBTypes;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.*;


/**
 * 
 * 保存当前坐席的登录信息。
 * <AUTHOR>
 *
 */
public class VisitorInfos {
	

	private  static VisitorInfos vistors ;
	
	private int cacheDate;
	public   Map<String,VisitorModel> visitors = new HashMap<String,VisitorModel>();
	
	private  int  seq = 100000;
	
	private boolean stopTheadFlag = false;
	
	public String  nextSeq(){
		seq++;
		String id = (seq+"");
		return id;
	}
	
	private VisitorInfos(){
		EasyCalendar cal = EasyCalendar.newInstance();
		cacheDate = cal.getDateInt();
		initThread();
	}
	
	public static VisitorInfos getInstance(){
		if(vistors != null){
			return vistors;
		}
		vistors = new VisitorInfos();
		
		return vistors;
	}

	private Thread mThread;
	
	public void initThread(){
		if(mThread==null) {
			mThread = new Thread(new MonitorThread());
			mThread.start();
		}
	}
	
	public void addVisitorModel(String sessionId,VisitorModel visitorModel){
		if(StringUtils.isBlank(sessionId)||visitorModel==null) {
			return;
		}
		visitors.put(sessionId, visitorModel);
		visitorModel.cache();
	}

	public void put(String sessionId,VisitorModel visitorModel){
		visitors.put(sessionId, visitorModel);
	}

	/**
	 * 获取内存中的用户信息对象
	 * @Description :获取用户信息对象，重启服务之后内存会被清掉。
	 * <AUTHOR>
	 * @Datetime 2021/9/27 17:28
	 * @Param sessionId: 用户唯一标识
	 * @return: com.yunqu.yc.mediagw.model.VisitorModel
	 */
	public  VisitorModel getVisitorModel(String sessionId){
		if(StringUtils.isBlank(sessionId)){
			return null;
		}
		VisitorModel visitorModel = visitors.get(sessionId);

		if(visitorModel!=null){
			return visitorModel;
		}

		Object lock = SyncLockUtil.getLock(sessionId);
		synchronized (lock){
			if(visitors.containsKey(sessionId)){
				return visitors.get(sessionId);
			}
			return getVisitorByCache(sessionId);
		}
	}
	public  VisitorModel getVisitorByMemory(String sessionId){
		if(StringUtils.isBlank(sessionId)){
			return null;
		}
		return visitors.get(sessionId);
	}
	/**
	 * 从缓存中获取
	 * @param sessionId
	 * @return
	 */
	public VisitorModel getVisitorByCache(String sessionId){
//		long startmills = System.currentTimeMillis();
//		VisitorInfosLogger.getLogger().info("get VisitorModel["+sessionId+"] by  memcache start");
		String visitorStr = MediaCacheUtil.get(Constants.CACHE_VISITOR_NAME+sessionId);
//		VisitorInfosLogger.getLogger().info("get VisitorModel["+sessionId+"] by  memcache,time:"+(System.currentTimeMillis()-startmills)+" ms ,visitorStr--->"+visitorStr);
		VisitorModel visitorByCache = StringUtils.isNotBlank(visitorStr)?JSONObject.parseObject(visitorStr, VisitorModel.class):null;
		if (visitorByCache != null) {
			if(System.currentTimeMillis() - visitorByCache.getLastChatTime()>100*60*1000) {
				VisitorInfosLogger.getLogger().info("get VisitorModel["+sessionId+"] by memcache , lastChatTime > 100min");
				visitorByCache.setBizType("welcome");
				visitorByCache.setChatSessionId(RandomKit.uniqueStr());
				visitorByCache.clearSelectKey();
			}
			this.put(sessionId, visitorByCache);
			return visitorByCache;
		}
		return null;

	}
	/**
	 * 从数据库中查找用户
	 * @param sessionId
	 * @return
	 */
	public  VisitorModel getVisitorByDB(String sessionId){
		try {

			//再从数据库中查找
			 EasyQuery query = QueryFactory.getQuery();
			EasySQL sql = new EasySQL("select * from "+EntContext.getDefEntTableName("CC_MEDIA_RECORD")+" where 1=1");
			sql.append(sessionId,"and  SESSION_ID=? ");
			
			if(query.getTypes()==DBTypes.ORACLE) {
				sql.append("and rownum<=1 order by END_TIME DESC");
			}else {
				sql.append("order by END_TIME DESC limit 1");
			}
			JSONObject hisChatObj =query.queryForRow(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
			if(hisChatObj==null)return null;
			VisitorModel visitorModel = new VisitorModel();
			visitorModel.setSessionId(sessionId);
			visitorModel.setBizType("welcome");
			visitorModel.setUpdateTime(hisChatObj.getLongValue("CALL_TIMESTAMP")*1000);
			visitorModel.setUserInfo(hisChatObj.getJSONObject("CHANNEL_CUST_INFO"));
			visitorModel.setCallbackUrl(hisChatObj.getString("CALLBACK_URL"));
			visitorModel.setChannelKey(hisChatObj.getString("CHANNEL_KEY"));
			visitorModel.setChannelId(hisChatObj.getString("CHANNEL_ID"));
			visitorModel.setChannelType(hisChatObj.getString("CHANNEL_TYPE"));
			visitorModel.setEntId(hisChatObj.getString("ENT_ID"));
			visitorModel.setBrokerName(hisChatObj.getString("BROKER_NAME"));
			if(hisChatObj.getIntValue("SERVER_STATE")==2){
				visitorModel.setChatSessionId(hisChatObj.getString("SERIAL_ID"));
				visitorModel.setBizType("zxkf");
			}else{
				visitorModel.setChatSessionId(RandomKit.uniqueStr());
				visitorModel.setBizType("welcome");
			}
			visitorModel.setLastChatTime(0);
			visitorModel.setSeq(nextSeq());
			this.addVisitorModel(sessionId,visitorModel);
			return visitorModel;
		} catch (Exception e) {
			VisitorInfosLogger.getLogger().error("getVisitorByDB is error ",e);
			return null;
		}
	}
	
	/**
	 * 删除用户信息
	 * @param sessionId
	 */
	public  void remove(String sessionId){
		VisitorModel visitorModel = visitors.remove(sessionId);
		if(visitorModel!=null){
			String eventKey = SessionNotifyEvents.getEventKey(visitorModel.getChannelKey(), sessionId);
			SessionNotifyEvents.getInstance().removeEventQueue(eventKey);
		}
	}
	
	/**
	 * 缓存用户信息对象到cache
	 */
	public void cacheVisitorModel() {
		VisitorInfosLogger.getLogger().info("cacheVisitorModel() is running visitors.size():"+visitors.size());
		try {
			for (String sessionId : visitors.keySet()) {
				VisitorModel visitorModel = visitors.get(sessionId);
				visitorModel.cache();
			}
		} catch (Exception e) {
			VisitorInfosLogger.getLogger().error("cacheVisitorModel() is error ",e);
		}
		VisitorInfosLogger.getLogger().info("cacheVisitorModel() is finish ");
	}

	/**
	 * 检查用户会话超时处理
	 */
	public void checkVisitorTimeout(){
		int zxkfTimeout = Constants.getZxkfTimeout();
		boolean mediaCenterIsDestroiing = Constants.getMediaCenterIsDestroiing();
		VisitorInfosLogger.getLogger().info("checkVisitorTimeout() start----> visitors.size():"+visitors.size());
		Set<String> ids = new HashSet<>(visitors.keySet());
		for (String sessionId : ids) {
			if(!Constants.isRun){
				break;
			}
			VisitorModel vi = visitors.get(sessionId);
			try {
				if("welcome".equalsIgnoreCase(vi.getBizType()) && System.currentTimeMillis()-vi.getLastChatTime()>10*60*1000){

					checkWelcomeTimeout(vi);
					continue;
				}
				if("end".equalsIgnoreCase(vi.getBizType())){
					checkEndTimeout(vi);
					continue;
				}
				//按键超时，5分钟超时
				if("selectKey".equalsIgnoreCase(vi.getBizType()) && System.currentTimeMillis()-vi.getLastChatTime()>5*60*1000){
					VisitorInfosLogger.getLogger().info("checkVisitorTimeout <selectKey> chat is timeout:"+vi.toJSONString());
					vi.setBizType("welcome");//超时之后设为“welcome”
					vi.setChatSessionId(RandomKit.uniqueStr());
					//20210401 按键超时邀请清理按键信息
					vi.clearSelectKey();
//					visitors.remove(sessionId);
					continue;
				}
				//机器人会话超时
				if("robot".equalsIgnoreCase(vi.getBizType())){
					checkRobotChatTimeout(vi);
					continue;
				}
				//留言超时
				if("word".equalsIgnoreCase(vi.getBizType())){
					checkWordChatTimeout(vi);
					continue;
				}
				//满意度超时
				if("satisfy".equalsIgnoreCase(vi.getBizType())){
					checkSatisfyChatTimeout(vi);
					continue;
				}
				//在线客服超时
				if("zxkf".equalsIgnoreCase(vi.getBizType())){
					long timeout = (long) zxkfTimeout *60*1000;
					if(System.currentTimeMillis()-vi.getLastChatTime()> timeout){//
						VisitorInfosLogger.getLogger().info("checkVisitorTimeout <zxkf> chat is timeout:"+vi.toJSONString());
						vi.setBizType("welcome");//超时之后设为“welcome”
						vi.setChatSessionId(RandomKit.uniqueStr());
						vi.clearSelectKey();
					}
					continue;
				}
				
			} catch (Exception e) {
				VisitorInfosLogger.getLogger().error("checkVisitorTimeout<"+sessionId+"> is error:"+e.getMessage(),e);
				vi.setBizType("welcome");//异常设为“welcome”
				vi.setChatSessionId(RandomKit.uniqueStr());
				VisitorInfos.getInstance().put(vi.getSessionId(), vi);
			}
		}
		VisitorInfosLogger.getLogger().info("checkVisitorTimeout() finish---->");
	}
	/**
	 * 检查机器人会话超时处理
	 */
	private void checkRobotChatTimeout(VisitorModel visitor){
		EntContext entContext = EntContext.getContext(visitor.getChannelKey());
		long robotTimeoutTime = entContext.getChannelAutoConf().getLongValue("ROBOT_TIME_OUT_TIME");
		String robotTimeoutMsg = entContext.getChannelAutoConf().getString("ROBOT_TIME_OUT_MSG");
		robotTimeoutTime = robotTimeoutTime>0?robotTimeoutTime:5;//默认五分钟
		robotTimeoutMsg = StringUtils.isNotBlank(robotTimeoutMsg)?robotTimeoutMsg:"您好，当前机器人会话已超时，感谢你的咨询。";
		
		if(System.currentTimeMillis()-visitor.getLastChatTime()>robotTimeoutTime*60*1000){//机器人会话超时
			//回复访客，机器人超时
			VisitorInfosLogger.getLogger().info("checkVisitorTimeout <robot> chat is timeout:"+visitor.toJSONString());
			//2.0#20201014-1
			String dateTime = EasyCalendar.newInstance().getDateTime("-");
			JSONObject accesssData = new JSONObject();
			accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
			accesssData.put("ROBOT_END_TIME", dateTime);
			accesssData.put("END_TIME", dateTime);
			accesssData.put("CLEAR_CAUSE", 2);//2.机器人超时结束
			AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);
			
			RequestDataModel  requestDataModel = new RequestDataModel();
			requestDataModel.setSerialId(RandomKit.uniqueStr());
			requestDataModel.setChannelKey(visitor.getChannelKey());
			requestDataModel.setEntId(visitor.getEntId());
			requestDataModel.setTimestamp(System.currentTimeMillis()+"");
			requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
			JSONObject data = new JSONObject();
			data.put("channelKey", visitor.getChannelKey());
			data.put("sessionId", visitor.getSessionId());
			data.put("event", "end");
			data.put("msgType","text");
			data.put("msgContent",robotTimeoutMsg);
			data.put("sender","system");
			data.put("chatSessionId", visitor.getChatSessionId());
			data.put("channelId", entContext.getChannelId());
			data.put("channelName", entContext.getChannelName());
			data.put("channelType", entContext.getChannelType());

			requestDataModel.setEvent("end");
			requestDataModel.setData(data);
			visitor.setBizType("welcome");
			visitor.setChatSessionId(RandomKit.randomStr());
			visitor.clearSelectKey();
			ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());

			//保存超时结束语
			ChatMessage.saveMessage(visitor.getChannelKey(), requestDataModel.toJSON(),ChatMessage.MSG_SENDER_SYS);

			//通知机器人下线
//			JSONObject jsonObject = new JSONObject();
//			JSONObject rdata = new JSONObject();
//			jsonObject.put("entId", visitor.getEntId());
//			jsonObject.put("serialId", RandomKit.uniqueStr());
//			jsonObject.put("channelKey", visitor.getChannelKey());
//			jsonObject.put("channelType", visitor.getChannelType());
//			jsonObject.put("data", rdata);
//			rdata.put("channelKey", visitor.getChannelKey());
//			rdata.put("msgType", "text");
//			rdata.put("msgContent", "88");
//			rdata.put("sessionId", visitor.getSessionId());
//			CommonUtil.sendMsgToRobot(jsonObject, visitor);
			CommonUtil.notifyRobotLogout(visitor,"VisitorInfos:checkRobotChatTimeout");
			ChatClickTagLog.getInstance().closeChat(visitor.getChannelKey(),visitor.getSessionId(),visitor.getChatSessionId());

		}
	}
	
	/**
	 * 检查留言会话超时处理
	 */
	private void checkWordChatTimeout(VisitorModel visitor){
		
		EntContext entContext = EntContext.getContext(visitor.getChannelKey());
		long wordTimeout = entContext.wordTimeout();
		String wordTimeoutMsg = entContext.getChannelAutoConf().getString("WORD_TIME_OUT_MSG");
		
		if(System.currentTimeMillis()-visitor.getLastChatTime()>wordTimeout*60*1000){//留言会话超时
			//回复访客，留言会话超时
			VisitorInfosLogger.getLogger().info("checkVisitorTimeout <word> chat is timeout:"+visitor.toJSONString());
			RequestDataModel  requestDataModel = new RequestDataModel();
			requestDataModel.setSerialId(RandomKit.uniqueStr());
			requestDataModel.setChannelKey(visitor.getChannelKey());
			requestDataModel.setEntId(visitor.getEntId());
			requestDataModel.setTimestamp(System.currentTimeMillis()+"");
			requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
			JSONObject data = visitor.getData();
			data.put("event", "end");
			data.put("msgContent",wordTimeoutMsg);
			data.put("msgType","text");
			data.put("msgTime", EasyCalendar.newInstance().getDateTime("-"));//消息发送时间
			data.put("sender","system");
			data.put("chatSessionId", visitor.getChatSessionId());
			data.put("sessionId", visitor.getSessionId());
			data.put("channelKey", visitor.getChannelKey());
			data.put("channelId", entContext.getChannelId());
			data.put("channelName", entContext.getChannelName());
			data.put("channelType", entContext.getChannelType());
			
			requestDataModel.setEvent("end");
			requestDataModel.setData(data);
			visitor.setBizType("welcome");
			visitor.setChatSessionId(RandomKit.randomStr());
			visitor.clearSelectKey();
			ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
			//保存超时结束语
			ChatMessage.saveMessage(visitor.getChannelKey(), requestDataModel.toJSON(),ChatMessage.MSG_SENDER_SYS);
		}
	}
	
	/**
	 * 检查满意度超时处理
	 */
	private void checkSatisfyChatTimeout(VisitorModel visitor){
		EntContext entContext = EntContext.getContext(visitor.getChannelKey());
		
		String satisfyTimeoutMsg = entContext.getChannelAutoConf().getString("SATISFY_TIME_OUT_MSG");
		satisfyTimeoutMsg = StringUtils.isNotBlank(satisfyTimeoutMsg)?satisfyTimeoutMsg:"您好，本次会话结束，感谢您使用我们的系统，再见。";
		if(System.currentTimeMillis()-visitor.getLastChatTime()>entContext.getSatisfyTimeout()*60*1000){//满意度超时
			//访客回复满意度超时
			VisitorInfosLogger.getLogger().info("checkVisitorTimeout <satisfy> chat is timeout:"+visitor.toJSONString());
			RequestDataModel  requestDataModel = new RequestDataModel();
			requestDataModel.setSerialId(RandomKit.uniqueStr());
			requestDataModel.setChannelKey(visitor.getChannelKey());
			requestDataModel.setEntId(visitor.getEntId());
			requestDataModel.setTimestamp(System.currentTimeMillis()+"");
			requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
			JSONObject data = visitor.getData();
			data.put("event", "end");
			data.put("msgContent",satisfyTimeoutMsg);
			data.put("msgType","text");
			data.put("msgTime", EasyCalendar.newInstance().getDateTime("-"));//消息发送时间
			data.put("sender","system");
			data.put("chatSessionId", visitor.getChatSessionId());
			data.put("sessionId", visitor.getSessionId());
			data.put("channelKey", visitor.getChannelKey());
			data.put("channelId", entContext.getChannelId());
			data.put("channelName", entContext.getChannelName());
			data.put("channelType", entContext.getChannelType());
			
			requestDataModel.setEvent("end");
			requestDataModel.setData(data);
			visitor.setBizType("welcome");
			visitor.setChatSessionId(RandomKit.randomStr());
			visitor.clearSelectKey();
			ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
			//保存超时结束语
			ChatMessage.saveMessage(visitor.getChannelKey(), requestDataModel.toJSON(),ChatMessage.MSG_SENDER_SYS);
		}
	}
	/**
	 * 检查超时用户处理
	 */
	private void checkWelcomeTimeout(VisitorModel visitor){
		if(System.currentTimeMillis() - visitor.getLastChatTime() > 10*60*1000){
			VisitorInfosLogger.getLogger().info("checkVisitorTimeout <welcome> chat is timeout:"+visitor.toJSONString());
			VisitorInfos.getInstance().remove(visitor.getSessionId());
		}
	}
	
	/**
	 * 检查会话结束之后的超时处理处理
	 */
	private void checkEndTimeout(VisitorModel visitor){
		if(System.currentTimeMillis()-visitor.getLastChatTime()>10*60*1000){//
			VisitorInfosLogger.getLogger().info("checkVisitorTimeout <end> chat is timeout:"+visitor.toJSONString());
			visitor.setBizType("welcome");
			visitor.setChatSessionId(RandomKit.randomStr());
		}
	}
	/**
	 * 获取会员等级标识
	 * @param levelName
	 * @return
	 */
	public static String getLevel(String levelName) {
		//1.01#20210616-1新增白银、铂金会员
//		vip1用户>PRO会员=钻石会员>铂金会员>黄金会员>白银会员>美的会员=普通会员>普通用户
//		20250320 新增美粉会员等级（黑钻美粉，白金美粉）  vip1用户 > PRO会员=钻石会员=黑钻美粉 > 铂金会员=白金美粉 > 黄金会员>白银会员> M会员=普通会员 >普通用户
//		20250626 新增美粉会员等级（蓝卡/银卡/金卡/白金卡/黑钻卡美粉） vip1用户 > PRO会员=钻石会员=黑钻卡美粉 > 铂金会员=白金卡美粉 > 黄金会员> 金卡美粉=银卡美粉 >白银会员> 蓝卡美粉=美的会员=普通会员 >普通用户
		if(StringUtils.isBlank(levelName)) return "level1";
		String levelCode = "";
		switch (levelName) {
			case "黑钻卡美粉":
				levelCode = "level5";
				break;
			case "白金卡美粉":
				levelCode = "level4";
				break;
			case "普通会员":
				levelCode = "level1";
				break;
			case "美的会员":
				levelCode = "level1";
				break;
			case "蓝卡美粉":
				levelCode = "level1";
				break;
			case "银卡美粉":
				levelCode = "level2";
				break;
			case "金卡美粉":
				levelCode = "level2";
				break;
			case "白银会员":
				levelCode = "level1";
				break;
			case "黄金会员":
				levelCode = "level3";
				break;
			case "铂金会员":
				levelCode = "level4";
				break;
			case "钻石会员":
				levelCode = "level5";
				break;
			case "PRO会员":
				levelCode = "level5";
				break;
			case "美的PRO会员":
				levelCode = "level5";
				break;
			case "vip1用户":
				levelCode = "level6";
				break;
			default:
				levelCode = "level1";
				break;
		}
		return levelCode;
	}
	
	public boolean isStopTheadFlag() {
		return stopTheadFlag;
	}

	public void setStopTheadFlag(boolean stopTheadFlag) {
		this.stopTheadFlag = stopTheadFlag;
	}



	/**
	 * 监控会话
	 * <AUTHOR>
	 *
	 */
	public class MonitorThread implements Runnable{

		@Override
		public void run() {
			
			VisitorInfosLogger.getLogger().info(" MonitorThread  is running");
			int count = 0;
			while(Constants.isRun){
				if(isStopTheadFlag()) break;
				
				try {
					Thread.sleep(1*1000);
				} catch (Exception e) {
				}
				count++;

				if(count<10){
					continue;
				}
				count = 0;

				try {
					checkVisitorTimeout();
				} catch (Exception e) {
					VisitorInfosLogger.getLogger().error("MonitorThread checkSelectKeyTimeout() is error: << "+e.getMessage(),e);
				}
			}
			
			VisitorInfosLogger.getLogger().info(" MonitorThread  is finish");
		}
	}
}
