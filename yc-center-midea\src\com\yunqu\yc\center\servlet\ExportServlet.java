package com.yunqu.yc.center.servlet;

import java.io.File;
import java.io.FileOutputStream;
import java.math.BigDecimal;
import java.sql.SQLException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;

import org.apache.poi.ss.usermodel.CellStyle;
import org.apache.poi.ss.util.CellRangeAddress;
import org.apache.poi.xssf.usermodel.XSSFCell;
import org.apache.poi.xssf.usermodel.XSSFFont;
import org.apache.poi.xssf.usermodel.XSSFRow;
import org.apache.poi.xssf.usermodel.XSSFSheet;
import org.apache.poi.xssf.usermodel.XSSFWorkbook;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.excel.ExcelUtils;
import org.easitline.common.utils.excel.handler.ExcelHeaderStyle;
import org.easitline.common.utils.kit.FileKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.center.base.AppBaseServlet;
import com.yunqu.yc.center.base.Constants;
/**
 * 数据导出  
 */
@WebServlet("/servlet/export")
public class ExportServlet extends AppBaseServlet {
	private static final long serialVersionUID = 1L;
	
	
	/**
	 * 导出企业话务量统计表
	 * @throws SQLException
	 */
	public void actionForExportEntCallStat() throws SQLException{
		HttpServletRequest request = this.getRequest();
		String entId = request.getParameter("entId");
		String statisticType = request.getParameter("statisticType");
		statisticType = StringUtils.isNotBlank(statisticType)?statisticType:"date";
		
		EasySQL sql = new EasySQL("select t3.ENT_NAME,");
		
		String dinColumn = "";//动态表头内容
		String statField = "";//sql语句中的统计字段，todo:考虑到oracle数据库中不支持别名做分组字段
		//区分不同的统计维度
		if("ent".equals(statisticType)){
			sql.append(" t3.ENT_CODE as STA_NAME,");
			statField = "t3.ENT_CODE";
			dinColumn = "企业名称";
		}else if("task".equals(statisticType)){
			sql.append(" t2.TASK_NAME as STA_NAME,");
			statField = "t2.TASK_NAME";
			dinColumn = "任务名称";
		}else if("agent".equals(statisticType)){
			sql.append(" t4.USER_ACCT as STA_NAME,t4.AGENT_PHONE,");
			statField = "t4.USER_ACCT,t4.AGENT_PHONE";
			dinColumn = "坐席名称";
		}else if("date".equals(statisticType)){
			sql.append(" t1.DATE_ID as STA_NAME,");
			statField = "t1.DATE_ID";
			dinColumn = "日期";
		}else if("phoneNum".equals(statisticType)){
			sql.append(" t1.CALLER as STA_NAME,");
			statField = "t1.CALLER";
			dinColumn = "话机号码";
		}
		
		sql.append(" SUM(t1.TOTAL_TIME) as TOTAL_TIME,");
		sql.append(" SUM(t1.AUTO_CALL_TIME) as AUTO_CALL_TIME,");
		sql.append(" SUM(t1.AGENT_CALL_TIME) as AGENT_CALL_TIME,");
		
		sql.append(" SUM(t1.CALL_COUNT) as CALL_COUNT,");
		sql.append(" SUM(t1.AUTO_CALL_COUNT) as AUTO_CALL_COUNT,");
		sql.append(" SUM(t1.AGENT_CALL_COUNT) as AGENT_CALL_COUNT,");
			
		sql.append(" SUM(t1.CONN_SUCC_COUNT) as CONN_SUCC_COUNT,");
		sql.append(" SUM(t1.AGENT_SUCC_COUNT) as AGENT_SUCC_COUNT,");
		sql.append(" SUM(t1.REJECT_FAIL_COUNT) as REJECT_FAIL_COUNT,");
		sql.append(" SUM(t1.NOANSWER_FAIL_COUNT) as NOANSWER_FAIL_COUNT");
		
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");
		sql.append(" from "+Constants.getStatSchema()+"."+ycstatTableInfo.get("TARGET_TABLE_NAME")+" t1");//这个表在ycstat这个库中，采用读数据源来访问
		sql.append(" , CC_ENT t3 ");
		//区分不同的统计维度
		if("ent".equals(statisticType)){
			sql.append(" where t1.TOTAL_TIME > 0 ");
		}else if("task".equals(statisticType)){
			sql.append(" ,"+getTableName(entId,"CC_TASK")+" t2  where t1.TOTAL_TIME > 0 and t1.TASK_ID = t2.TASK_ID ");
		}else if("agent".equals(statisticType)){
			sql.append(" ,CC_USER t4  where t1.AGENT_ID = t4.USER_ID");
		}else if("date".equals(statisticType)){
			sql.append(" where t1.DATE_ID is not null");
		}else if("phoneNum".equals(statisticType)){
			sql.append(" where t1.CALLER is not null");
		}
		//固定查询条件
		sql.append("and t1.ENT_ID = t3.ENT_ID");
		sql.append(entId, " and t1.ENT_ID = ?");
		
		if(StringUtils.isNotBlank(request.getParameter("startDate"))){
			sql.append(request.getParameter("startDate").replace("-", ""), " and t1.DATE_ID >= ? ");
		}
		if(StringUtils.isNotBlank(request.getParameter("endDate"))){
			sql.append(request.getParameter("endDate").replace("-", ""), " and t1.DATE_ID <= ?");
		}
		sql.append(" group by t3.ENT_NAME,"+statField+" order by t3.ENT_NAME asc,"+statField+" ASC ");
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		
		headers.add(" 序号 ");
		headers.add(dinColumn);
		headers.add(" 企业名称 ");
		headers.add(" 通话总时长(秒) ");
		headers.add(" 自动外呼通话时长(秒) ");
		headers.add(" 人工外呼呼通话时长(秒) ");
		headers.add(" 呼叫次数 ");
		headers.add(" 自动外呼呼叫次数 ");
		headers.add(" 人工外呼呼叫次数 ");
		headers.add(" 接通次数 ");
		headers.add(" 转坐席次数 ");
		headers.add(" 拒绝接听次数 ");
		headers.add(" 无人应答次数 ");
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;

		if(data!=null&&data.size()>0){
			for(Map<String,String> map : data){
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				if(StringUtils.isNotBlank(map.get("AGENT_PHONE"))){
					list.add(map.get("STA_NAME")+"（"+map.get("AGENT_PHONE")+"）");
				}else{
					list.add(map.get("STA_NAME"));
				}
				list.add(map.get("ENT_NAME"));
				list.add(parseTime(map.get("TOTAL_TIME")));
				list.add(parseTime(map.get("AUTO_CALL_TIME")));
				list.add(parseTime(map.get("AGENT_CALL_TIME")));
				list.add(map.get("CALL_COUNT"));
				list.add(map.get("AUTO_CALL_COUNT"));
				list.add(map.get("AGENT_CALL_COUNT"));
				list.add(map.get("CONN_SUCC_COUNT"));
				list.add(map.get("AGENT_SUCC_COUNT"));
				list.add(map.get("REJECT_FAIL_COUNT"));
				list.add(map.get("NOANSWER_FAIL_COUNT"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			 Map<String, String> entInfo = getEntInfo(entId);
			String fileName = "企业话务量统计表.xlsx";
			if(entInfo!=null && entInfo.size()>0){
				fileName = "企业话务量统计表("+entInfo.get("ENT_NAME")+").xlsx";
			}
			renderFile(file,fileName);
		} catch (Exception e) {
			e.printStackTrace();
		};
		
	}
	
	/**
	 * 导出任务统计表
	 * @throws SQLException
	 */
	public void actionForExportRptTaskStat() throws SQLException{
		HttpServletRequest request = this.getRequest();
		String entId = request.getParameter("entId");
		String statisticType = request.getParameter("statisticType");
		statisticType = StringUtils.isNotBlank(statisticType)?statisticType:"date";
		
		EasySQL sql = new EasySQL("select t3.ENT_NAME,");
		String statField = "";//sql语句中的统计字段，todo:考虑到oracle数据库中不支持别名做分组字段
		String fristHead = "任务名称";//动态表头内容
		//区分不同的统计维度
		if("task".equals(statisticType)){
			sql.append(" t2.TASK_NAME as STA_NAME,");
			statField = "t2.TASK_NAME";
			fristHead = "任务名称";
		}else if("skillGroup".equals(statisticType)){
			sql.append(" t4.SKILL_GROUP_NAME as STA_NAME,");
			statField = "t4.SKILL_GROUP_NAME";
			fristHead = "技能组名称";
		}else if("agent".equals(statisticType)){
			sql.append(" t4.AGENT_NAME as STA_NAME,t4.AGENT_PHONE,");
			statField = "t4.AGENT_NAME,t4.AGENT_PHONE";
			fristHead = "坐席名称";
		}else if("date".equals(statisticType)){
			sql.append(" t1.DATE_ID as STA_NAME,");
			statField = "t1.DATE_ID";
			fristHead = "日期";
		}
		
		sql.append(" SUM(t1.OBJ_COUNT) as OBJ_COUNT,");
		sql.append(" SUM(t1.OBJ_USE_COUNT) as OBJ_USE_COUNT,");
		sql.append(" SUM(t1.CALL_COUNT) as CALL_COUNT,");
		sql.append(" SUM(t1.CALL_SUCCESS_COUNT) as CALL_SUCCESS_COUNT,");
		sql.append(" SUM(t1.CALL_REJECT_COUNT) as CALL_REJECT_COUNT,");
		sql.append(" SUM(t1.CALL_NOANSWER_COUNT) as CALL_NOANSWER_COUNT,");
		sql.append(" SUM(t1.AGENT_COUNT) as AGENT_COUNT,");
		sql.append(" SUM(t1.SALE_SUCCESS_COUNT) as SALE_SUCCESS_COUNT,");
		sql.append(" SUM(t1.SALE_FAIL_COUNT) as SALE_FAIL_COUNT,");
		sql.append(" SUM(t1.SALE_TATOL_TIME) as SALE_TATOL_TIME");
		
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_TASK_STAT");
		sql.append(" from "+Constants.getStatSchema()+"."+ycstatTableInfo.get("TARGET_TABLE_NAME")+" t1");
		sql.append(" left join CC_ENT t3 on t1.ENT_ID = t3.ENT_ID ");
		
		//区分不同的统计维度
		if("task".equals(statisticType)){
			sql.append(" ,"+getTableName(entId,"CC_TASK")+" t2 ");
			sql.append(" where  t1.TASK_ID = t2.TASK_ID");
		}else if("skillGroup".equals(statisticType)){
			sql.append(" , "+getTableName(entId,"CC_SKILL_GROUP")+" t4 ");
			sql.append(" where t1.GROUP_ID = t4.SKILL_GROUP_ID");
		}else if("agent".equals(statisticType)){
			sql.append(" ,"+getTableName(entId,"CC_BUSI_USER")+" t4");
			sql.append(" where t1.AGENT_ID = t4.USER_ID");
		}else if("date".equals(statisticType)){
			sql.append(" where t1.DATE_ID is not null");
		}
		//固定查询条件
		sql.append(entId, " and t1.ENT_ID = ?");
		
		if(StringUtils.isNotBlank(request.getParameter("startDate"))){
			sql.append(request.getParameter("startDate").replace("-", ""), " and t1.DATE_ID >= ? ");
		}
		if(StringUtils.isNotBlank(request.getParameter("endDate"))){
			sql.append(request.getParameter("endDate").replace("-", ""), " and t1.DATE_ID <= ?");
		}
		sql.append(" group by t3.ENT_NAME,"+statField+" order by t3.ENT_NAME asc,"+statField+" asc");
		
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(fristHead);
		headers.add(" 企业名称");
		headers.add(" 名单总量 ");
		headers.add(" 已使用名单量 ");
		headers.add(" 外呼次数 ");
		headers.add(" 接通名单量 ");
		headers.add(" 转坐席数 ");
		headers.add(" 营销成功数 ");
		headers.add(" 营销失败数 ");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		String entName = getEntInfo(entId).get("ENT_NAME");

		if(data!=null&&data.size()>0){
			for(Map<String,String> map : data){
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				if(StringUtils.isNotBlank(map.get("AGENT_PHONE"))){
					list.add(map.get("STA_NAME")+"（"+map.get("AGENT_PHONE")+"）");
				}else{
					list.add(map.get("STA_NAME"));
				}
				list.add(map.get("ENT_NAME"));
		    	list.add(map.get("OBJ_COUNT"));
		    	list.add(map.get("OBJ_USE_COUNT"));
			    list.add(map.get("CALL_COUNT"));
			    list.add(map.get("CALL_SUCCESS_COUNT"));
			    list.add(map.get("AGENT_COUNT"));
			    list.add(map.get("SALE_SUCCESS_COUNT"));
		    	list.add(map.get("SALE_FAIL_COUNT"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file,"任务统计表("+entName+").xlsx");
		} catch (Exception e) {
			e.printStackTrace();
		};
	}
	
	
	/**
	 * 导出企业计费统计表
	 * @throws SQLException
	 */
	public void actionForExportEntBillStat() throws SQLException{
		HttpServletRequest request = this.getRequest();
		String entId = request.getParameter("entId");
		String monthId = request.getParameter("monthId");
		if(StringUtils.isNoneBlank(monthId)){
			monthId = monthId.replace("month_", "");
		}
		String statisticType = request.getParameter("statisticType");
		statisticType = StringUtils.isNotBlank(statisticType)?statisticType:"date";
		
		EasySQL sql = new EasySQL("select t3.ENT_NAME,");
		String statField = "";//sql语句中的统计字段，todo:考虑到oracle数据库中不支持别名做分组字段
		String fristHead = "任务名称";//动态表头内容
		//区分不同的统计维度
		if("task".equals(statisticType)){
			sql.append(" t2.TASK_NAME as STA_NAME,");
			statField = "t2.TASK_NAME";
			fristHead = "任务名称";
		}else if("date".equals(statisticType)){
			sql.append(" t1.DATE_ID as STA_NAME,");
			statField = "t1.DATE_ID";
			fristHead = "日期";
		}
		//统计各项数据
		sql.append(" SUM(t1.CALL_COUNT) as CALL_COUNT,");
		sql.append(" SUM(t1.TOTAL_TIME) as TOTAL_TIME,");
		sql.append(" SUM(t1.BILL_TIME_6) as BILL_TIME_6,");
		sql.append(" SUM(t1.BILL_TIME_60) as BILL_TIME_60");
		
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_BILL_STAT");
		sql.append(" from "+Constants.getStatSchema()+"."+ycstatTableInfo.get("TARGET_TABLE_NAME")+" t1");
		sql.append(" , CC_ENT t3 ");
		
		//区分不同的统计维度
		if("task".equals(statisticType)){
			sql.append(","+getTableName(entId,"CC_TASK")+" t2 ");
			sql.append(" where t1.TASK_ID = t2.TASK_ID");
		}else if("date".equals(statisticType)){
			sql.append(" where t1.DATE_ID is not null");
		}
		
		//固定查询条件
		sql.append("and t1.ENT_ID = t3.ENT_ID");
		sql.append(entId, " and t1.ENT_ID = ?");
		sql.append(monthId,"and t1.MONTH_ID=?");
		sql.append(" group by t3.ENT_NAME,t1.MONTH_ID,"+statField+" order by t3.ENT_NAME asc,"+statField+" asc");
				
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		headers.add(" 序号 ");
		headers.add(fristHead);
		headers.add(" 企业名称");
		headers.add(" 通话次数 ");
		headers.add(" 通话时长 ");
		headers.add(" 6秒计费时长 (数量)");
		headers.add(" 60秒计费时长 (数量)");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(3600);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		int i = 0;
		List<String> list = null;
		String entName = getEntInfo(entId).get("ENT_NAME");

		if(data!=null&&data.size()>0){
			for(Map<String,String> map : data){
				list = new ArrayList<String>();
				list.add(String.valueOf(++i));
				list.add(map.get("STA_NAME"));
				list.add(map.get("ENT_NAME"));
		    	list.add(map.get("CALL_COUNT"));
		    	list.add(parseTime(map.get("TOTAL_TIME")));
			    list.add(map.get("BILL_TIME_6"));
			    list.add(map.get("BILL_TIME_60"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file,"企业计费统计表("+entName+").xlsx");
		} catch (Exception e) {
			e.printStackTrace();
		};
	}
	
	/**
	 * 导出企业月统计表
	 * @throws SQLException
	 */
	public void actionForExportEntMonthBill() throws SQLException{
		HttpServletRequest request = this.getRequest();
		EasySQL sql = new EasySQL("select * from CC_ENT_BILL where 1=1");
		sql.append(request.getParameter("entId"), " and ENT_ID = ?");
		String monthId = request.getParameter("monthId");
		if(StringUtils.isNoneBlank(monthId)){
			sql.append(monthId.replace("month_", ""), "and MONTH_ID = ?");
		}
		List<Map<String, String>> data = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		headers.add(" 企业名称");
		headers.add(" 月份");
		headers.add(" 计费号码 ");
		headers.add(" 计费单元 ");
		headers.add(" 通道数");
		headers.add(" 套餐内计费时长");
		headers.add(" 基础套餐费（元）");
		headers.add(" 超套餐费用（元）");
		headers.add(" 总通话次数");
		headers.add(" 总通话时长");
		headers.add(" 总计费时长");
		headers.add(" 超套餐计费时长");
		headers.add(" 套餐内通话时长");
		headers.add(" 基础账单费（元）");
		headers.add(" 超套账单费（元）");
		headers.add(" 费用合计（元）");
		headers.add(" 处理状态");
		headers.add(" 更新时间");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(4000);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		List<String> list = null;
		if(data!=null&&data.size()>0){
			for(Map<String,String> map : data){
				list = new ArrayList<String>();
				list.add(map.get("ENT_NAME"));
		    	list.add(map.get("MONTH_ID"));
		    	list.add(map.get("FEE_CODE"));
			    list.add(map.get("FEE_UNIT_DESC"));
			    list.add(map.get("CALL_LICENSE"));
		    	list.add(map.get("BASE_TIMES"));
			    list.add(parseFee(map.get("BASE_FEE")));
			    list.add(parseFee(map.get("OVER_FEE")));
			    list.add(map.get("CALL_COUNT"));
		    	list.add(map.get("CALL_TIME"));
		    	list.add(map.get("BILL_TIME"));
		    	list.add(map.get("OVER_BILL_TIME"));
		    	list.add(map.get("SUM_BASE_TIME"));
			    list.add(parseFee(map.get("SUM_BASE_FEE")));
			    list.add(parseFee(map.get("SUM_OVER_FEE")));
			    list.add(parseFee(map.get("SUM_FEE")));
			    list.add(parseState(map.get("STAT_STATE")));
			    list.add(map.get("LAST_UPDATE_TIME"));
				excelData.add(list);
			}
		}
		try {
			ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file,"企业月账单统计表.xlsx");
		} catch (Exception e) {
			e.printStackTrace();
		};
	}
	
	/**
	 * 虚拟运营话务量统计导出
	 */
	@SuppressWarnings({ "resource", "deprecation" })
	public void actionForExportEntCityBillStat(){
		HttpServletRequest request = this.getRequest();
		String entId = request.getParameter("entId");
		String entName = request.getParameter("entName");
		String monthId = request.getParameter("monthId").replace("month_", "");
		String statisticType = request.getParameter("statisticType");
		Map<String, String> ycstatTableInfo = getYcstatTableByTaget("CC_RPT_CALL_STAT");

		EasySQL sql = new EasySQL("select t6.NODE_NAME,");
		if("2".equals(statisticType)){		//按机构
			sql.append("t3.CENTER_ID,t3.NODE_NAME STAT_NAME,");
		}else{						//按地市
			sql.append("t2.NODE_NAME STAT_NAME,");
		}
		//统计各项数据		
		sql.append(" SUM(t1.CALL_COUNT) OUT_CALL_COUNT,");					//呼出次数
		sql.append(" SUM(t1.CONN_SUCC_COUNT) CONN_SUCC_COUNT,");			//接通次数
		sql.append(" SUM(case when t1.FEE_TIME>=1 then t1.FEE_TIME else 0 end) PER_CAPITA_TIME,");	//通话时长
		sql.append(" COUNT(distinct t4.USER_ID) AGENT_COUNT,");				//坐席人数
		sql.append(" COUNT(distinct t1.AGENT_ID) ONLINE_AGENT_COUNT");		//上线人力
		sql.append(" from CC_EC_NODE t2");
		sql.append(" left join CC_EC_NODE t3 on t2.NODE_CODE = t3.P_DEPT_CODE and t2.ENT_ID = t3.ENT_ID");
		sql.append(" left join "+getTableName(entId, "CC_BUSI_USER")+" t4 on t3.CENTER_ID = t4.ENT_ID");
		sql.append(" left join CC_USER t7 on t7.USER_ID = t4.USER_ID");
		sql.append(" left join "+getTableName(entId, "CC_ROLE")+" t8 on t4.ROLE_ID = t8.ROLE_ID");
		
		sql.append(" left join CC_EC_NODE t6 on t2.P_DEPT_CODE = t6.NODE_CODE and t2.ENT_ID = t6.ENT_ID");
		sql.append(" left join (");
		sql.append("select * from " + Constants.getStatSchema()+"."+ycstatTableInfo.get("TARGET_TABLE_NAME") + " where 1=1 ");
		sql.append(monthId, "and MONTH_ID = ?");
		sql.append(" ) t1 on t4.USER_ID = t1.AGENT_ID");
		sql.append(" where 1=1");
		sql.append(" and t2.NODE_TYPE=2");
		sql.append(" and t3.NODE_TYPE=4");
		sql.append(entId,"and t3.ENT_ID=?");
		sql.append(" and t7.USER_STATE<>9");
		sql.append(Constants.ROLE_TYPE_ADMIN," and t8.ROLE_TYPE <> ?");
		sql.append(" group by t2.P_DEPT_CODE,t6.NODE_NAME,t2.IDX_ORDER");
		if("2".equals(statisticType)){		//按机构
			sql.append(",t3.CENTER_ID,t3.NODE_NAME");
		}else{						//按地市
			sql.append(",t2.NODE_NAME");
		}
		sql.append("order by t2.P_DEPT_CODE,t2.IDX_ORDER");
		
		List<Map<String, String>> data = null;
		try {
			data = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		} catch (SQLException e1) {
			this.error(e1.getMessage(), e1);
		}
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		headers.add(" 省份");
		if("2".equals(statisticType)){		//按机构
			headers.add(" 机构名称");
		}else{								//按地市
			headers.add(" 地市");
		}
		headers.add(" 坐席数 ");
		headers.add(" 上线人力");
		headers.add(" 呼出数");
		headers.add(" 接通数");
		headers.add(" 计费时长（分钟）");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(4000);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		List<String> list = null;
		if(data!=null&&data.size()>0){
			for(Map<String,String> map : data){
				list = new ArrayList<String>();
				list.add(map.get("NODE_NAME"));
		    	list.add(map.get("STAT_NAME"));
		    	list.add(map.get("AGENT_COUNT"));
			    list.add(parseParam(map.get("ONLINE_AGENT_COUNT")));
			    list.add(parseParam(map.get("OUT_CALL_COUNT")));
			    list.add(parseParam(map.get("CONN_SUCC_COUNT")));
		    	list.add(parseParam(map.get("PER_CAPITA_TIME")));
				excelData.add(list);
			}
		}
		try {
			int rowIndex = 0;
			XSSFWorkbook xssf = new XSSFWorkbook();
			XSSFSheet sheet = xssf.createSheet();
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));
			XSSFRow rowTitle = sheet.createRow(rowIndex++);
			CellStyle cellStyle = xssf.createCellStyle();
			//cellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER); // 水平居中
			XSSFFont font = xssf.createFont(); 
			font.setFontHeight(20);
			cellStyle.setFont(font);
			XSSFCell cellTitle = rowTitle.createCell(0);
			cellTitle.setCellValue("虚拟运营话务量统计（"+entName+" - "+monthId+"）");
			cellTitle.setCellStyle(cellStyle);
			
			XSSFRow rowHeader = sheet.createRow(rowIndex++);
			for(int i = 0; i < styles.size(); i++){
				ExcelHeaderStyle excelHeaderStyle = styles.get(i);
				XSSFCell cell = rowHeader.createCell(i);
				CellStyle style = xssf.createCellStyle();
				sheet.setColumnWidth(i, excelHeaderStyle.getWidth());
				cell.setCellValue(excelHeaderStyle.getData());
				cell.setCellStyle(style);
			}
			
			for(int i = 0; i < excelData.size(); i++){
				XSSFRow row = sheet.createRow(rowIndex++);
				List<String> dataList = excelData.get(i);
				for (int ii = 0; ii < dataList.size(); ii++) {
					row.createCell(ii).setCellValue(dataList.get(ii));
				}
			}
			
			xssf.write(new FileOutputStream(file.getAbsolutePath()));
			
			//ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file,"虚拟运营话务量统计（"+entName+"-"+monthId+"）.xlsx");
		} catch (Exception e) {
			e.printStackTrace();
		};
	}
	
	public void actionForExportEntCityBillMonthStat(){
		HttpServletRequest request = getRequest();
		String entId = request.getParameter("entId");
		String entName = request.getParameter("entName");
		String monthId = request.getParameter("monthId").replace("month_", "");
		String statisticType = request.getParameter("statisticType");
		
		EasySQL sql = new EasySQL("select t2.CITY_NAME,");
		String groupClo = "t2.CITY_NAME,";
		if("2".equals(statisticType)){				//按机构
			sql.append("t2.NODE_NAME STAT_NAME,");
			groupClo+="t2.NODE_NAME";
		}else if("1".equals(statisticType)){		//按地市
			sql.append("t2.AREA_NAME STAT_NAME,");
			groupClo+="t2.AREA_NAME";
		}else{								//按坐席
			sql.append("t2.NODE_NAME ENT_NAME,t1.AGENT_ID STAT_NAME,");
			groupClo+="t2.NODE_NAME, t1.AGENT_ID";
		}
		//统计各项数据		
		sql.append(" COUNT(distinct t1.AGENT_ID) ONLINE_AGENT_COUNT,");		//上线人力
		sql.append(" sum(distinct t1.FEE_UNIT) FEE_UNIT,");      //计费方式，1 按6秒计费  2 按分钟计费
		sql.append(" sum(distinct t1.BASE_FEE) BASE_FEE,");      //套餐内通话费（单位：分）
		sql.append(" sum(distinct t1.BASE_TIME) BASE_TIME,");    // 套餐内通话时长
		sql.append(" sum(t1.BASE_FEE) S_BASE_FEE,");    //包月套餐费（合计）
		sql.append(" sum(case when t1.FEE_UNIT = 1 then t1.FEE_TIME_6 else t1.FEE_TIME_60 end) S_FEE_TIME ,");    //计费时长(合计）
		sql.append(" sum(case when t1.FEE_UNIT = 1 then t1.OVER_TIME_6 else t1.OVER_TIME_60 end) S_OVER_TIME,");  //超套餐计费时长（合计）
		sql.append(" sum(case when t1.FEE_UNIT = 1 then t1.OVER_FEE_6 else t1.OVER_FEE_60 end) S_OVER_FEE,");     //超套餐费用（合计）
		sql.append(" sum(case when t1.FEE_UNIT = 1 then t1.OVER_FEE_6+BASE_FEE else t1.OVER_FEE_60+BASE_FEE end) TOTAL_OVER_FEE");     //费用合计
		
		sql.append(" from CC_FEE_AGENT_BILL t1,cc_ec_node_snapshot t2");
		
		sql.append(" where 1=1");
		sql.append(" and t1.ENT_ID = t2.CENTER_ID");
		sql.append(entId," and t1.P_ENT_ID=?");
		sql.append(monthId, " and t1.MONTH_ID = ?");
		sql.append(entId," and t2.ENT_ID=?");
		sql.append(monthId, " and t2.MONTH_ID = ?");
		sql.append(" group by ");
		sql.append(groupClo);
		sql.append(" order by ");
		sql.append(groupClo);
		
		List<Map<String, String>> queryForList = null;
		try {
			 queryForList = getQuery().queryForList(sql.getSQL(), sql.getParams(),new MapRowMapperImpl());
		} catch (Exception e) {
			this.error(e.getMessage(), e);
		}
		
		if (queryForList==null) {
			return;
		}
		
		//组装表头
		File file=FileKit.createTempFile(RandomKit.randomStr()+".xlsx");
		List<String> headers=new ArrayList<String>();
		headers.add(" 省份");
		if("2".equals(statisticType)){		//按机构
			headers.add(" 机构名称");
		}else if("1".equals(statisticType)){		//按地市
			headers.add(" 地市");	
		}else{								//按坐席
			headers.add(" 机构名称");
			headers.add(" 坐席");
		}
		
		
		headers.add(" 上线人力");
		headers.add(" 计费方式（1 按6秒计费  2 按分钟计费）");
		headers.add(" 套餐内通话费（元）");
		headers.add(" 套餐内通话时长");
		headers.add(" 合计包月套餐费（元）");
		headers.add(" 合计计费时长");
		headers.add(" 合计超套餐计费时长");
		headers.add(" 合计超套餐费用（元）");
		headers.add(" 费用合计（元）");
		
		List<ExcelHeaderStyle> styles=new ArrayList<ExcelHeaderStyle>();
		for(String header:headers){
			ExcelHeaderStyle style=new ExcelHeaderStyle();
			style.setData(header);
			style.setWidth(4000);
			styles.add(style);
		}
		List<List<String>> excelData=new ArrayList<List<String>>();
		List<String> list = null;
		if(queryForList!=null&&queryForList.size()>0){
			for(Map<String,String> map : queryForList){
				list = new ArrayList<String>();
				
				list.add(map.get("NODE_NAME"));
				if("3".equals(statisticType)){		
					list.add(map.get("ENT_NAME"));
				}
		    	list.add(map.get("STAT_NAME"));
			    list.add(parseParam(map.get("ONLINE_AGENT_COUNT")));
			    list.add(parseFeeType(map.get("FEE_UNIT")));
			    list.add(parseNum(map.get("BASE_FEE"),"100"));
			    list.add(parseParam(map.get("BASE_TIME")));
			    BigDecimal b1 = new BigDecimal(map.get("ONLINE_AGENT_COUNT"));
			    BigDecimal b2 = new BigDecimal(parseNum(map.get("BASE_FEE"),"100"));
			    list.add(b1.multiply(b2).intValue()+"");
			    list.add(parseParam(map.get("S_FEE_TIME")));
			    list.add(parseParam(map.get("S_OVER_TIME")));
			    list.add(parseNum(map.get("S_OVER_FEE"),"100"));
			    list.add(parseNum(map.get("TOTAL_OVER_FEE"),"100"));
				excelData.add(list);
			}
		}
		try {
			int rowIndex = 0;
			XSSFWorkbook xssf = new XSSFWorkbook();
			XSSFSheet sheet = xssf.createSheet();
			sheet.addMergedRegion(new CellRangeAddress(0, 0, 0, 6));
			XSSFRow rowTitle = sheet.createRow(rowIndex++);
			CellStyle cellStyle = xssf.createCellStyle();
			//cellStyle.setAlignment(XSSFCellStyle.ALIGN_CENTER); // 水平居中
			XSSFFont font = xssf.createFont(); 
			font.setFontHeight(20);
			cellStyle.setFont(font);
			XSSFCell cellTitle = rowTitle.createCell(0);
			cellTitle.setCellValue("虚拟运营月账单统计（"+entName+" - "+monthId+"）");
			cellTitle.setCellStyle(cellStyle);
			
			XSSFRow rowHeader = sheet.createRow(rowIndex++);
			for(int i = 0; i < styles.size(); i++){
				ExcelHeaderStyle excelHeaderStyle = styles.get(i);
				XSSFCell cell = rowHeader.createCell(i);
				CellStyle style = xssf.createCellStyle();
				sheet.setColumnWidth(i, excelHeaderStyle.getWidth());
				cell.setCellValue(excelHeaderStyle.getData());
				cell.setCellStyle(style);
			}
			
			for(int i = 0; i < excelData.size(); i++){
				XSSFRow row = sheet.createRow(rowIndex++);
				List<String> dataList = excelData.get(i);
				for (int ii = 0; ii < dataList.size(); ii++) {
					row.createCell(ii).setCellValue(dataList.get(ii));
				}
			}
			
			xssf.write(new FileOutputStream(file.getAbsolutePath()));
			
			//ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
			renderFile(file,"虚拟运营月账单统计（"+entName+"-"+monthId+"）.xlsx");//通过文件流的形式导出
		} catch (Exception e) {
			e.printStackTrace();
		}
	}
	
	
	private String parseParam(String param){
		if(StringUtils.isBlank(param))	return "0";
		return param;
	}
	
	/**
	 * 处理状态
	 */
	private String parseState(String state) {
		if("0".equals(state)) return "待处理";
		if("1".equals(state)) return "处理中";
		return "处理完成";
	}
	
	/**
	 * 计费方式，1 按6秒计费  2 按分钟计费
	 */
	private String parseFeeType(String value) {
		if("1".equals(value)) return "按6秒计费";
		if("2".equals(value)) return "按分钟计费";
		return "";
	}
	
	/**
	 * 格式化Fee
	 */
	private String parseFee(String fee) {
		if(StringUtils.isBlank(fee)) return "0";
		int f = Integer.parseInt(fee);
		return (f * 0.01)+"";
	}

	/**
	 * 格式化秒,将秒数转成 "00:00:00"格式
	 * @param value
	 * @return
	 */
	private String parseTime(String value) {
	    if(!StringUtils.isNumeric(value)){
	    	return "00:00:00";
	    }
	    int theTime = Integer.valueOf(value);// 秒
	    int theTime1 = 0;// 分
	    int theTime2 = 0;// 小时
	    if(theTime > 60) {
	        theTime1 = theTime/60;
	        theTime = theTime%60;
	            if(theTime1 > 60) {
	            theTime2 = theTime1/60;
	            theTime1 = theTime1%60;
	            }
	    }
	    String result = ""+theTime+"";
	        if(theTime1 > 0) {
	        result = ""+theTime1+":"+result;
	        }
	        if(theTime2 > 0) {
	        result = ""+theTime2+":"+result;
	        }
	    return result;
	}
	
	@Override
	protected String getResId() {
		return null;
	}
	/**
	 * 获取统计库中的最新统计表名和统计时间
	 * @param tableName
	 * @return
	 */
	private Map<String, String> getYcstatTableByTaget(String tableName){
		Map<String, String> tabInfo = null;
		try {
			String sql = "SELECT TARGET_TABLE_NAME,UPDATE_TIME from "+Constants.getStatSchema()+".cc_stat_table_info where TABLE_ID = ?  ";
			tabInfo = this.getQuery().queryForRow(sql, new String[] { tableName },new MapRowMapperImpl());
			//设置默认的统计表
			if(tabInfo == null){
				tabInfo = new HashMap<>();
				tabInfo.put("TARGET_TABLE_NAME", tableName+"1");
				tabInfo.put("UPDATE_TIME", EasyCalendar.newInstance().getDateTime("-"));
			}
		} catch (Exception ex) {
			ex.printStackTrace();
		}
		return tabInfo;
	}
	
	/**
     * 格式化小数
     *
     * @param value1
     * @param value2
     * @return
     */
    private String parseNum(String value1, String value2) {
        if (!StringUtils.isNumeric(value1) || !StringUtils.isNumeric(value2)
                || "0".equals(value1) || "0".equals(value2)) {
            return "0";
        }
        BigDecimal f1 = new BigDecimal((float) Integer.valueOf(value1) / Integer.valueOf(value2)).setScale(2, BigDecimal.ROUND_HALF_UP);
        double f3 = f1.doubleValue();
        return f3 + "";
    }

    /**
     * 导出进线接入记录
     * @throws SQLException
     */
    public void actionForMemberAccessRecord() throws SQLException{
        HttpServletRequest request = this.getRequest();
        String entId = request.getParameter("entId");
        String configId = request.getParameter("configId");
        String memberName = request.getParameter("memberName");
        String startTime = request.getParameter("startTime");
        String endTime = request.getParameter("endTime");
        String sessionId = request.getParameter("sessionId");
        String channelKey = request.getParameter("channelKey");

        // 构建查询SQL
        EasySQL sql = new EasySQL("select t1.SESSION_ID, t2.CHANNEL_NAME, t1.MEMBER_NAME, t1.CREATE_TIME, t1.LEVEL_NAMES");
        sql.append(" from YCBUSI.CC_MEDIA_MEMBER_RECORD t1");
        sql.append(" left join YCUSER.CC_CHANNEL t2 on t1.CHANNEL_KEY=t2.CHANNEL_KEY and t2.ENT_ID=?", entId);
        sql.append(" where 1=1");

        sql.append(configId, " and t1.CONFIG_ID = ?");
        sql.append(sessionId, " and t1.SESSION_ID = ?");
        sql.append(channelKey, " and t1.CHANNEL_KEY = ?");

        if(StringUtils.isNotBlank(memberName)) {
            sql.append(" and (t1.MEMBER_NAME like ? or t1.LEVEL_NAMES like ?)",
                "%" + memberName + "%", "%" + memberName + "%");
        }

        if(StringUtils.isNotBlank(startTime)) {
            sql.append(" and t1.CREATE_TIME >= ?", startTime);
        }

        if(StringUtils.isNotBlank(endTime)) {
            sql.append(" and t1.CREATE_TIME <= ?", endTime);
        }

        sql.append(" order by t1.CREATE_TIME desc");

        // 查询数据
        this.getQuery().setMaxRow(50000);
        List<Map<String, String>> data = this.getQuery().queryForList(
            sql.getSQL(),
            sql.getParams(),
            new MapRowMapperImpl()
        );

        // 创建临时文件
        File file = FileKit.createTempFile(RandomKit.randomStr() + ".xlsx");

        // 组装表头
        List<String> headers = new ArrayList<>();
        headers.add("序号");
        headers.add("访客标识");
        headers.add("渠道名称");
        headers.add("匹配身份策略");
        headers.add("进线时间");
        headers.add("访客会员名称");

        List<ExcelHeaderStyle> styles = new ArrayList<>();
        for(String header : headers) {
            ExcelHeaderStyle style = new ExcelHeaderStyle();
            style.setData(header);
            style.setWidth(4000);
            styles.add(style);
        }

        // 组装数据
        List<List<String>> excelData = new ArrayList<>();
        int i = 0;
        if(data != null && !data.isEmpty()) {
            for(Map<String, String> map : data) {
                List<String> list = new ArrayList<String>();
                list.add(String.valueOf(++i));
                list.add(StringUtils.trimToEmpty(map.get("SESSION_ID")));
                list.add(StringUtils.trimToEmpty(map.get("CHANNEL_NAME")));
                list.add(StringUtils.trimToEmpty(map.get("MEMBER_NAME")));
                list.add(StringUtils.trimToEmpty(map.get("CREATE_TIME")));
                list.add(StringUtils.trimToEmpty(map.get("LEVEL_NAMES")));
                excelData.add(list);
            }
        }

        try {
            ExcelUtils.getInstance().exportObjects2Excel(excelData, styles, file.getAbsolutePath());
            renderFile(file, "进线接入记录_" + System.currentTimeMillis() + ".xlsx");
        } catch (Exception e) {
            this.error("导出失败", e);
            e.printStackTrace();
        }
    }

    /**
     * 批量保存会员身份回复语配置
     */
    public void actionForSaveMemberAutoConfig() throws SQLException {
        String channelKey = getRequest().getParameter("channelKey");
        String confKey = getRequest().getParameter("confKey");
        String configData = getRequest().getParameter("configData");

        if(StringUtils.isBlank(channelKey) || StringUtils.isBlank(confKey) || StringUtils.isBlank(configData)){
            renderText("{\"state\":0,\"msg\":\"参数不完整\"}");
            return;
        }

        try {
            // 先删除该渠道该配置项的所有记录
            EasySQL deleteSql = new EasySQL("delete from YCUSER.CC_CHANNEL_MEMBER_AUTOCONF");
            deleteSql.append(" where CHANNEL_KEY=? and CONF_KEY=?", channelKey, confKey);
            this.getQuery().execute(deleteSql.getSQL(), deleteSql.getParams());

            // 解析配置数据并批量插入
            com.alibaba.fastjson.JSONObject configJson = com.alibaba.fastjson.JSONObject.parseObject(configData);
            for(String memberId : configJson.keySet()) {
                String txtContent = configJson.getString(memberId);
                if(StringUtils.isNotBlank(txtContent)) {
                    EasySQL insertSql = new EasySQL("insert into YCUSER.CC_CHANNEL_MEMBER_AUTOCONF");
                    insertSql.append(" (CHANNEL_KEY, MEMBER_ID, CONF_KEY, TXT_CONTENT, CREATE_TIME)");
                    insertSql.append(" values (?, ?, ?, ?, sysdate)", channelKey, memberId, confKey, txtContent);
                    this.getQuery().execute(insertSql.getSQL(), insertSql.getParams());
                }
            }

            renderText("{\"state\":1,\"msg\":\"保存成功\"}");
        } catch (Exception e) {
            this.error("批量保存失败", e);
            renderText("{\"state\":0,\"msg\":\"保存失败：" + e.getMessage() + "\"}");
        }
    }

	@Override
	protected String getAppDatasourceName() {
		return Constants.READ_DS_NAME;
	}
}
