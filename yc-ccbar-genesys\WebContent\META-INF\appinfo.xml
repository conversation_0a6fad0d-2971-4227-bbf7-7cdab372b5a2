<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="yc-ccbar" name="云呼ccbar(genesys_websocket),恢复原版本，取消统一登录" package-by="ljp46" package-time="2025-08-04 16:33:01" version="1.0#20250814-1">
    <datasources>
        <datasource description="云呼平台数据源(写1)" isnull="true" name="yc-wirte-ds-1"/> 
        <datasource description="云呼平台数据源(写2)" isnull="true" name="yc-wirte-ds-2"/> 
        <datasource description="读数据源" isnull="true" name="yc-read-ds"/> 
    </datasources>
    <description>
        1.0#20250814-1
            1.优化超长消息文本存储逻辑，避免频繁写库。
            1.增加超过8小时的缓存数据持久化逻辑，避免设置太长时间，频繁更新导致缓存失效。
        1.0#20250710-1
            1.优化坐席所在MQ队列 缓存失效问题。
            2.整理常量定义。
            3.优化坐席工作台服务上限同步逻辑。
            4.优化写聊天记录时报chatSessionId为空的问题。
        1.0#20250626-1
            1.增加坐席端会话引用功能。
            2.增加对接坐席助手。
            3.优化“强制签出”功能无法签出全媒体的问题。
            4.优化websocket连接异常中断问题。
    </description>
    <versionHistory>
        1.0#20250613-1
        1.修复CheckGenesysAgentThread问题
        1.0#20250514-1
        1.全媒体会话转移时支持选择渠道配置的转移技能组。
        1.0#20250116-1
        1.解决全媒体聊天记录不显示机器人引导问和CSS工单消息。
        1.0#20250114-1 修复agents集合并发操作异常
        1.0#20250108-1
        1.优化全媒体历史记录加载接口查询SQL，移除MSG_TIMESTAMP排序字段。
        1.0#20241212-1
        1.优化部分聊天记录超长写入数据库失败的问题，必须配合yc-mediagw（1.01#20241212-1）升级。
        1.0#20240105-1
        1.优化日志输出。
        1.0#20241107-1
        1.优化三方挂断问题
        1.0#20240102-1
        1.优化MQ消费者监听线程，防止消费者监听未关闭导致产生大量死信队列。
        2.优化语音坐席签入后无法修改”可呼“状态的问题。
        3.增加转移到技能组逻辑。（同步升级yc-mediacenter，online）。
        1.0#20230509-1
        1.优化全媒体坐席监控页面发起的改变坐席状态变更事件处理，优化语音坐席监控页面发起的签出事件处理。
        2.移除死循环中的队列堵塞方法，将add()替换成offer()，take()替换成 poll()。
        1.0#20230306-1 新增坐席端加载排队信息返回坐席所关联的渠道排队和技能组排队
        1.0#20230223-1 优化通过MQ发送消息给坐席找不到sessionId报错的问题。
        1.0#20221212-1 聊天记录加载接口 兼容sessionId查多个渠道的数据。
        1.0#20221014-1 js链接处理
    </versionHistory>
</application>
