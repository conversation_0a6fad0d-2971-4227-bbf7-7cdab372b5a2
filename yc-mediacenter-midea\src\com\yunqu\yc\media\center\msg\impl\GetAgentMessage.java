package com.yunqu.yc.media.center.msg.impl;


import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.*;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;
import com.yunqu.yc.media.center.util.CacheUtil;
import com.yunqu.yc.media.center.util.MediaCacheUtil;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.List;

/**
 * 获得可服务的坐席,根据按键获取坐席信息
 * <AUTHOR>
 *
 */
public class GetAgentMessage extends Message{

	@Override
	public void onMessage(MessageModel messageModel) {
		
		
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		Channel channel = entContext.getChannel(messageModel.getChannelId());
		
		String sessionId = messageModel.getSessionId();
		messageModel.setEvent("getAgent");
		messageModel.setMsgType("text");
		
		ChannelKey channelKey  = channel.getChannelKeyByCode(messageModel.getMsgContent());
		long tid = Thread.currentThread().getId();
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] >> GetAgentMessage("+sessionId+") ->  "+messageModel.toString());
		//如果输入的渠道信息错误,则显示导航菜单
		if(channelKey == null){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] >> GetAgentMessage("+sessionId+") not found model[ChannelKey] ->  channelKey:"+channel.getChannelKey()+",keycode:"+messageModel.getMsgContent());
			messageModel.setMsgContent(channel.getAutoConfig().getWelcomeMsg());
			messageModel.setSender("system");
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			return ;
		}
		//黑名单用户
		if(entContext.checkBlackList(sessionId)){
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Black User["+channelKey.getKeyCode()+","+channelKey.getKeyName()+"]->"+sessionId);
			messageModel.setEvent("end");
			messageModel.setMsgContent(channel.getAutoConfig().getBlacklistMsg());
			messageModel.setSender("system");
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			return ;
		}		

		//用户正在排队，不能重复进入排队。
		if(entContext.getQueueUser(sessionId)!= null){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 用户正在排队，不能重复进入排队，sessionId:"+sessionId);
			return;
		}
		//用户已接入，不能再次接入或进入排队
		if(entContext.getUser(sessionId)!=null){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 用户已接入，不能再次接入或进入排队，userSession:"+entContext.getUser(sessionId));
			return;
		}
		messageModel.setChannelId(channel.getChannelId());
		messageModel.setChannelType(channel.getChannelType());
		messageModel.setChannelName(channel.getChannelName());
		messageModel.setChannelKey(channel.getChannelKey());

		//使用内存对象
		UserSession userSession = UserSession.getInstance(sessionId);
		userSession.setChatSessionId(messageModel.getChatSessionId());
		userSession.setMessageModel(messageModel);
		userSession.setBeginTime(EasyCalendar.newInstance().getDateTime("-"));
		userSession.setEntId(messageModel.getEntId());
		userSession.setSessionId(messageModel.getSessionId());
		userSession.setRequestTime(messageModel.getRequestTime());
		userSession.setUserInfo(messageModel.getUserInfo());
		userSession.setChannel(channel);
		userSession.setChannelKey(channelKey);
		userSession.setLevel(messageModel.getUserInfo().getString("level"));
		userSession.setMemberId(messageModel.getUserInfo().getString("memberId"));
		userSession.setMemberGroupId(messageModel.getUserInfo().getString("memberGroupId"));

		//2.0#20210518-1
		if(!userSession.isFee()) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 用户已接入，不能进入排队，当前用户状态："+userSession.getState()+"，userSession:"+entContext.getUser(sessionId));
			return;
		}


		//20221130 熟客优先策略
		try {
			boolean intoLastAent = intoLastAent(channel, channelKey, userSession, messageModel);
			if(intoLastAent) return;
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}


		SkillGroup skillGroup = null;

		try {
			//根据访客匹配的会员身份策略配置找技能组
			String memberGroupId = userSession.getMemberGroupId();
			if(StringUtils.isNotBlank(memberGroupId)){
				skillGroup = channelKey.getSkillGroupById(memberGroupId);
				//从企业中查询技能组
				if(skillGroup==null){
					skillGroup = entContext.getSkillGroup(memberGroupId);
				}
			}
			if(skillGroup==null){
				skillGroup = channelKey.chooseSkillGroup(userSession.getLevel());
			}
			userSession.setSkillGroup(skillGroup);
			messageModel.getUserInfo().put("skillGroupId",skillGroup.getSkillGroupId());
			messageModel.getUserInfo().put("skillGroupName",skillGroup.getSkillGroupName());
		} catch (Exception e) {
			// 找不到key对应的技能组
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] >> GetAgentMessage("+sessionId+") not found model[SkillGroup] ->  channelKey:"+channel.getChannelKey()+",keycode:"+messageModel.getMsgContent());
			messageModel.setMsgContent("[102]" + channel.getAutoConfig().getAgentOfflineMsg());
			messageModel.setSender("system");
			String msg = messageModel.toString(RandomKit.randomStr());
			ProducerBroker.sendUserMessage(sessionId,msg);
			//系统回复语直接入库
			userSession.saveMessage(JSONObject.parseObject(msg),4);
			return;
		}
		//2.0#20201014-1 添加接入记录
//		JSONObject data = new JSONObject();
//		data.put("ENT_ID", messageModel.getEntId());
//		data.put("SESSION_ID", sessionId);
//		data.put("CHANNEL_ID", channel.getChannelId());
//		data.put("CHANNEL_KEY_ID", channelKey.getChannelKeyId());
//		data.put("SKILL_GROUP_ID", skillGroup.getSkillGroupId());
//		data.put("CHAT_SESSION_ID", messageModel.getChatSessionId());
//		AccessRecord.getInstance().saveAccessRecord(data);
		
		int queueNo = skillGroup.addQueue(userSession);
		if(!skillGroup.hasOnlineAgent()||!skillGroup.hasFeeAgent()){
			//渠道按键排队队列只是记录排队，真正排队号是在技能组队列中
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> sessionId("+userSession.getSessionId()+"),queueNo：->"+queueNo);
			if(queueNo>0){
//				String msg = channel.getAutoConfig().getQueueMsg();
				String msg = channel.getAutoConfig().getQueueMsgByLevel(userSession.getUserInfo().getString("levelCode"));
				msg = StringUtils.replace(msg, "#sortPos#", queueNo+"");
				messageModel.setMsgContent(msg);
				messageModel.setEvent("queue");//标识为正在排队，不做任何回复
				messageModel.setQueueNo(queueNo+"");
				messageModel.setSender("system");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
				
				//更新接入记录，进入排队
				JSONObject udata = new JSONObject();
				JSONObject data = new JSONObject();
				data.put("ENT_ID", messageModel.getEntId());
				udata.put("QUEUE_START_TIME", EasyCalendar.newInstance().getDateTime("-"));
				udata.put("IS_IN_QUEUE", 1);
				udata.put("CHAT_SESSION_ID", messageModel.getChatSessionId());
				AccessRecord.getInstance().updateAccessRecord(udata);
				
				//用户接入排队后提示语 IN_QUEUE_MSG 例如：排队过程中，您可先提前输入您的问题或需求
				String afterMsg = channel.getAutoConfig().getInQueueAfterMsg();
				if(StringUtils.isNotBlank(afterMsg)) {
					try {
						Thread.sleep(300);
						messageModel.setMsgType("text");
						messageModel.setEvent("System");
						messageModel.setMsgContent(afterMsg);
						messageModel.setSender("system");
						msg = messageModel.toString(RandomKit.randomStr());
						ProducerBroker.sendUserMessage(sessionId,msg);
						//系统回复语直接入库
						userSession.saveMessage(JSONObject.parseObject(msg),4);
					} catch (Exception e) {
						MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
					}
				}
			}
		}	
	}
	/**
	 * 熟客优先
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2022/11/30 12:33
	 * @return: boolean
	 */
	private boolean intoLastAent(Channel channel,ChannelKey channelKey,UserSession userSession,MessageModel messageModel) throws Exception{
		JSONObject channelConfig = channel.getChannelConfig();
		String sessionId = userSession.getSessionId();
		String priortiyLatestAgent = channelConfig.getString("PRIORTIY_LATEST_AGENT");//熟客优先配置，1：开启，其他：关闭
		if(!StringUtils.equals("1",priortiyLatestAgent)){
			return false;
		}
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，配置："+ priortiyLatestAgent);
		String entId = channel.getEntId();
		EntContext context = EntContext.getContext(entId);
		String skillId;
		String agentId;
		String sql = "select GROUP_ID,AGENT_ID from "+context.getTableName("CC_MEDIA_RECORD")+" where SERVER_STATE=3 and SESSION_ID=? and END_TIME>=? order by END_TIME desc";
		String priortiyLatestTime = channelConfig.getString("PRIORTIY_LATEST_TIME");//查询范围：1 最近24小时，3 最近3天，7 最近7天，15 最近15天，30 最近30天
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，查询范围："+ priortiyLatestTime);
		int timeInt = Integer.parseInt(priortiyLatestTime);
		String cacheKey = "LAST_AGENT_" + channelKey.getChannelKeyId() + "_" + sessionId;
		String lastChatFlag = MediaCacheUtil.get(cacheKey);
		if (lastChatFlag!=null){
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，缓存中存在上次接入信息，直接使用："+lastChatFlag);
			String[] split = lastChatFlag.split("&&");
			skillId = split[0];
			agentId = split[1];
		}else{
			EasyCalendar cal = EasyCalendar.newInstance();
			cal.add(EasyCalendar.DAY,-timeInt);
			List<EasyRow> easyRows = QueryFactory.getWriteQuery(entId).queryForList(sql, new Object[]{sessionId, cal.getDateTime("-")});
			if(easyRows==null||easyRows.size()==0) {
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，缓存中不存在上次接入信息，未查询到数据库话单，不优先接入！");
				return false;
			}
			EasyRow row = easyRows.get(0);
			skillId = row.getColumnValue("GROUP_ID");
			agentId = row.getColumnValue("AGENT_ID");
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，缓存中不存在上次接入信息，查询到数据库话单，技能组id："+skillId+"，agentId："+agentId);
		}

		SkillGroup skillGroup = channelKey.getSkillGroupById(skillId);
		if(skillGroup==null||skillGroup.getAgent(agentId)==null){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，技能组信息不存在，或者在该技能组下未找到坐席信息，技能组id："+skillId+"，agentId："+agentId);
			return false;
		}
		Agent agent = skillGroup.getAgent(agentId);
		if(!agent.isReady()||agent.isBusy()){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，目标坐席非空闲状态，不优先接入！，技能组id："+skillId+"，agentId："+agentId);
			return false;
		}
		userSession.setSkillGroup(skillGroup);
		messageModel.getUserInfo().put("skillGroupId",skillGroup.getSkillGroupId());
		messageModel.getUserInfo().put("skillGroupName",skillGroup.getSkillGroupName());
		boolean addResult = agent.addUser(userSession, 1);
		if(!addResult){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，接入目标坐席失败，不优先接入！，技能组id："+skillId+"，agentId："+agentId);
			return false;
		}
		agent.updateLastServiceTime();
		userSession.setState(2);
//		stopQueue(userSession, 0);
		EasyCalendar cal2 = EasyCalendar.newInstance();
		//保存排队记录
		EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_QUEUE"), "SERIAL_ID");
		record.put("SERIAL_ID", userSession.getChatSessionId());
		record.put("DATE_ID", cal2.getDateInt());
		record.put("ENT_ID",  entId);
		record.put("SESSION_ID", sessionId);
		record.put("GROUP_ID", skillId);
		record.put("CHANNEL_ID",channel.getChannelId());
		record.put("CHANNEL_KEY", channelKey.getChannelKeyId());
		record.put("QUEUE_TIME", cal2.getDateTime("-"));
		record.put("END_TIME", cal2.getDateTime("-"));
		record.put("QUEUE_STAY_TIME", 0);
		record.put("END_CAUSE", 0);
		QueryFactory.getWriteQuery(entId).save(record);

		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] GetAgentMessage("+sessionId+") -> 熟客优先接入流程，优先接入... 技能组id："+skillId+"，agentId："+agentId);
		userSession.setAgentId(agent.getAgentId());
		userSession.setLoginType(1);
		userSession.login();

		//更新接入记录，15.熟客优先转人工结束
		JSONObject accessObj = new JSONObject();
		accessObj.put("CHAT_SESSION_ID", userSession.getChatSessionId());
		accessObj.put("CLEAR_CAUSE", 15);//15.熟客优先转人工结束
//		accessObj.put("IS_IN_AGENT", 1);
//		accessObj.put("END_TIME", cal2.getDateTime("-"));
//		accessObj.put("QUEUE_START_TIME", cal2.getDateTime("-"));
//		accessObj.put("QUEUE_END_TIME", cal2.getDateTime("-"));
//		accessObj.put("IS_IN_QUEUE", 0);
//		accessObj.put("IN_AGENT_TIME", cal2.getDateTime("-"));
		AccessRecord.getInstance().updateAccessRecord(accessObj);
		return true;
	}

	@Override
	public String getCommandName() {
		// TODO Auto-generated method stub
		return "GetAgentMessage";
	}
}