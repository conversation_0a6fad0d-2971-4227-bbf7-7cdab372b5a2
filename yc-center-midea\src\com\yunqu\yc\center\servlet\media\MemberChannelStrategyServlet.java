package com.yunqu.yc.center.servlet.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppBaseServlet;
import com.yunqu.yc.center.util.MemberConfigUtil;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;

/**
 * 渠道策略管理Servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/memberChannelStrategy/*")
public class MemberChannelStrategyServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    /**
     * 新增渠道策略
     */
    public EasyResult actionForAdd(){
        try {
            JSONObject channelStrategy = getJSONObject("channelStrategy");
            String configId = channelStrategy.getString("CONFIG_ID");
            String[] channelKeys = channelStrategy.getString("CHANNEL_KEYS").split(",");
            String[] channelNames = channelStrategy.getString("CHANNEL_NAMES").split(",");
            Integer rankIndex = channelStrategy.getInteger("RANK_INDEX");
            
            if(StringUtils.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }
            boolean hasChannel = false;
            // 逐个校验并收集冲突渠道，同时添加不冲突的渠道配置
            StringBuilder msg = new StringBuilder();
            msg.append("添加成功！");
            for(int i = 0; i < channelKeys.length; i++) {
                String channelKey = channelKeys[i];
                String channelName = i < channelNames.length ? channelNames[i] : channelKey;
                if(StringUtils.isBlank(channelKey)) {
                    continue;
                }
                // 校验冲突
                if(MemberConfigUtil.checkChannelRankIndexConflict(channelKey, rankIndex, null)) {
                    if(msg.indexOf("身份等级冲突：") < 0){
                        msg.append("身份等级冲突：");
                    }
                    msg.append(channelName).append("，");
                    continue;
                }
                // 不冲突则创建渠道策略配置（若存在则更新）
                boolean has = this.getQuery().queryForExist(
                        "select count(1) from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ? and CHANNEL_KEY = ?",
                        new Object[]{configId, channelKey}
                );
                if(!has) {
                    MemberConfigUtil.createChannelStrategy(configId, channelKey, channelName,channelStrategy);
                    hasChannel = true;
                } else {
                    // 已存在则按当前表单参数更新
                    MemberConfigUtil.updateChannelStrategy(configId, channelKey, channelStrategy);
                }
            }
            // 新增/更新完成后，回写上级配置中的 CHANNEL_KEYS、CHANNEL_NAMES
            if(hasChannel) {
                MemberConfigUtil.reloadMemberChannelList(configId);
            }

            String resultMsg = msg.indexOf("身份等级冲突：") > 0 ? msg.substring(0, msg.length() - 1) : msg.toString();
            return EasyResult.ok(null, resultMsg);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("添加失败：" + e.getMessage());
        }
    }
    
    /**
     * 修改渠道策略
     */
    public EasyResult actionForUpdate(){
        try {
            JSONObject channelStrategy = getJSONObject("channelStrategy");
            String configId = channelStrategy.getString("CONFIG_ID");
            String channelKey = channelStrategy.getString("CHANNEL_KEY");
            Integer rankIndex = channelStrategy.getInteger("RANK_INDEX");
            
            if(StringUtils.isBlank(configId) || StringUtils.isBlank(channelKey)) {
                return EasyResult.fail("配置ID和渠道KEY不能为空");
            }
            
            // 检查渠道身份等级冲突
            if(MemberConfigUtil.checkChannelRankIndexConflict(channelKey, rankIndex, configId)) {
                return EasyResult.fail("该渠道的当前会员身份等级冲突");
            }
            
            // 更新渠道策略配置
            MemberConfigUtil.updateChannelStrategy(configId, channelKey, channelStrategy);
            return EasyResult.ok(null, "修改成功");
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("修改失败：" + e.getMessage());
        }
    }
    
    /**
     * 删除渠道策略
     */
    public EasyResult actionForDelete(){
        try {
            String configId = getJsonPara("configId");
            String channelKey = getJsonPara("channelKey");
            
            if(StringUtils.isBlank(configId) || StringUtils.isBlank(channelKey)) {
                return EasyResult.fail("配置ID和渠道KEY不能为空");
            }
            
            // 删除渠道策略配置
            int executeUpdate = this.getQuery().executeUpdate(
                    "delete from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ? and CHANNEL_KEY = ?",
                    configId, channelKey
            );

            // 删除后，更新上级配置中的 CHANNEL_KEYS、CHANNEL_NAMES
            if(executeUpdate > 0){
                MemberConfigUtil.reloadMemberChannelList(configId);
            }
            return EasyResult.ok(null, "删除成功");
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("删除失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取会员身份策略基本信息（用于新增渠道时回填）
     */
    public JSONObject actionForGetMemberStrategyInfo(){
        try {
            String configId = getJsonPara("configId");
            
            if(StringUtils.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }
            
            String sql = "select * from YCUSER.CC_MEDIA_MEMBER_CONFIG where CONFIG_ID = ?";
            JSONObject result = this.getQuery().queryForRow(sql, new Object[]{configId}, new JSONMapperImpl());
            
            if(result == null) {
                return EasyResult.fail("未找到对应的会员身份策略");
            }
            
            return EasyResult.ok(result);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("查询失败：" + e.getMessage());
        }
    }


    @Override
    protected String getResId() {
        return null;
    }
}
