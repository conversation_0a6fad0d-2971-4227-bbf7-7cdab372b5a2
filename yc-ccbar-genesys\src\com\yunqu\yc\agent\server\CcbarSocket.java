package com.yunqu.yc.agent.server;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.chat.ChatContext;
import com.yunqu.yc.agent.chat.model.MessageModel;
import com.yunqu.yc.agent.enums.MsgSenderEnum;
import com.yunqu.yc.agent.log.GenesysLogger;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.mqclient.MediaProducerBroker;
import com.yunqu.yc.agent.server.video.VideoMessage;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.utils.calendar.EasyCalendar;

import javax.websocket.*;
import javax.websocket.server.PathParam;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;
  
/**
 * 全媒体socket-实际业务未使用该类
 * <AUTHOR>
 *
 */
//@ServerEndpoint("/ccbarws/{username}")
@Deprecated
public class CcbarSocket {  
  
    private static int onlineCount = 0;  
    private static EasyCache cache = CacheManager.getMemcache();
    private static Map<String, CcbarSocket> clients = new HashMap<String, CcbarSocket>(); //new ConcurrentHashMap<String, CcbarSocket>();  
    private Session session;  
    private String username;  
    private String agentId;
      
    @OnOpen  
    public void onOpen(@PathParam("username") String username, Session session) throws IOException {  
    	MediaLogger.getLogger().info("<ccbarsocket> onOpen() 已链接");  
        this.username = username;  
        this.session = session;  
        YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
        if(principal==null){
        	JSONObject result = new JSONObject();
        	result.put("error", "session timeout！");
        	session.getAsyncRemote().sendText(result.toJSONString());
        	session.close();
        	return;
        }
        this.agentId = principal.getLoginAcct();
        addOnlineCount(); 
        clients.put(this.agentId, this);  
        MediaLogger.getLogger().info("<ccbarsocket> onOpen() -> agentId:"+agentId);  
    }  
  
    @OnClose  
    public void onClose() throws IOException {  
    	MediaLogger.getLogger().info("<ccbarsocket> onClose() 已关闭");  
    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
    	MediaLogger.getLogger().info("<ccbarsocket> onClose() -> agentId:"+principal.getLoginAcct());
        clients.remove(this.agentId);  
        subOnlineCount();  
        AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
        if(agentModel == null) return;
        
        if("on".equalsIgnoreCase(agentModel.getMultiMediaSwitch())){
        	JSONObject  cmdJson = new JSONObject();
            cmdJson.put("entId", agentModel.getEntId());
     		cmdJson.put("agentId", agentId);
     		cmdJson.put("command", "cmdClose");  //坐席异常关闭
     		try {
     			MediaProducerBroker.sendMediaCenterMessage(cmdJson.toJSONString());
     		} catch (Exception ex) {
     			 MediaLogger.getLogger().error(ex,ex);
     		}
        }
    }  
  
    @OnMessage  
    public void onMessage(String message) throws IOException {
    	
    	MediaLogger.getLogger().info( "<ccbarsocket>["+agentId+"] << "+message );
    	JSONObject jsonObject = JSONObject.parseObject(message); 
    	String event = jsonObject.getString("event");
    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
    	
    	if("heartbeat".equalsIgnoreCase(event)){
    		try {
				sendMessageToAgent("Heartbeat",principal.getLoginAcct(),message);
			} catch (Exception ex) {
				MediaLogger.getLogger().info("<ccbarsocket> onMessage() error  ,cause:"+ex.getMessage(),ex);
			}

			try {
				sendMonitorInfo();
			} catch (Exception ex) {
				MediaLogger.getLogger().info("<ccbarsocket> sendMonitorInfo() error  ,cause:" + ex.getMessage(), ex);
			}
			try {
				JSONObject cmdJson = new JSONObject();
				cmdJson.put("entId", principal.getEntId());
				cmdJson.put("agentId", agentId);
				cmdJson.put("command", "cmdHeartbeat");
				MediaProducerBroker.sendMediaCenterMessage(cmdJson.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error("mediaEvent->Send msgcontent error,cause:" + ex.getMessage(), ex);
			}
    		
    		return;
    	}
    	
    	if("MsgConfirm".equalsIgnoreCase(event)||"MsgMonitor".equalsIgnoreCase(event)) {
    		return ;
    	}
    	
    	JSONObject cmdJson = jsonObject.getJSONObject("cmdJson");
		String msgType = cmdJson.getString("msgType");
		//如果消息类型为event
		if("event".equals(msgType)){
			this.actionForEvent(cmdJson);
			return;
		}

		if("agent".equals(msgType)&&StringUtils.isBlank(cmdJson.getString("msgContent"))) {
			return;
		}
		this.sendMessageToUser(cmdJson);
    }  
    
    
    public synchronized void sendMessage(String message){
    	try {
			this.session.getBasicRemote().sendText(message);
		} catch (IOException ex) {
			GenesysLogger.getLogger().error(ex,ex);
		}  
    }
    
    private JSONObject getQueueMonitorInfo(){
    	AgentModel agentModel = AgentInfos.getAgentInfo(this.agentId);
    	String data = cache.get("MEDIA_MONITOR_DATA_"+agentModel.getEntId());
    	if(StringUtils.isBlank(data)) return new JSONObject();
    	JSONObject dataObject =  JSONObject.parseObject(data);
    	JSONObject channelInfo = dataObject.getJSONObject("channelInfo");
    	int type1 = 0;
    	int type2 = 0;
    	int type3 = 0;
    	int type5 = 0;
    	JSONArray queueInfo1 = new JSONArray();
    	JSONArray queueInfo2 = new JSONArray();
    	JSONArray queueInfo3 = new JSONArray();
    	JSONArray queueInfo5 = new JSONArray();//新增：APP渠道
    	
    	Set<String> channelIds = agentModel.getChannelIds();
    	MediaLogger.getLogger().info("agentChannelIds->"+channelIds);
    	Set<String> keys = channelInfo.keySet();
    	for(String key :keys){
    		JSONObject channelObj = channelInfo.getJSONObject(key);
    		String type = channelObj.getString("channelType");
    		int  queueCount = channelObj.getIntValue("queueCount");
    		if(queueCount <=0) continue;
    		if("1".equals(type)) {
    			if(channelIds.contains(channelObj.getString("channelId"))){
    				type1+=queueCount;
    				queueInfo1.add(channelObj);
    			}
    		}
    		if("2".equals(type)){
    			if(channelIds.contains(channelObj.getString("channelId"))){
    				type2+=queueCount;
    				queueInfo2.add(channelObj);
    			}
    		}
    		if("3".equals(type)){
    			if(channelIds.contains(channelObj.getString("channelId"))){
    				type3+=queueCount;
    				queueInfo3.add(channelObj);
    			}
    		}
    		if("5".equals(type)){
    			if(channelIds.contains(channelObj.getString("channelId"))){
    				type5+=queueCount;
    				queueInfo5.add(channelObj);
    			}
    		}
    	}
    	JSONObject typeQueyInfo = new JSONObject();
    	typeQueyInfo.put("1", type1);
    	typeQueyInfo.put("2", type2);
    	typeQueyInfo.put("3", type3);
    	typeQueyInfo.put("5", type5);
    	JSONObject channelQueueInfo = new JSONObject();
    	channelQueueInfo.put("1", queueInfo1);
    	channelQueueInfo.put("2", queueInfo2);
    	channelQueueInfo.put("3", queueInfo3);
    	channelQueueInfo.put("5", queueInfo5);
    	JSONObject monitorInfo = new JSONObject();
    	monitorInfo.put("channelQueueInfo", channelQueueInfo);
    	monitorInfo.put("typeQueyInfo", typeQueyInfo);
    	
    	MediaLogger.getLogger().info("getQueueMonitorInfo->"+monitorInfo);
    	return monitorInfo;
    	
    }
    
    public void sendMonitorInfo(){
    	
    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
    	JSONObject result = new JSONObject();
		result.put("entId", principal.getEntId());
		result.put("agentId", agentId);
		result.put("messageId", "monitorInfo");
		result.put("type", "mediaQueueInfo");
		String data = cache.get("MEDIA_QUEUE_INFO_"+principal.getEntId());
		if(StringUtils.isBlank(data)) data = "{}";
		//增加技能组的排队信息返回
		JSONObject cmdData = JSONObject.parseObject(data);
		String monitorData = cache.get("MEDIA_MONITOR_DATA_"+principal.getEntId());
		if(StringUtils.isNotBlank(data)) {
			JSONObject dataObject = JSONObject.parseObject(monitorData);
			JSONObject groupInfo = dataObject.getJSONObject("groupInfo");
			cmdData.put("groupQueueInfo", groupInfo);
		}
		result.put("cmddata", cmdData);
		try {
			CcbarSocket.sendMessageToAgent("Monitor",principal.getLoginAcct(), result.toJSONString());
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex,ex);
		}
    	
		result.put("type", "mediaRankInfo");
		data = cache.get("MEDIA_SERVICE_RANK_"+principal.getEntId());
		if(StringUtils.isBlank(data)) data = "{}";
		JSONObject serviceInfo = JSONObject.parseObject(data);
		EasyCalendar cal = EasyCalendar.newInstance();
		String totalServiceCount  = MediaCacheUtil.get("MEDIA_SERVICE_COUNT_"+cal.getDateInt()+"_"+this.agentId);
		
		if(StringUtils.isBlank(totalServiceCount)){
			totalServiceCount  = "0";
		}
		serviceInfo.put("totalServiceCount", totalServiceCount);
		result.put("cmddata", serviceInfo.toJSONString());
		
		try {
			CcbarSocket.sendMessageToAgent("Monitor",principal.getLoginAcct(), result.toJSONString());
		} catch (Exception ex) {
			MediaLogger.getLogger().error(ex,ex);
		}
    	
    }
    
    
  
    @OnError  
    public void onError(Session session, Throwable error) {  
    	MediaLogger.getLogger().error("<ccbarsocket> ["+agentId+"] ccbarsocket.onError("+username+") "+error.getMessage(),error);
    }  
  
    /**
     * 发送信息给用户
     * @param agentId
     * @param message
     * @throws Exception
     */
    public static  void sendMessageToAgent(String msgType,String agentId,String message) throws Exception {  
    	MediaLogger.getLogger().info( "<ccbarsocket>["+agentId+"]["+msgType+"] >> "+message );
    	CcbarSocket item = clients.get(agentId);
    	if(item == null) {
    		throw new Exception("Agent["+agentId+"] ccbarsocket object not found!");
    	}
    	if(!item.session.isOpen()){
    		MediaLogger.getLogger().info( "<ccbarsocket>["+agentId+"]["+msgType+"] >> send message fail,cause:session is close!");
    		throw new Exception("ccbarsocket is close!");
    	}
    	synchronized (item.session) {
    	//	item.session.getAsyncRemote().sendText(message);  
    		item.session.getBasicRemote().sendText(message);  
		}
    }  
      
    public static void sendMessageAll(String message) throws IOException {  
        for (CcbarSocket item : clients.values()) {  
            item.session.getAsyncRemote().sendText(message);  
        }  
    }  
    
      
  
    public static synchronized int getOnlineCount() {  
        return onlineCount;  
    }  
  
    public static synchronized void addOnlineCount() {  
        CcbarSocket.onlineCount++;  
    }  
  
    public static synchronized void subOnlineCount() {  
        CcbarSocket.onlineCount--;  
    }  
  
    public static synchronized Map<String, CcbarSocket> getClients() {  
        return clients;  
    }  
    
    
    public void actionForEvent(JSONObject cmdJson){
		
		String sessionId = cmdJson.getString("sessionId");
		
		//请求事件
		String event = cmdJson.getString("event");  
		JSONObject eventData = cmdJson.getJSONObject("eventData");
		
		//发起转移
		if("StartTransfer".equals(event)){
			eventData.put("srcAgentId", this.agentId);
			cmdJson.put("eventData", eventData);
			cmdJson.put("event", "ReceiveTransfer");
			String targetAgentId = eventData.getString("targetAgentId");
			try {
				MediaProducerBroker.sendAgentMessage(targetAgentId,cmdJson.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error("mediaEvent->Send msgcontent error,cause:"+ex.getMessage(),ex);
			}
		}
		
		//转移确认
		if("ConfirmTransfer".equals(event)){
			cmdJson.put("event", "ResultTransfer");
			String srcAgentId = eventData.getString("srcAgentId");
			String targetAgentId = eventData.getString("targetAgentId");
			try {
				//转移确认，把确认结构返回给原来坐席。
				MediaProducerBroker.sendAgentMessage(srcAgentId,cmdJson.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error("mediaEvent->Send msgcontent error,cause:"+ex.getMessage(),ex);
			}
			
			String result = eventData.getString("result");
			//转移成功,发送给mediaCenter 做转移处理,由UserSession对象完成转移处理。
			if("succ".equalsIgnoreCase(result)){
				try {
					AgentModel targetAgentModel = AgentInfos.getAgentInfo(this.agentId);
					JSONObject  jsonObject = new JSONObject();
					jsonObject.put("entId", targetAgentModel.getEntId());
					//原agentId
					jsonObject.put("agentId", srcAgentId);
					//目标坐席ID
					jsonObject.put("targetAgentId", targetAgentId);
					jsonObject.put("command", "cmdTransfer");
					jsonObject.put("sessionId", sessionId);
					MediaProducerBroker.sendMediaCenterMessage(jsonObject.toJSONString());
				} catch (Exception ex) {
					MediaLogger.getLogger().error(ex,ex);
				}
			}
		}
		
		/**
		 * 坐席关闭会话
		 */
		if("Released".equals(event)){
			AgentModel agentModel = AgentInfos.getAgentInfo(agentId);
			JSONObject  jsonObject = new JSONObject();
			jsonObject.put("entId", agentModel.getEntId());
			jsonObject.put("agentId", agentId);
			jsonObject.put("command", "cmdReleased");
			jsonObject.put("sessionId", cmdJson.getString("sessionId"));
			try {
				MediaProducerBroker.sendMediaCenterMessage(jsonObject.toJSONString());
			} catch (Exception ex) {
				MediaLogger.getLogger().error(ex, ex);
			}
		}
		
		//视频通话相关事件
		if(StringUtils.isNotBlank(event)&&(event.toLowerCase().indexOf("video")>-1||event.toLowerCase().indexOf("inviteagent")>-1)) {
			AgentModel agentModel = AgentInfos.getAgentInfo(this.agentId);
			cmdJson.put("entId", agentModel.getEntId());
			cmdJson.put("agentId", this.agentId);
			new VideoMessage().sendMessage(cmdJson);
			return;
		}
		
		/**
		 *视频通话时，文件共享
		 */
		if("shareFile".equals(event)) {
			this.sendMessageToUser(cmdJson);
		}
		
    }
    
    /**
     * 发送用户
     * @param agentModel
     * @param cmdJson
     */
    private void sendMessageToUser(JSONObject cmdJson){

    	YCUserPrincipal  principal  = (YCUserPrincipal)session.getUserPrincipal();
		String sessionId = cmdJson.getString("sessionId");
		String msgContent = cmdJson.getString("msgContent");  //这里必须修改
		
		String msgType = cmdJson.getString("msgType");
		
		JSONObject visitorObject = JSONObject.parseObject(MediaCacheUtil.get(Constants.USER_VISITOR+sessionId));
		MediaLogger.getLogger().info("sendMessageToUser.visitor->"+visitorObject);
		if(visitorObject == null) visitorObject = new JSONObject();
		String chatSessionId = visitorObject.getString("chatSessionId");
		MsgSenderEnum sendEnum = MsgSenderEnum.getEnumByKey(cmdJson.getString("sender"),MsgSenderEnum.坐席);
		MessageModel messageModel = new MessageModel();
		messageModel.setInitialEvent(cmdJson.getString("event"));
		messageModel.setAgentId(principal.getLoginAcct());
		messageModel.setEntId(principal.getEntId());
		messageModel.setSessionId(sessionId);
		messageModel.setChatSessionId(chatSessionId);
		messageModel.setMsgType(msgType);
		messageModel.setSender(sendEnum.getKey());
		messageModel.setSenderCode(sendEnum.getCode());
		messageModel.setMsgContent(msgContent);
		
		try {
			MediaProducerBroker.sendUserMessage(sessionId,messageModel.toString());
			//更新当前用户和坐席的信息通知
			JSONObject  notifyJson = new JSONObject();
			notifyJson.put("entId", principal.getEntId());
			notifyJson.put("agentId", principal.getLoginAcct());
			notifyJson.put("sessionId", sessionId);
			notifyJson.put("command", "cmdMsgNotify");
			notifyJson.put("sender", sendEnum.getKey());
			MediaProducerBroker.sendMediaCenterMessage(notifyJson.toJSONString());
			//保存会话记录
			ChatContext.getMessage(principal.getEntId()).save(messageModel);
		} catch (Exception ex) {
			MediaLogger.getLogger().error("mediaEvent->Send msgcontent error,cause:"+ex.getMessage(),ex);
		}
		
	}
    
    
    

}  