package com.yunqu.yc.media.center.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;

import java.sql.SQLException;



/**
 * 全媒体接入记录，选择正确按键之后记录本次会话
*结束原因：1.机器人正常结束；2.机器人超时结束;；3.排队超时结束；4.排队主动结束；5.成功转人工结束；6.非工作时间进留言；7.排队超时进留言；
 * <AUTHOR>
 *
 */

public class AccessRecord {
	
	private static class Holder{
		private static AccessRecord me = new AccessRecord();
	}
	
	
	public static AccessRecord getInstance() {
		return Holder.me;
	}
	
	public void saveAccessRecord(JSONObject data) {
		try {
			String entId = data.getString("ENT_ID");
			EntContext context = EntContext.getContext(entId);
			EasyRecord msgRecord = new EasyRecord(context.getTableName("CC_MEDIA_ACCESS_RECORD"),"CHAT_SESSION_ID");
			msgRecord.set("DATE_ID", EasyCalendar.newInstance().getDateInt());
			msgRecord.set("START_TIME", EasyCalendar.newInstance().getDateTime("-"));
			msgRecord.set("IS_IN_ROBOT", 0);
			msgRecord.set("IS_IN_QUEUE", 0);
			msgRecord.set("IS_IN_AGENT", 0);
			msgRecord.set("IS_IN_WORD", 0);
			if(!data.isEmpty()) {
				msgRecord.setColumns(data);
			}
			QueryFactory.getWriteQuery(entId).save(msgRecord);
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);

		}
	}
	
	/**
	 * 更新接入记录
	 * //结束原因：1.机器人正常结束；2.机器人超时结束;；3.排队超时结束；4.排队主动结束；5.成功转人工结束；6.非工作时间进留言；7.排队超时进留言；15.熟客优先转人工结束
	 * @param data
	 */
	public void updateAccessRecord(JSONObject data) {
		if(data==null||data.isEmpty()) {
			return;
		}
		String entId = data.getString("ENT_ID");
		EntContext context = EntContext.getContext(entId);
		EasyRecord msgRecord = new EasyRecord(context.getTableName("CC_MEDIA_ACCESS_RECORD"),"CHAT_SESSION_ID");
		msgRecord.setColumns(data);
		try {
			QueryFactory.getWriteQuery(entId).update(msgRecord);
		} catch (SQLException e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	
	
}
