package com.yunqu.yc.center.servlet.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppBaseServlet;
import com.yunqu.yc.center.util.MemberConfigUtil;
import com.yunqu.yc.center.utils.CacheUtil;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.util.List;

/**
 * 会员身份策略管理Servlet
 * <AUTHOR>
 */
@WebServlet("/servlet/memberStrategy/*")
public class MemberStrategyServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    /**
     * 新增会员身份策略
     */
    public EasyResult actionForAdd(){
        try {
            JSONObject memberStrategy = getJSONObject("memberStrategy");
            String entId = getJSONObject().getString("entId");
            // 校验会员身份唯一性
            if(MemberConfigUtil.checkMemberIdExists(memberStrategy.getString("MEMBER_ID"), null)) {
                return EasyResult.fail("该会员身份已存在策略！");
            }
            Integer rankIndex = memberStrategy.getInteger("RANK_INDEX");

            // 校验身份等级唯一性
            if(MemberConfigUtil.checkRankIndexExists(rankIndex, null)) {
                return EasyResult.fail("该身份等级已存在！");
            }

            String configId = RandomKit.randomStr();

            // 保存会员身份策略
            EasyRecord record = new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CONFIG", "CONFIG_ID");
            record.setColumns(memberStrategy);
            record.setPrimaryValues(configId);
            record.set("CREATE_TIME", EasyDate.getCurrentDateString());
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());

            this.getQuery().save(record);

            boolean hasChannel = false;

            // 如果选择了渠道，则生成关联渠道策略配置
            String channelKeysStr = memberStrategy.getString("CHANNEL_KEYS");
            StringBuilder checkchannelSbf = new StringBuilder();
            checkchannelSbf.append("添加成功！");
            if(StringUtils.isNotBlank(channelKeysStr)) {
                String[] channelKeys = channelKeysStr.split(",");
                String[] channelNames = memberStrategy.getString("CHANNEL_NAMES").split(",");

                for(int i = 0; i < channelKeys.length; i++) {
                    String channelKey = channelKeys[i];
                    String channelName = i < channelNames.length ? channelNames[i] : channelKey;
                    if(StringUtils.isBlank(channelKey)) {
                        continue;
                    }
                    // 检查渠道身份等级冲突
                    if(MemberConfigUtil.checkChannelRankIndexConflict(channelKey, rankIndex, null)) {
                        if(checkchannelSbf.indexOf("身份等级冲突：") < 0){
                            checkchannelSbf.append("身份等级冲突：");
                        }
                        checkchannelSbf.append(channelName).append("，");
                        continue;
                    }
                    // 创建渠道策略配置
                    MemberConfigUtil.createChannelStrategy(configId, channelKey, channelName, memberStrategy);
                    hasChannel=true;
                }
                if(hasChannel){
                    MemberConfigUtil.reloadMemberChannelList(configId);
                }

            }
            //通知全媒体路由中心yc-mediacenter执行重新加载身份策略列表
            CacheManager.getMemcache().put("RELOAD_MEMBER_CONFIG_" + entId,"1",60);

            String checkResult = checkchannelSbf.indexOf("身份等级冲突：") > 0 ? checkchannelSbf.substring(0, checkchannelSbf.length() - 1) : checkchannelSbf.toString();
            return EasyResult.ok(null, checkResult);
        } catch (Exception e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("添加失败：" + e.getMessage());
        }
    }

    /**
     * 修改会员身份策略
     */
    public EasyResult actionForUpdate(){
        try {
            JSONObject memberStrategy = getJSONObject("memberStrategy");
            String configId = memberStrategy.getString("CONFIG_ID");
            Integer rankIndex = memberStrategy.getInteger("RANK_INDEX");
            Integer queueLevel = memberStrategy.getInteger("QUEUE_LEVEL");
            String entId = getJSONObject().getString("entId");

            if(StringUtils.isBlank(configId)) {
                return EasyResult.fail("策略id不能为空！");
            }

            if(rankIndex==null||queueLevel==null) {
                return EasyResult.fail("必填项不能为空！");
            }

            // 校验身份等级唯一性
            if(MemberConfigUtil.checkRankIndexExists(rankIndex, configId)) {
                return EasyResult.fail("该身份等级已存在！");
            }

            // 获取原有渠道配置
            List<JSONObject> oldChannels = this.getQuery().queryForList(
                "select CHANNEL_KEY from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ?",
                new Object[]{configId}, new JSONMapperImpl()
            );

            // 更新会员身份策略
            EasyRecord record = new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CONFIG", "CONFIG_ID");
            record.setColumns(memberStrategy);
            record.set("UPDATE_TIME", EasyDate.getCurrentDateString());

            this.getQuery().update(record);
            StringBuilder checkchannelSbf = new StringBuilder();
            checkchannelSbf.append("修改成功！");

            // 处理渠道策略配置
            String channelKeysStr = memberStrategy.getString("CHANNEL_KEYS");

            boolean isChange = false;
            // 如果选择了"覆盖所有渠道"，删除原有关联渠道策略配置
            String overrideAllChannels = memberStrategy.getString("OVERRIDE_ALL_CHANNELS");
            if("1".equals(overrideAllChannels)) {
                isChange=true;
                this.getQuery().executeUpdate("delete from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ?", configId);
            }

            //清空了所有渠道，删除原有关联渠道策略配置
            if(StringUtils.isBlank(channelKeysStr)) {
                isChange=true;
                this.getQuery().executeUpdate("delete from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ?", configId);
            }else{
                //删除移除的渠道
                for (JSONObject oldChannel : oldChannels) {
                    String channelKey = oldChannel.getString("CHANNEL_KEY");
                    if(!channelKeysStr.contains(channelKey)) {
                        this.getQuery().executeUpdate("delete from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ? and CHANNEL_KEY = ?", configId, channelKey);
                    }
                }
                // 如果选择了渠道，则修改 或 添加新增的渠道配置
                String[] newChannelKeys = channelKeysStr.split(",");
                String[] channelNames = memberStrategy.getString("CHANNEL_NAMES").split(",");

                for(int i = 0; i < newChannelKeys.length; i++) {
                    String newChannelKey = newChannelKeys[i];
                    String newChannelName = i < channelNames.length ? channelNames[i] : newChannelKey;

                    if(StringUtils.isNotBlank(newChannelKey)) {
                        // 检查渠道身份等级冲突
                        if(MemberConfigUtil.checkChannelRankIndexConflict(newChannelKey, rankIndex, configId)) {
                            if(checkchannelSbf.indexOf("身份等级冲突：") < 0){
                                checkchannelSbf.append("身份等级冲突：");
                            }
                            checkchannelSbf.append(newChannelName).append("，");
                            continue;
                        }

                        // 检查是否已存在
                        boolean has = this.getQuery().queryForExist(
                                "select count(1) from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ? and CHANNEL_KEY = ?",
                                new Object[]{configId, newChannelKey}
                        );
                        if(!has) {
                            MemberConfigUtil.createChannelStrategy(configId, newChannelKey, newChannelName, memberStrategy);
                            isChange=true;
                        }else{
                            if("1".equals(overrideAllChannels)){
                                MemberConfigUtil.updateChannelStrategy(configId, newChannelKey, memberStrategy);
                            }
                        }
                    }
                }
            }

            //更新身份策略中的CHANNEL_KEY，CHANNEL_NAMES
            if(isChange){
                MemberConfigUtil.reloadMemberChannelList(configId);
            }
            //通知全媒体路由中心yc-mediacenter执行重新加载身份策略列表
            CacheManager.getMemcache().put("RELOAD_MEMBER_CONFIG_" + entId,"1",60);

            String checkResult = checkchannelSbf.indexOf("身份等级冲突：") > 0 ? checkchannelSbf.substring(0, checkchannelSbf.length() - 1) : checkchannelSbf.toString();
            return EasyResult.ok(null, checkResult);
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("修改失败：" + e.getMessage());
        }
    }

    /**
     * 删除会员身份策略
     */
    public EasyResult actionForDelete(){
        try {
            String configId = getJsonPara("configId");

            if(StringUtils.isBlank(configId)) {
                return EasyResult.fail("配置ID不能为空");
            }

            // 删除关联的渠道策略配置
            this.getQuery().executeUpdate("delete from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CONFIG_ID = ?", configId);

            // 删除会员身份策略
            this.getQuery().deleteById(new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CONFIG", "CONFIG_ID").setPrimaryValues(configId));
            return EasyResult.ok(null, "删除成功");
        } catch (SQLException e) {
            this.error(e.getMessage(), e);
            return EasyResult.fail("删除失败：" + e.getMessage());
        }
    }

    @Override
    protected String getResId() {
        return null;
    }
}
