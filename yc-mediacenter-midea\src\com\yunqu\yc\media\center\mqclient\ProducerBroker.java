package com.yunqu.yc.media.center.mqclient;

import com.yunqu.yc.media.center.base.Constants;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.util.MediaCacheUtil;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.cache.EasyCache;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

import javax.jms.JMSException;
import java.util.HashMap;
import java.util.Map;

/**
 * 
 * <AUTHOR> 每个CCBAR
 *         根据所在的mars独立创建属于该mars的MQ，Agent是通过nignx进行登录，agent会在其中的一台mars上进行登录。
 *         因此每台mars的ccbar只需要处理当前坐席的请求信息。
 *
 */
public class ProducerBroker {

	private static Map<String, Broker> agentBrokers = new HashMap<String, Broker>();
	private static Map<String, Broker> userBrokers = new HashMap<String, Broker>();
	private static EasyCache cache = CacheManager.getMemcache();

	/**
	 * 获得坐席所在的Broker
	 * @param agentId
	 */
	public static void sendAgentMessage(String agentId,String msg){
		if(StringUtils.isBlank(agentId)){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage error,cause: agentId is null",null);
			return ;
		}
			//获得当前坐席所在的mars上的MQ
		String brokerName = MediaCacheUtil.get(Constants.BROKER_AGENT_NAME+agentId);
		
		if(StringUtils.isBlank(brokerName)){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage error,cause: agentId["+agentId+"] broker is null",null);
			return ;
		}
		
		Broker broker = agentBrokers.get(brokerName);
		String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
		if(broker==null){
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
			agentBrokers.put(brokerName, broker);
		}
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage("+agentId+","+brokerName+")->"+msg);
		try {
			broker.sendMessage(msg);
		} catch(JMSException e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage("+agentId+","+brokerName+") error,the broker is close,need again new broker,cause:"+e.getMessage(),e);
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
			agentBrokers.put(brokerName, broker);
			try {
				broker.sendMessage(msg);
			} catch (JMSException e1) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage("+agentId+","+brokerName+") again send is error,cause:"+e1.getMessage(),e1);
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendAgentMessage("+agentId+","+brokerName+") error,cause:"+ex.getMessage(),ex);
		}
	}

	/**
	 * 获得用户所对应的MARS。
	 * @param sessionId
	 * @param msg
	 * @return
	 */
	public static void sendUserMessage(String sessionId,String msg){
		
		if(StringUtils.isBlank(sessionId)){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendUserMessage error,cause: sessionId is null",null);
			return ;
		}

		//获得当用户所在的mars上的MQ队列名称
		String brokerName =  MediaCacheUtil.get(Constants.BROKER_USER_NAME+sessionId);
		
		if(StringUtils.isBlank(brokerName)){
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> send user msg error,cause: can't found sessionId["+sessionId+"] broker ",null);
			return ;
		}
		
		Broker broker = userBrokers.get(brokerName);
		String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
		if(broker==null){
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
			userBrokers.put(brokerName, broker);
		}
		
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"]  >> sendUserMessage("+sessionId+","+brokerName+")->"+msg);
		
		try {
			broker.sendMessage(msg);
		} catch(JMSException e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendUserMessage("+sessionId+","+brokerName+") error,the broker is close,need again new broker,cause:"+e.getMessage(),e);
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
			userBrokers.put(brokerName, broker);
			try {
				broker.sendMessage(msg);
			} catch (JMSException e1) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendUserMessage("+sessionId+","+brokerName+") again send error,cause:"+e1.getMessage(),e1);
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"]  >> sendUserMessage("+sessionId+","+brokerName+") error,cause:"+ex.getMessage(),ex);
		}
		
	}
	
}
