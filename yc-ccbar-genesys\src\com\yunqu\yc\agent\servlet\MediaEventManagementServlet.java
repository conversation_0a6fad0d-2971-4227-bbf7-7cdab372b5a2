package com.yunqu.yc.agent.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.genesys.util.RespResultEnum;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.mqclient.MediaProducerBroker;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.MediaAgentStateEnum;
import com.yunqu.yc.agent.msg.MediaRespCmdEnum;
import com.yunqu.yc.agent.msg.impl.v1.EventHandlerV1;
import com.yunqu.yc.agent.server.WebSocket;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.annotation.WebServlet;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.Date;
@Deprecated
@WebServlet({"/servlet/MediaEventManagement"})
public class MediaEventManagementServlet
  extends EventBaseServlet
{
  private static final long serialVersionUID = 2151657556079304871L;
  private static final String RESULT_SUCC = "succ";
  private static final String RESULT_FAIL = "fail";
  EventHandlerV1 eventMessage = (EventHandlerV1)EventFactory.getEventMessage("v1");
  
  
  
  
  
  public void updateCount(String count, String entId, String agentId,String updateAgentAcc)
  {
    JSONObject notifyJson = new JSONObject();
    notifyJson.put("entId", entId);
    notifyJson.put("agentId", agentId);
    notifyJson.put("count", count);
    notifyJson.put("command", "cmdLimit");
    MediaProducerBroker.sendMediaCenterMessage(notifyJson.toJSONString());
    
    try
    {
      SimpleDateFormat sdf = new SimpleDateFormat();
      sdf.applyPattern("yyyy-MM-dd HH:mm:ss");
      Date date = new Date();
      EasyRecord record = new EasyRecord(getTableName("CC_MEDIA_AGENT_STATE_RECORD"), new String[] { "RECORD_ID" });
      record.setPrimaryValues(new Object[] { RandomKit.randomStr() });
      record.set("AGENT_ACCT", agentId);
      record.set("AGENT_PHONE", agentId);
      record.set("STATE", count);
      record.set("RECORD_TIME", sdf.format(date));
      record.set("UPDATE_AGENT_ACC", updateAgentAcc);
      record.set("ENT_ID", entId);
      getQuery().save(record);
    }
    catch (SQLException e)
    {
      e.printStackTrace();
      MediaLogger.getLogger().error("服务上线日志入库失败->actionForLimit:" + e.getMessage());
    }
  }
  
  public EasyResult actionForEvent()
    throws Exception
  {
	  setResHeader();
    if (getYCUserPrincipal() == null) {
      return getSessionTimeoutResult();
    }
    EasyResult result = EasyResult.ok();
    
    String cmdJson = getRequest().getParameter("cmdJson");
    MediaLogger.getLogger().info("<MediaEvent>[" + getAgentId() + "][" + getRequest().getRemoteAddr() + "] << " + cmdJson);
    
    JSONObject cmdObject = JSONObject.parseObject(cmdJson);
    if (cmdObject == null)
    {
      result.setSuccess("fail", "处理失败，原因：cmdJson数据请求格式错误！");
      return result;
    }
    String command = cmdObject.getString("messageId");
    String resp = "";
    try
    {
      if ("cmdNotReady".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdNotReady"));
        resp = getCommandResult(getAgentId(), "cmdNotReady");
        if ("000".equals(resp))
        {
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaAgentStateEnum.BUSY.getReq(getEntId(), getAgentId()).toJSONString());
        	WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.BUSY.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          if (StringUtils.isBlank(resp)) {
            return EasyResult.error(500, "置忙失败,消息响应超时");
          }
          return EasyResult.error(500, "置忙失败[" + result + "]");
        }
      }
      if ("cmdReady".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdReady"));
        resp = getCommandResult(getAgentId(), "cmdReady");
        if ("000".equals(resp))
        {
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaAgentStateEnum.IDLE.getReq(getEntId(), getAgentId()).toJSONString());
        	WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.IDLE.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          if (StringUtils.isBlank(resp)) {
            return EasyResult.error(500, "置闲失败,消息响应超时");
          }
          return EasyResult.error(500, "置闲失败[" + result + "]");
        }
        return result;
      }
      if ("cmdLogout".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdLogout"));
        resp = getCommandResult(getAgentId(), "cmdLogout");
        if ("001".equals(resp)) {
          return EasyResult.error(500, "当前还有待处理用户，请处理完成再签出！");
        }
        if ("000".equals(resp))
        {
          MediaCacheUtil.delete(Constants.BROKER_AGENT_NAME + getAgentId());
          
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaRespCmdEnum.Logout.getReq(getEntId(), getAgentId(), RespResultEnum.SUCCESS).toJSONString());
//          CcbarSocket.sendMessageToAgent("State", getAgentId(), MediaAgentStateEnum.LOGOFF.getReq(getEntId(), getAgentId()).toJSONString());
          WebSocket.sendMessageToAgent(getAgentId(), MediaRespCmdEnum.Logout.getReq(getEntId(), getAgentId(), RespResultEnum.SUCCESS).toJSONString());
          WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.LOGOFF.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          return EasyResult.error(500, "签出失败！");
        }
        return result;
      }
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error("run actionForEvent() error,cause:" + ex.getMessage(), ex);
      

      MediaLogger.getLogger().info("<Resp>[" + getAgentId() + "][" + command + ":" + resp + "] >>" + result);
    }
    return result;
  }
  
  
  private String getAgentStateInfo(String entId, String agentId, String command)
  {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("entId", entId);
    jsonObject.put("agentId", agentId);
    jsonObject.put("command", command);
    
    return jsonObject.toJSONString();
  }
  
  
  
  
  
  
  protected String getResId()
  {
    return null;
  }
}
