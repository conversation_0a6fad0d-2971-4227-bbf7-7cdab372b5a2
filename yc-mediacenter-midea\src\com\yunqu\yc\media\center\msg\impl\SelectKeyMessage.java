package com.yunqu.yc.media.center.msg.impl;


import org.apache.commons.lang3.StringUtils;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.AccessRecord;
import com.yunqu.yc.media.center.model.Channel;
import com.yunqu.yc.media.center.model.ChannelKey;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.model.SkillGroup;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.Message;

public class SelectKeyMessage extends Message{

	@Override
	public void onMessage(MessageModel messageModel) {
		
		Channel channel = EntContext.getContext(messageModel.getEntId()).getChannel(messageModel.getChannelId());
		ChannelKey channelKey  = channel.getChannelKeyByCode(messageModel.getMsgContent());
		String sessionId = messageModel.getSessionId();
		
		// 如果输入的渠道信息错误，返回导航语
		if (channelKey == null) {
			messageModel.setMsgType("text");
			messageModel.setEvent("selectKey");
			messageModel.setMsgContent(channel.getAutoConfig().getWelcomeMsg());
			messageModel.setSender("system");
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			return;
		}
		
		//如果按键类型为机器人，则直接转机器人处理。
		//按键类型工作时间优先人工 在mediagw已判断工作时间转人工 ，此处直接转机器人
		if(StringUtils.equalsAny(channelKey.getKeyType() , "2" , "4")){
			//2.0#20201014-1 添加接入记录
			try {
				JSONObject data = new JSONObject();
				data.put("CHAT_SESSION_ID", messageModel.getChatSessionId());
				data.put("IS_IN_ROBOT", 1);
				data.put("ROBOT_START_TIME", EasyCalendar.newInstance().getDateTime("-"));
				SkillGroup skillGroup = channelKey.getSkillGroup();
				data.put("SKILL_GROUP_ID", skillGroup.getSkillGroupId());
				AccessRecord.getInstance().updateAccessRecord(data);
			} catch (Exception e) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
			}
			messageModel.setMsgType("text");
			messageModel.setEvent("automatic");
			messageModel.setMsgContent(channel.getAutoConfig().getWelcomeMsg());
			messageModel.setSender("system");
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			return;
		}
		
		//如果是人工的，转人工处理。
		GetAgentMessage getAgentMessage = new GetAgentMessage();
		getAgentMessage.onMessage(messageModel);
		
	}

	@Override
	public String getCommandName() {
		// TODO Auto-generated method stub
		return "SelectKeyMessage";
	}
}
