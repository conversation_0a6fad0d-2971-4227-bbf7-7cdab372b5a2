package com.yunqu.yc.media.center.model;

import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.util.CommonUtil;

/**
 * 视频会话对象
 * <AUTHOR>
 *
 */
public class VideoSession {
	/*聊天记录id*/
	private String chatId;
	
	private String entId;
	
	private String agentId;
	
	private String sessionId;
	
	private String chatSessionId;

	/*CALL_TYPE 发起者，  1 客户端   2 坐席端*/
	private int callType = 2;
	
	/*挂机原因， 11 用户挂断，12 用户拒接，13 用户未接，14用户已取消，21 坐席关闭会议，22 坐席拒接，23 坐席未接，24坐席已取消，31 系统异常*/
	private int clearCause;
	
	/*视频会话状态：inviteVideo 邀请中，videoSuccess 视频连接成功，closeVideo 视频关闭*/
	private String videoState = "inviteVideo";
	
	/*视频邀请时间，videoState=inviteVideo开始计算*/
	private long inviteTime = 0;
	
	/*视频会话开始时间，videoState=videoSuccess开始计算*/
	private long startTime = 0;
	
	/*视频会话结束时间，videoState=closeVideo开始计算*/
	private long endTime = 0;
	
	/*三方坐席id*/
	private String thirdAgentId;
	
	/*邀请三方时间*/
	private long thirdReqTime = 0;
	
	/*三方视频会话开始时间*/
	private long thirdStartTime = 0;
	
	/*三方视频会话结束时间*/
	private long thirdEndTime = 0;
	
	/*三方视频会话标志，0不存在，1存在*/
	private int thirdFlag = 0;
	
	/* 三方挂机原因，1会议结束，2三方拒接，3三方未接，4三方退出会议，5主持人踢出会议*/
	private int thirdClearCause = 1;
	
	private String videoRecordId;
	
	private String confId;
	
	//yq-webrtc 云趣自研，tx-trtc 腾讯云TRTC
	private String videoApiType;
	/**
	 * 是否正在邀请中
	 * @return
	 */
	public boolean isInvite() {
		return "inviteVideo".equals(this.videoState);
	}
	
	/**
	 * 是否在通话中
	 * @return
	 */
	public boolean isVideoChat() {
		return "videoSuccess".equals(this.videoState);
	}
	
	public boolean istxtrtc() {
		return "tx-trtc".equals(videoApiType);
	}
	
	/**
	 * 视频邀请
	 * @param entId
	 * @param agentId
	 * @param sessionId
	 * @param chatSessionId
	 * @param chatId 聊天记录id
	 * @param callType
	 */
	public void inviteVideo(String entId, String agentId, String sessionId, String chatSessionId, String chatId, int callType,String videoApiType) {
		this.videoState = "inviteVideo";
		this.entId = entId;
		this.agentId = agentId;
		this.sessionId = sessionId;
		this.chatSessionId = chatSessionId;
		this.callType = callType;
		this.inviteTime = System.currentTimeMillis();
		this.chatId = chatId;
		this.videoApiType = videoApiType;
		this.saveRecord();
		this.setVideoFlag();
	}
	
	/**
	 * 取消邀请
	 */
	public JSONObject cancelInviteVideo(int clearCause) {

		//如果是已接通视频，用户端发送取消邀请，则设置为用户端挂断 11，并更新 END_TIME，TOTAL_TIME
		if(clearCause==14 && this.startTime>0 && "videoSuccess".equals(this.videoState)){
			clearCause = 11;
			this.endTime = System.currentTimeMillis();
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] VideoSession.cancelInviteVideo("+clearCause+","+sessionId+") -> 用户端发送取消邀请，已接通视频，则设置为用户端挂断（CLEAR_CAUSE=11），并更新 END_TIME，TOTAL_TIME");
		}

		this.videoState = "closeVideo";
		this.clearCause = clearCause;
		updateRecord();
		return this.updateChatMsg();
	}
	/**
	 * 视频接通成功开始视频会话
	 */
	public void start() {
		this.videoState = "videoSuccess";
		this.startTime = System.currentTimeMillis();
		updateRecord();
	}
	
	/**
	 * 视频会议创建成功，更新视频通话记录中的confId,recordId
	 */
	public void startMeeting(String videoRecordId,String confId) {
		this.videoRecordId = videoRecordId;
		this.confId = confId;
		this.startTime = System.currentTimeMillis();
		updateRecord();
	}
	/**
	 * 关闭视频会话
	 */
	public JSONObject close(int clearCause) {
		if("closeVideo".equals(this.videoState)||"cancelInviteVideo".equals(this.videoState)) {
			return new JSONObject();
		}
		this.videoState = "closeVideo";
		this.clearCause = clearCause;
		this.endTime = System.currentTimeMillis();
		this.closeThirdAgent();
		updateRecord();
		return this.updateChatMsg();
	}
	
	/**
	 * 邀请三方
	 * @param targetAgentId
	 */
	public void inviteThirdAgent(String targetAgentId) {
		this.thirdAgentId = targetAgentId;
		this.thirdReqTime = System.currentTimeMillis();
	}
	/**
	 * 取消邀请三方
	 * @param targetAgentId
	 */
	public void cancelInviteThirdAgent() {
		this.thirdAgentId = null;
	}
	
	/**
	 * 三方坐席同意邀请
	 */
	public void agreeInviteThirdAgent() {
		this.thirdStartTime = System.currentTimeMillis();
		this.thirdFlag = 1;
		updateRecord();
	}
	
	/**
	 * 三方坐席拒绝邀请
	 * @param cause 三方挂机原因，1会议结束，2三方拒接，3三方未接，4三方退出会议，5主持人踢出会议
	 */
	public void disagreeInviteThirdAgent() {
		this.thirdClearCause = 2;
		this.thirdAgentId = null;
	}
	
	/**
	 * 三方坐席退出会议
	 */
	public void closeThirdAgent() {
		//退出三方坐席的视频通话
		if(StringUtils.isNotBlank(thirdAgentId)) {
			Agent agentInfo = EntContext.getContext(entId).getAgentInfo(thirdAgentId);
			agentInfo.removeVideoUser(sessionId);
			updateRecord();
		}
	}
	
	
	/**
	 * 邀请用户是否超时
	 * 邀请状态下，时间超过2分钟将清除视频会话
	 * @return
	 */
	public boolean isInviteTimeOut() {
		if(!"inviteVideo".equals(this.videoState)) {
			return false;
		}
		
		//tx-trtc 1分钟超时
		if(istxtrtc()&&System.currentTimeMillis() - this.inviteTime>=60*1000) {
			return true;
		}
		
		if(System.currentTimeMillis() - this.inviteTime>=2*60*1000) {
			return true;
		}
		
		return false;
	}

	private void saveRecord(){
		EntContext context = EntContext.getContext(this.entId);
		EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_VIDEO_RECORD"), "ID");
		EasyCalendar cal = EasyCalendar.newInstance();
		record.setPrimaryValues(this.chatId);
		record.set("CHAT_SESSION_ID", 		this.chatSessionId);
		record.set("SESSION_ID", 			this.sessionId);
		record.set("MONTH_ID", 				cal.getMonth());
		record.set("DATE_ID", 				cal.getDateInt());
		record.set("BUSI_ORDER_ID", 		context.getBusiOrderId());
		record.set("ENT_ID", 				this.entId);
		record.set("CALL_TIME", 			CommonUtil.parseDateTime(this.inviteTime));
		record.set("CALL_TIMESTAMP", 		this.inviteTime);
		record.set("CALL_TYPE", 			this.callType);
		record.set("THIRD_CALL_FLAG", 		this.thirdFlag);
		if(thirdFlag>0){
			record.set("THIRD_REQ_TIME", 		CommonUtil.parseDateTime(this.thirdReqTime));
			record.set("THIRD_BEGIN_TIME", 		CommonUtil.parseDateTime(this.thirdStartTime));
			record.set("THIRD_END_TIME", 		this.thirdStartTime>0?CommonUtil.parseDateTime(this.thirdEndTime):"");
			record.set("THIRD_TOTAL_TIME", 		this.thirdStartTime>0&&this.thirdEndTime>0?(this.thirdEndTime-this.thirdStartTime)/1000:0);
		}

		if(this.clearCause>0)record.set("CLEAR_CAUSE", 			this.clearCause);
		record.set("RECORD_ID", 			this.videoRecordId);
		record.set("CONF_ID", 				this.confId);

		try {
			QueryFactory.getWriteQuery(entId).save(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] VideoSession.save() error,cause:"+ex.getMessage(),ex);
		}
	}

	private void updateRecord(){
		EntContext context = EntContext.getContext(this.entId);
		EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_VIDEO_RECORD"), "ID");
		EasyCalendar cal = EasyCalendar.newInstance();
		record.setPrimaryValues(this.chatId);
		if(this.clearCause>0)record.set("CLEAR_CAUSE", 			this.clearCause);
		record.set("BEGIN_TIME", 			CommonUtil.parseDateTime(this.startTime));
		record.set("END_TIME", 				this.startTime>0?CommonUtil.parseDateTime(this.endTime):"");
		record.set("TOTAL_TIME", 			this.startTime>0&&this.endTime>0?(this.endTime-this.startTime)/1000:0);

		if(this.thirdClearCause>0)record.set("THIRD_CLEAR_CAUSE", 	this.thirdClearCause);
		if(thirdFlag>0){
			record.set("THIRD_REQ_TIME", 		CommonUtil.parseDateTime(this.thirdReqTime));
			record.set("THIRD_BEGIN_TIME", 		CommonUtil.parseDateTime(this.thirdStartTime));
			record.set("THIRD_END_TIME", 		this.thirdStartTime>0?CommonUtil.parseDateTime(this.thirdEndTime):"");
			record.set("THIRD_TOTAL_TIME", 		this.thirdStartTime>0&&this.thirdEndTime>0?(this.thirdEndTime-this.thirdStartTime)/1000:0);
		}
		record.set("RECORD_ID", 			this.videoRecordId);
		record.set("CONF_ID", 				this.confId);

		try {
			boolean bl = QueryFactory.getWriteQuery(entId).update(record);
			if (!bl){
				QueryFactory.getWriteQuery(entId).save(record);
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] VideoSession.save() error,cause:"+ex.getMessage(),ex);
		}
	}
	
	/**
	 *  更新聊天记录表，更新视频通话结果
	 */
	private JSONObject updateChatMsg(){
		JSONObject msgContent = null;
		JSONObject msgObj = getClearMsg();
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] VideoSession.updateChatMsg()---------->chatId:"+chatId+",msgContent:"+msgObj);
		try {
			String msgStr = QueryFactory.getWriteQuery(entId).queryForString("select MSG_CONTENT FROM " + EntContext.getContext(entId).getTableName("CC_MEDIA_CHAT_RECORD") + " WHERE CHAT_ID = ?", new Object[]{chatId});
			msgContent = JSONObject.parseObject(msgStr);
			msgContent.putAll(msgObj);
		}catch (Exception e){
		}
		this.setChatMsg(msgContent!=null?msgContent.toJSONString():msgObj.toJSONString(),true);
		return msgObj;
	}

	/**
	 * set msgContent
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2022/3/23 18:57
	 * @Param msg:
	 * @return: void
	 */
	public void setChatMsg(String msg,boolean setTime){
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] VideoSession.setChatMsg()---------->chatId:"+chatId+",msgContent:"+msg);
		try {
			EntContext  entContext = EntContext.getContext(entId);
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_CHAT_RECORD"),"CHAT_ID");
			//设定主键值
			record.setPrimaryValues(chatId);
			record.set("MSG_CONTENT",msg);
			EasyCalendar cal = EasyCalendar.newInstance();
			//2.0#20210517-1
			if(setTime){
				record.set("MSG_TIME",cal.getDateTime("-"));
				record.set("MSG_TIMESTAMP",cal.getTimeInMillis());
			}
			QueryFactory.getWriteQuery(entId).update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 设置会话记录的视频通话标志
	 */
	//2.0#20210623-1 
	private void setVideoFlag() {
		try {
			EntContext  entContext = EntContext.getContext(entId);
			EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_RECORD"),"SERIAL_ID");
			//设定主键值
			record.setPrimaryValues(chatSessionId);
			record.set("VIDEO_FLAG",1);
			QueryFactory.getWriteQuery(entId).update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 开始标记
	 * @param jsonObject
	 */
	public void startTag(JSONObject jsonObject) {
		try {
			long timestamp = jsonObject.getLongValue("timestamp");
			timestamp = timestamp>0?timestamp:System.currentTimeMillis();
			EntContext  context = EntContext.getContext(entId);
			EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_VIDEO_RECORD"), "ID");
			record.setPrimaryValues(chatId);
			record.set("START_TAG_TIME",timestamp);
			QueryFactory.getWriteQuery(entId).update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 结束标记
	 * @param jsonObject
	 */
	public void stopTag(JSONObject jsonObject) {
		try {
			long timestamp = jsonObject.getLongValue("timestamp");
			timestamp = timestamp>0?timestamp:System.currentTimeMillis();
			EntContext  context = EntContext.getContext(entId);
			EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_VIDEO_RECORD"), "ID");
			record.setPrimaryValues(chatId);
			record.set("STOP_TAG_TIME",timestamp);
			QueryFactory.getWriteQuery(entId).update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 删除标记
	 * @param jsonObject
	 */
	public void dropTag(JSONObject jsonObject) {
		try {
			long timestamp = jsonObject.getLongValue("timestamp");
			timestamp = timestamp>0?timestamp:System.currentTimeMillis();
			EntContext  context = EntContext.getContext(entId);
			EasyRecord record = new EasyRecord(context.getTableName("CC_MEDIA_VIDEO_RECORD"), "ID");
			record.setPrimaryValues(chatId);
			record.set("START_TAG_TIME","");
			record.set("STOP_TAG_TIME","");
			QueryFactory.getWriteQuery(entId).update(record);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	private JSONObject getClearMsg() {
		long time = (this.endTime-this.startTime);
		String timestr = "";
		if(this.startTime>0&&time>0) {
			timestr = CommonUtil.parseTimeStr(time/1000);
			timestr = timestr.startsWith("00:")?timestr.substring(3):timestr;
		}else {
			timestr = "0";
		}
		//{"clearCause":11,"userText":"通话时长 00:05:00","agentText":"通话时长 00:05:00","time":300,"callType":1}
		String userText = "";
		String agentText = "";
		switch (clearCause) {
		case 11:
			if(!this.isInvite()) {
				userText = "通话时长 "+timestr;
				agentText = "通话时长 "+timestr;
			}
			break;
		case 12:
			userText = "已拒绝";
			agentText = "用户已拒绝";
			
			if(istxtrtc()) {
				userText = "视频邀请已取消，可继续在线文本沟通";
			}
			
			break;
		case 13:
			userText = "对方已取消";
			agentText = "用户无应答";
			if(istxtrtc()) {
				userText = "由于您长时间未接通，视频邀请已关闭";
			}
			break;
		case 14:
			userText = "已取消";
			agentText = "用户已取消";
			break;
		case 21:
			if(!this.isInvite()) {
				userText = "通话时长 "+timestr;
				agentText = "通话时长 "+timestr;
			}
			break;
		case 22:
			userText = "坐席已拒绝";
			agentText = "已拒绝";
			break;
		case 23:
			userText = "对方超时未接听";
			agentText = "对方已取消";
			break;
		case 24:
			userText = "坐席已取消";
			agentText = "已取消";
			break;

		default:
			userText = "网络繁忙";
			agentText = "网络繁忙";
			break;
		}
		
		JSONObject msgObj = new JSONObject();
		msgObj.put("clearCause", clearCause);
		msgObj.put("userText", userText);
		msgObj.put("agentText", agentText);
		msgObj.put("time", time);
		msgObj.put("chatId", chatId);
		msgObj.put("callType", callType);
		return msgObj;
	}
	
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public String getChatSessionId() {
		return chatSessionId;
	}
	public void setChatSessionId(String chatSessionId) {
		this.chatSessionId = chatSessionId;
	}
	public int getCallType() {
		return callType;
	}
	public void setCallType(int callType) {
		this.callType = callType;
	}
	public int getClearCause() {
		return clearCause;
	}
	public void setClearCause(int clearCause) {
		this.clearCause = clearCause;
	}
	public String getVideoState() {
		return videoState;
	}
	public void setVideoState(String videoState) {
		this.videoState = videoState;
	}
	public long getInviteTime() {
		return inviteTime;
	}
	public void setInviteTime(long inviteTime) {
		this.inviteTime = inviteTime;
	}
	public long getStartTime() {
		return startTime;
	}
	public void setStartTime(long startTime) {
		this.startTime = startTime;
	}
	public long getEndTime() {
		return endTime;
	}
	public void setEndTime(long endTime) {
		this.endTime = endTime;
	}

	public String getThirdAgentId() {
		return thirdAgentId;
	}

	public void setThirdAgentId(String thirdAgentId) {
		this.thirdAgentId = thirdAgentId;
	}

	public int getThirdFlag() {
		return thirdFlag;
	}

	public void setThirdFlag(int thirdFlag) {
		this.thirdFlag = thirdFlag;
	}

	public int getThirdClearCause() {
		return thirdClearCause;
	}

	public void setThirdClearCause(int thirdClearCause) {
		this.thirdClearCause = thirdClearCause;
	}

	public String getChatId() {
		return chatId;
	}
	public void setChatId(String chatId) {
		this.chatId = chatId;
	}

	public long getThirdReqTime() {
		return thirdReqTime;
	}

	public void setThirdReqTime(long thirdReqTime) {
		this.thirdReqTime = thirdReqTime;
	}

	public long getThirdStartTime() {
		return thirdStartTime;
	}

	public void setThirdStartTime(long thirdStartTime) {
		this.thirdStartTime = thirdStartTime;
	}

	public long getThirdEndTime() {
		return thirdEndTime;
	}

	public void setThirdEndTime(long thirdEndTime) {
		this.thirdEndTime = thirdEndTime;
	}

	public String getVideoApiType() {
		return videoApiType;
	}

	public void setVideoApiType(String videoApiType) {
		this.videoApiType = videoApiType;
	}
}
