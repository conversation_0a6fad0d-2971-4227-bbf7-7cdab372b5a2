/**
 * <html>
 * <body>
 *  <P> Copyright 广州云趣信息科技有限公司</p>
 *  <p> All rights reserved.</p>
 *  <p> Created on 2018年6月2日 上午10:07:30</p>
 *  <p> Created by wubin</p>
 *  </body>
 * </html>
 */
package com.yunqu.cc.online.dao;

import java.sql.SQLException;
import java.util.List;
import java.util.Map;

import org.apache.log4j.Logger;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.MapRowMapperImpl;

import com.alibaba.fastjson.JSON;
import com.yunqu.cc.online.base.AppDaoContext;
import com.yunqu.cc.online.exception.BizException;
import com.yunqu.cc.online.log.CommLogger;

/**     
* @Package：com.yunqu.cc.online.dao   
* @ClassName：OtherDao   
* @Description：   <p> OtherDao</p>
* @Author： - wubin   
* @CreatTime：2018年6月2日 上午10:07:30   
* @Modify By：   
* @ModifyTime：  2018年6月2日
* @Modify marker：   
* @version    V1.0
*/
@WebObject(name="Other")
public class OtherDao extends AppDaoContext{
	
	private Logger logger = CommLogger.logger;
	
	/**
	 * @Description: 获取所有常用语分类  -  节点挂载 Node title or 关联表的Content - 后期看产品
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getAllPhraseDir(){
		List<Map<String, String>> allPhrase = null;
		try {
			/**
			 * 思路: 左表为目录表 - 常用语表  内容+标题
			 */
			/*SELECT DISTINCT CCPD."ID",
			CCPD.CODE,
			CCPD.ENABLE_STATUS,
			CCPD. NAME,
			CCPD.PARENT_ID,
			CCP.TITLE,
			CCP."CONTENT"
			FROM C_CF_PHRASE_DIR CCPD
			LEFT JOIN C_CF_PHRASE CCP ON CCPD. ID = CCP.DIR_ID
			WHERE
				1 = 1*/
			EasySQL sql = new EasySQL("SELECT * FROM C_CF_PHRASE_DIR WHERE 1 = 1 ");
			/*EasySQL sql = new EasySQL("SELECT DISTINCT CCPD.ID, CCPD.PARENT_ID, CCPD.NAME, CCPD.CODE,CCPD.ENABLE_STATUS ,CCP.TITLE,CCP.CONTENT FROM C_CF_PHRASE_DIR CCPD ");
			sql.append("LEFT JOIN C_CF_PHRASE CCP ON CCPD. ID = CCP.DIR_ID WHERE 1 = 1");*/
			//sql.append("","AND CCPD.ID = ?"); 条件
			allPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			logger.error(String.format("常用语内容数据查询异常!异常信息为:%s",e));
			throw new BizException(String.format("常用语内容数据查询异常!异常信息为:%s",e), e);
		}
		return allPhrase;
	}
	
	/**
	 * @Description:  获取个人常用语 - 目录
	 * @param userAcc 当前用户账号
	 * @param enable  禁用启用状态  01 02 
	 * @param type    01 个人  02公共  目录类型 
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getPersonCommon(String userAcc,String enable,String type) {
		List<Map<String, String>> personPhrase = null;
		try {
			EasySQL sql = new EasySQL("SELECT * FROM C_CF_PHRASE_DIR WHERE 1 = 1 ");
			sql.append(type, "AND TYPE = ?");//01个人  02公共
			sql.append(userAcc, "AND CREATE_ACC = ?");//当前用户acc
			sql.append(enable, "AND ENABLE_STATUS = ?");//禁用启用状态  01 02   数据库C_CF_PHRASE_DIR 设计有问题 父节点禁用 子节点并没有禁用 后期优化
			sql.append("ORDER BY SORT_NUM DESC");//排序
			/*EasySQL sql = new EasySQL("SELECT CCPD. NAME, CCPD.PARENT_ID, CCP.DIR_ID, CCP.ID,CCP.TITLE,CCP.CONTENT,CCPD.ENABLE_STATUS");
			sql.append("FROM C_CF_PHRASE_DIR CCPD LEFT JOIN C_CF_PHRASE CCP ON CCPD. ID = CCP.DIR_ID");*/
			personPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			logger.error(String.format("获取个人常用语目录异常!异常信息为:%s",e));
			throw new BizException(String.format("获取个人常用语目录异常!异常信息为:%s",e), e);
		}
		return personPhrase;
	}
	
	
	/**
	 * @Description: 获取公共常用语  - 目录
	 * @param enable 开启状态
	 * @param type   类型  02 公共
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getCommonPhrase(String enable,String type) {
		List<Map<String, String>> personPhrase = null;
		try {
			EasySQL sql = new EasySQL("SELECT * FROM C_CF_PHRASE_DIR WHERE 1 = 1 ");
			sql.append(type, "AND TYPE = ?");//01个人  02公共
			sql.append(enable, "AND ENABLE_STATUS = ?");//禁用启用状态  01 02   数据库C_CF_PHRASE_DIR 设计有问题 父节点禁用 子节点并没有禁用 后期优化
			sql.append("ORDER BY SORT_NUM DESC");//排序
			personPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			logger.error(String.format("获取公共常用语目录异常!异常信息为:%s",e));
			throw new BizException(String.format("获取公共常用语目录异常!异常信息为:%s",e), e);
		}
		return personPhrase;
	}
	
	
	
	/**
	 * @Description:   获取常用语 - 内容
	 * @param userAcc  当前用户的账号
	 * @param isPublic 是否公开
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getPersonPhrase(String userAcc,String isPublic) {
		List<Map<String, String>> personPhrase = null;
		try {
			EasySQL sql = new EasySQL("SELECT * FROM C_CF_PHRASE WHERE 1 = 1");
			sql.append(userAcc, "AND CREATE_ACC = ?");//当前用户acc
			sql.append(isPublic,"and IS_PUBLIC = ");//是否公开 N or Y 
			sql.append("ORDER BY SORT_NUM DESC");//排序
			personPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			logger.error(String.format("获取个人常用语目录异常!异常信息为:%s",e));
			throw new BizException(String.format("获取个人常用语目录异常!异常信息为:%s",e), e);
		}
		return personPhrase;
	}
	

	/**
	 * @Description: 常用语内容数据 - findById
	 * @param ID     常用语ID
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getAllPhrase(String ID) {
		List<Map<String, String>> allPhrase = null;
		try {
			EasySQL sql = new EasySQL("SELECT CCPD.ID AS GROUPID,CCPD.NAME,CCP.DIR_ID,CCP.ID FROM C_CF_PHRASE_DIR CCPD");
			sql.append("LEFT JOIN C_CF_PHRASE CCP ON CCPD. ID = CCP.DIR_ID");
			sql.append("WHERE 1 = 1");
			sql.append("","AND CCPD.ID = ?");
			allPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			throw new BizException(String.format("常用语内容数据查询异常! 请求入参为:%s ,异常信息为:%s",JSON.toJSONString(ID)), e);
		}
		return allPhrase;
	}
	
	
	/**
	 * @Description: 通过KeyWord搜索内容- Not目录
	 * @param keyWord
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getPhraseBySearchKeyWord(String keyWord) {
		List<Map<String, String>> allPhrase = null;
		try {
			EasySQL sql = new EasySQL("SELECT  CCP.CONTENT FROM  C_CF_PHRASE CCP WHERE 1=1 AND ENABLE_STATUS='01' ");//ENABLE_STATUS 是否开启
			sql.appendLike(keyWord, "AND CONTENT LIKE ?");
			allPhrase = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			throw new BizException(String.format("数据库获取常用语 通过keyWord异常! 请求入参为:%s ,异常信息为:%s",JSON.toJSONString(keyWord)), e);
		}
		return allPhrase;
	}
	
	
	/**
	 * @Description:   搜索知识库关键字信息
	 * @param keyWord  关键字
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> getKnowBySearchKeyWord(String keyWord) {
		List<Map<String, String>> knowList = null;
		try {
			EasySQL sql = new EasySQL("SELECT  TITLE, CONTENT,CREATE_TIME FROM  C_CF_KEYWORD CCK WHERE 1=1 ");
			sql.appendLike(keyWord, "AND CONTENT LIKE ?");
			sql.append("ORDER BY CREATE_TIME DESC");
			knowList = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			throw new BizException(String.format("搜索知识库关键字信息异常! 请求入参为:%s ,异常信息为:%s",JSON.toJSONString(keyWord)), e);
		}
		return knowList;
	}
	
	/**
	 * @Description: 加载关键字的tab初始化数据 - 每次条用inf接口会将搜索的关键字入库保存
	 * @param uid	  用户userId
	 * @param loginAcct 用户账号
	 * @return List<Map<String,String>>
	 * @Autor: wubin - <EMAIL>
	 */
	public List<Map<String, String>> initkeyWord(String uid,String loginAcct) {
		List<Map<String, String>> knowList = null;
		try {
			//条件过滤 分组统计查询  order by createTime and limit 展示条数限制  优化为分组后排序 按照nums or time
			/*SELECT COK.KEYWORD,COUNT (*) AS NUMS FROM C_OL_KEYWORD_SRH COK WHERE CREATE_ACC = 'admin@mars' GROUP BY KEYWORD*/
			/*EasySQL sql = new EasySQL("SELECT  COK.KEYWORD ,COUNT (*) AS NUMS FROM C_OL_KEYWORD_SRH COK  WHERE 1=1 ");
			sql.appendLike(loginAcct, "AND CREATE_ACC LIKE ?");
			sql.append("GROUP BY KEYWORD");*/
			EasySQL sql = new EasySQL("select * from (SELECT  COK.KEYWORD ,COUNT (*) AS NUMS FROM C_OL_KEYWORD_SRH COK  WHERE 1=1  ");
			sql.append(loginAcct, "AND CREATE_ACC = ?");
			sql.append("GROUP BY KEYWORD ORDER BY NUMS DESC ) d where ROWNUM <= 10");
			knowList = getQuery().queryForList(sql.getSQL(), sql.getParams(), new MapRowMapperImpl());
		} catch (SQLException e) {
			throw new BizException(String.format("搜索知识库关键字信息异常! 请求入参为:%s ,异常信息为:%s",JSON.toJSONString(uid)), e);
		}
		return knowList;
	}
	
}
