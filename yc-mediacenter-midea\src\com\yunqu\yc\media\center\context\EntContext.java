package com.yunqu.yc.media.center.context;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.listener.GlobalContextListener;
import com.yunqu.yc.media.center.log.MediaCenterDeamonLogger;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.*;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.util.CacheUtil;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

public class EntContext {
	
	private String petraGwUrl;
	private String marsGwUrl;
	private String entId = "";
	private String schemaId;
	private String marsId;
	private String petraId;
	private String entName;
	private String resEntId;
	private String entType;
	private String entCode;
	private String feeCode;
	private String busiOrderId;
	private static Thread daemonThread;

	/**
	 *  在线用户信息
	 */
	private Map<String,UserSession> users = new ConcurrentHashMap<String,UserSession>();
	
	/**
	 * 企业所有用户排队信息，用于防止同一个人在同一个渠道出现多个排队
	 */
	private Map<String,UserSession> queueUsers = new HashMap<String,UserSession>();
	
	/**
	 * 保存企业的技能组信息
	 */
	private Map<String,SkillGroup>  skillGroups = new HashMap<String,SkillGroup>();
	/**
	 * 保存企业的坐席信息
	 */
	private Map<String,Agent>  agents  = new HashMap<String,Agent>();
	
	/**
	 * 保存企业的坐席信息
	 */
	private Map<String,Channel>  channels  = new HashMap<String,Channel>();
	
	private static final Map<String,EntContext> contexts = new HashMap<String,EntContext>();

	private static final String DEFENTID = "1000";
	/**
	 * 是否正在统计排队
	 */
	private static boolean isMonitorQueue = false;
	
	public static Collection<EntContext> getEntContexts(){
		return contexts.values();
	}
	
	public static EntContext getContext(String entId){
		if(StringUtils.isBlank(entId)) {
			//v2.0#20200103-1
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] EntContext.getContext() error >>cause: entId is not found,use default value '1000'!");
			entId = DEFENTID;
		}
		EntContext context = contexts.get(entId);
		if(context != null) {
			return context;
		}
		synchronized (entId.intern()){
			context = contexts.get(entId);
			if(context != null) {
				return context;
			}
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] >>>>>>>>EntContext.init("+entId+") not exisit,run init...");
			try {
				context = new EntContext(entId);
				context.initEntInfo();
				context.reloadMemberConfig();
				contexts.put(entId, context);
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] EntContext.init() error >>cause:"+ex.getMessage(),ex);
				return null;
			}

			try {
				context.initAgentInfo();
				context.initSkillGroupInfo();
				context.initChannelInfo();
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] EntContext.init() error >>cause:"+ex.getMessage(),ex);
			}
			context.startDaemonThread();
			return context;
		}
	}
	
	public Map<String,UserSession> getQueueUsers(){
		return this.queueUsers;
	}
	
	public UserSession getQueueUser(String sessionId){
		return this.queueUsers.get(sessionId);
	}
	
	public Map<String,SkillGroup> getSkillGroups(){
		return this.skillGroups;
	}
	
	public void addQueueUser(UserSession user){
		this.queueUsers.put(user.getSessionId(), user);
	}
	
	public Map<String, UserSession> getUsers() {
		return users;
	}

	public void setUsers(Map<String, UserSession> users) {
		this.users = users;
	}

	public UserSession removeQueueUser(String sessionId){
		return this.queueUsers.remove(sessionId);
	}
	
	public UserSession getUser(String sessionId){
		return this.users.get(sessionId);
	}
	
	public void addUser(String sessionId,UserSession userSession){
		this.users.put(sessionId, userSession);
	}
	
	public void removeUser(String sessionId){
		this.users.remove(sessionId);
	}
	
	public void addAgent(String agentId,Agent agent){
		agents.put(agentId, agent);
	}
	
	public void removeAgent(String agentId) {
		agents.remove(agentId);
	}
	
	public SkillGroup getSkillGroup(String skillGroupId){
		return skillGroups.get(skillGroupId);
	}
	
	public String getBusiOrderId() {
		return busiOrderId;
	}

	public void setBusiOrderId(String busiOrderId) {
		this.busiOrderId = busiOrderId;
	}

	
	public   Channel   getChannel(String channelId){
		if(!channels.containsKey(channelId)) {
			initChannelInfo();
		}
		return channels.get(channelId);
	} 
	
	
	public   Map<String,Channel>  getChannels(){
		return  this.channels;
	}
	
	public  void releaseAll(){
		try {
			Collection<UserSession>  colls = users.values();
			for(UserSession user:colls) user.logout(9);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		try {
			Collection<Agent>  colls = agents.values();
			for(Agent agent:colls) agent.logout(9);
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		try {
			Collection<SkillGroup>  colls = skillGroups.values();
			for(SkillGroup group:colls) group.stopQueueThread();
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	
	/**
	 * 从企业坐席列表中查找坐席，如果没有则从数据库中查询
	 * @param agentId
	 * @return
	 */
	public Agent getAgentInfo(String agentId){
		Agent agent =  agents.get(agentId);
		if(agent == null){
			agent = getAgentInfoByDb(agentId);
			//查询坐席技能组
			if(agent!=null) {
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] 从企业坐席列表中查找坐席["+agentId+"] 未找到，查询数据库重新创建坐席对象agent.hashCode()-->"+agent.hashCode());
				String sql = "select t1.SKILL_GROUP_ID,t2.USER_ACCT from "+getTableName( "CC_SKILL_GROUP_USER")+" t1 , CC_USER t2 "
						+ " where t1.USER_ID = t2.USER_ID  and t1.ENT_ID = ?   and t1.BUSI_ORDER_ID = ?  and t2.USER_ACCT  = ? ";
				try {
					List<JSONObject> rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId,getBusiOrderId(),agentId},new JSONMapperImpl());
					for (JSONObject row1 : rows) {
						String groupId = row1.getString("SKILL_GROUP_ID");
						if(!agent.getGroupIds().contains(groupId)) {
							agent.addSkillGroup(groupId);
						}
					}
				} catch (Exception e) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
				}
//				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] get agentInfo by database -> "+JSONObject.toJSONString(agent));
				agents.put(agent.getAgentId(), agent);
			}
		}
		return agent;
	}
	
	/**
	 * 从数据库中查询坐席信息。
	 * @param agentId
	 * @return
	 */
	public Agent getAgentInfoByDb(String agentId){
		Agent agent = null;

		String 	sql = "select t1.* ,t2.USER_ACCT"
				+ "	from "+getTableName( "CC_BUSI_USER")+" t1 ,CC_USER t2  "
				+ " where t1.USER_ID = t2.USER_ID AND t2.ADMIN_FLAG = 0 and t1.BUSI_ORDER_ID = ?  and t1.ENT_ID = ? and t2.USER_ACCT=? and t2.USER_STATE = 0";
		try {
			JSONObject row = QueryFactory.getReadQuery().queryForRow(sql, new Object[] { busiOrderId, entId, agentId },new JSONMapperImpl());
			if(row==null)  return null;
			agent = new Agent();
			agent.setAgentId(row.getString("USER_ACCT"));  //这里送给平台的是坐席工号，例如：8001@1000
			agent.setAgentName(row.getString("AGENT_NAME"));
			agent.setAgentPhone(row.getString("AGENT_PHONE"));
			agent.setEntId(row.getString("ENT_ID"));
			JSONObject extConf = row.getJSONObject("EXT_CONF");
			if(extConf!=null) {
				agent.setNickName(extConf.getString("nickname"));
			}
			
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		return agent;
	}
	
	/**
	 * 从数据库中统计坐席当日服务人数
	 * @param agentId
	 * @return
	 */
	//20210406 增加查询坐席当日服务总数
	public int getAgentServiceCount(String agentId){
		Agent agent = this.agents.get(agentId);
		
		String sql = "select count(1) from "+getTableName("CC_MEDIA_RECORD")+" where AGENT_ID = ? AND DATE_ID = ?";
		try {
			return QueryFactory.getReadQuery().queryForInt(sql, new Object[] {agentId,agent.getDateId()});
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		return 0;
	}
	
	/**
	 * 从数据库中查询坐席正在服务的用户
	 * @param agentId
	 * @return
	 */
	public List<JSONObject> getAgentServiceUser(String agentId){
		
		String sql = "select * from "+getTableName("CC_MEDIA_RECORD")+" where AGENT_ID = ? AND SERVER_STATE=2";
		try {
			return QueryFactory.getReadQuery().queryForList(sql, new Object[] {agentId}, new JSONMapperImpl());
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		return null;
	}
	
	
	
	/**
	 * 获得企业所在的schema
	 * @param tableName 企业名称
	 * @return
	 */
	public String getTableName(String tableName){
		return this.getSchemaId() + "." + tableName;
	}
	
	
	private EntContext(String entId) throws Exception{
		if(StringUtils.isBlank(entId)) throw new Exception("entId is null!");
		this.entId = entId;
		
	}
	
	private void startDaemonThread(){

		if(daemonThread!=null){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] EntContext.init() start DaemonThread is error >>cause: daemonThread is not null!");
			return ;
		}
		daemonThread = new Thread(new DaemonThread(this.entId));
		daemonThread.start();
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.init() start DaemonThread is finish!");
	}
	
	
	
	/**
	 * 初始化企业的相关上下文信息，包括：磐石网关，mars网关和数据库等
	 * @throws Exception
	 */
	private void initEntInfo() throws Exception{
		
		String sql = "select P_ENT_ID,ENT_TYPE,ENT_NAME,ENT_CODE,FEE_CODE from CC_ENT where ENT_ID = ?";
		
		JSONObject entInfo = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{this.entId},new JSONMapperImpl());
		
		if("3".equals(entInfo.getString("ENT_TYPE"))){
			this.resEntId = entInfo.getString("P_ENT_ID");
		}else{
			this.resEntId = this.entId;
		}
		this.entType = entInfo.getString("ENT_TYPE");
		this.entName = entInfo.getString("ENT_NAME");
		this.entCode = entInfo.getString("ENT_CODE");
		this.feeCode = entInfo.getString("FEE_CODE");
		
		sql = "select   t1.PETRA_ID,t1.MARS_ID,t2.PETRA_URL,t3.MARS_URL ,t1.SCHEMA_ID    from CC_ENT_RES  t1  , CC_PETRA_RES t2 ,  CC_MARS_RES t3   where  t1.PETRA_ID = t2.PETRA_ID and t1.MARS_ID = t3.MARS_ID  and t1.ENT_ID = ?";
		EasyRow row = QueryFactory.getReadQuery().queryForRow(sql, new Object[]{this.getResEntId()});
		if(row == null) return;
		this.petraId = row.getColumnValue("PETRA_ID");
		this.marsId = row.getColumnValue("MARS_ID");
		this.petraGwUrl = row.getColumnValue("PETRA_URL")+"/petradatagw/interface";
		this.marsGwUrl = row.getColumnValue("MARS_URL") +"/yc-api/interface";
		this.schemaId = row.getColumnValue("SCHEMA_ID");
		
		sql = "select BUSI_ORDER_ID  from CC_BUSI_ORDER where ENT_ID = ? and BUSI_ID = '003'";
		
		this.busiOrderId =  QueryFactory.getReadQuery().queryForString(sql, new Object[]{this.getResEntId()});
		
		
	}
	
	/**
	 * 加载技能组信息
	 */
	private void initSkillGroupInfo(){
		
//		String 	sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME,ENT_ID,QUEUE_STRATEGY,CHANNEL_ID,KEY_ID  from "+getTableName( "CC_SKILL_GROUP")+" where ENT_ID = ?  and BUSI_ORDER_ID = ?";
		String 	sql = "select * from "+getTableName( "CC_SKILL_GROUP")+" where ENT_ID = ?  and BUSI_ORDER_ID = ?";
		List<JSONObject> rows;
		try {
			rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId,this.busiOrderId },new JSONMapperImpl());
			for (JSONObject row : rows) {
				if(skillGroups.containsKey(row.getString("SKILL_GROUP_ID"))) continue;
				SkillGroup skillGroup = new SkillGroup();
				skillGroup.setSkillGroupId(row.getString("SKILL_GROUP_ID"));
				skillGroup.setSkillGroupName(row.getString("SKILL_GROUP_NAME"));
				skillGroup.setEntId(entId);
				skillGroup.setPolicy(row.getString("QUEUE_STRATEGY"));
				skillGroup.setIdxOrder(row.getIntValue("IDX_ORDER"));
				skillGroup.setLoadStatus("0");
				skillGroup.reload();
				skillGroup.reloadMemberQueue(getMemberConfig());
				if(skillGroup.getThread()==null) {
					skillGroup.initQueueThread();
				}
				skillGroups.put(row.getString("SKILL_GROUP_ID"),skillGroup);
			}
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.init()  initSkillGroupInfo is finish!");
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}

		
	}
	
	private void initAgentInfo(){
		String 	sql = "select t1.ENT_ID ,t2.USER_ACCT ,t1.AGENT_NAME ,t2.AGENT_PHONE "
					+ "	from "+getTableName( "CC_BUSI_USER")+" t1 ,CC_USER t2  "
					+ " where t1.USER_ID = t2.USER_ID AND t2.ADMIN_FLAG = 0 and t1.BUSI_ORDER_ID = ?  and t1.ENT_ID = ?  and t2.USER_STATE = 0";
		List<EasyRow> rows;
		try {
			rows =  QueryFactory.getReadQuery().queryForList(sql, new Object[] { this.getBusiOrderId(),this.entId});
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] >>>init agentInfo,size()->"+rows.size());
			for(EasyRow row:rows){
				Agent agent = new Agent();
				agent.setAgentId(row.getColumnValue("USER_ACCT"));  //这里送给平台的是坐席工号，例如：8001@1000
				agent.setAgentName(row.getColumnValue("AGENT_NAME"));
				agent.setEntId(row.getColumnValue("ENT_ID"));
				agent.setAgentPhone(row.getColumnValue("AGENT_PHONE"));
				agents.put(row.getColumnValue("USER_ACCT"), agent);
			}
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.init()  initAgentInfo is finish!");
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	

	
	/**
	 * 初始化渠道信息
	 */
	private void initChannelInfo(){
		String sql = "select CHANNEL_ID from CC_CHANNEL where ENT_ID = ?";
		List<EasyRow> rows;
		try {
			rows =  QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId });
			for(EasyRow row:rows){
				Channel channel = new Channel();
				channel.setChannelId(row.getColumnValue("CHANNEL_ID"));
				channel.setChannelName(row.getColumnValue("CHANNEL_NAME"));
				channel.setEntId(this.entId);
				channel.reload();
				channels.put(row.getColumnValue("CHANNEL_ID"), channel);
			}
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.init() initChannelInfo is finish!");
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	
	public String getSchemaId() {
		return schemaId;
	}

	public String getMarsId() {
		return marsId;
	}

	public String getPetraId() {
		return petraId;
	}

	public String getPetraGwUrl(){
		return this.petraGwUrl;
	}
	
	public String getMarsGwUrl(){
		return this.marsGwUrl;
	}


	public String getEntName() {
		return entName;
	}


	public String getResEntId() {
		return resEntId;
	}


	public void setResEntId(String resEntId) {
		this.resEntId = resEntId;
	}


	public String getEntType() {
		return entType;
	}


	public void setEntType(String entType) {
		this.entType = entType;
	}


	public String getEntCode() {
		return entCode;
	}


	public String getFeeCode() {
		return feeCode;
	}
	
	
	
	
	private class DaemonThread  implements Runnable{
		
		private String entId;
		
		public DaemonThread(String entId) {
			this.entId = entId;
		}

		@Override
		public void run() {
			
			try {
				Thread.sleep(30*1000);
			} catch (Exception e) {
				// TODO: handle exception
			}
			
			EntContext entContext = getContext(entId);

			int runCont = 0;
			while (GlobalContextListener.runState) {

				runCont++;
				try {
					Thread.sleep(50);
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				// 每15秒扫描一次
				if(runCont<300){
					continue;
				}
				runCont=0;
				MediaCenterDeamonLogger.getLogger().info("DaemonThread->run check...");

				entContext.systemMonitor();
				entContext.reloadMemberConfig();
				try {
					entContext.syncMonitorInfo();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				
				try {
					entContext.checkAgent();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				
				try {
					entContext.checkSkillgroup();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				
				try {
					entContext.checkChannel();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				
				try {
					entContext.checkEnt();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}
				
				try {
					entContext.checkUser();
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				}

				
			}
		}
	}
	
	/**
	 * 系统监控
	 * 实时监控各个渠道和各个技能组的在线人数和排队人数
	 */
	public void systemMonitor(){
		if(isMonitorQueue){
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] systemMonitor is running,do not begin new monitor...");
			return;
		}
		isMonitorQueue = true;
		
		try {

			//遍历获取渠道信息
			JSONObject channelInfo = new JSONObject();
			Set<Channel> _channels  = new HashSet<>(channels.values());
			for (Channel _channel : _channels) {
				JSONObject chanelObject = new JSONObject();
				chanelObject.put("queueCount", 0);
				chanelObject.put("onlineUserCount", 0);
				chanelObject.put("channelKey",_channel.getChannelKey());
				chanelObject.put("channelId",_channel.getChannelId());
				chanelObject.put("channelName", _channel.getChannelName());
				chanelObject.put("channelType", _channel.getChannelType());
				channelInfo.put(_channel.getChannelId(), chanelObject);
			}
			
			//遍历获取技能组信息
			JSONObject groupInfo = new JSONObject();
			Set<SkillGroup> _groups  = new HashSet<>(skillGroups.values());
			for (SkillGroup _skillGroup : _groups) {
				JSONObject groupObject = new JSONObject();
				groupObject.put("queueCount", 0);
				groupObject.put("onlineUserCount", 0);
				groupObject.put("onlineAgentCount", _skillGroup.getOnlineAgentSize());
				groupObject.put("groupId", _skillGroup.getSkillGroupId());
				groupObject.put("groupName", _skillGroup.getSkillGroupName());
				groupInfo.put(_skillGroup.getSkillGroupId(), groupObject);
			}
			
			for (Channel channel : _channels) {
				Set<ChannelKey> channleKeys = new HashSet<>(channel.getChannelKeys().values());
				for(ChannelKey _channelKey :channleKeys){
					try {
						if(channelInfo.containsKey(_channelKey.getChannelId())){
							JSONObject _channel = channelInfo.getJSONObject(_channelKey.getChannelId());
							_channel.put("queueCount", _channel.getIntValue("queueCount") + _channelKey.getQueueSize());
							channelInfo.put(_channelKey.getChannelId(), _channel);
						}
						
						String skillGroupId = _channelKey.getSkillGroup().getSkillGroupId();
						if(groupInfo.containsKey(skillGroupId)){
							JSONObject _group = groupInfo.getJSONObject(skillGroupId);
							_group.put("queueCount", _group.getIntValue("queueCount") + _channelKey.getQueueSize());
							groupInfo.put(skillGroupId, _group);
						}
					} catch (Exception e) {
//						MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
//			MediaCenterLogger.getLogger().error(e.getMessage(),e);
					}
				}
			}
			
			Set<UserSession> _users  = new HashSet<>(users.values());
			for(UserSession userSession : _users){
				try {
					JSONObject _channel = channelInfo.getJSONObject(userSession.getChannel().getChannelId());
					_channel.put("onlineUserCount", _channel.getIntValue("onlineUserCount") + 1);
					channelInfo.put(userSession.getChannel().getChannelId(), _channel);
					
					String skillGroupId = userSession.getChannelKey().getSkillGroup().getSkillGroupId();
					JSONObject _group = groupInfo.getJSONObject(skillGroupId);
					_group.put("onlineUserCount", _group.getIntValue("onlineUserCount") + 1);
					groupInfo.put(skillGroupId, _group);
				} catch (Exception e) {
//					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
//			MediaCenterLogger.getLogger().error(e.getMessage(),e);
				}
			}
			
			EasyCalendar cal = EasyCalendar.newInstance();
			JSONObject monitorInfo = new JSONObject();
			monitorInfo.put("channelInfo", channelInfo);
			monitorInfo.put("groupInfo", groupInfo);
			monitorInfo.put("updateTime", cal.getDateTime("-"));
			monitorInfo.put("totalOnlineUserCount", users.size());
			monitorInfo.put("totalQueueCount", queueUsers.size());
			monitorInfo.put("totalOnlineAgentCount", agents.size());
			String key = "MEDIA_MONITOR_DATA_"+entId;
			CacheUtil.put(key,monitorInfo.toJSONString(),300);
			monitor();
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
		
		isMonitorQueue = false;
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] systemMonitor is finish!");
	}
	
	
	/**
	 * 排行榜监控
	 */
	private void  monitor(){
		//监控排队情况
		JSONObject typeQueueInfo = new JSONObject();
		JSONObject channelQueueInfo = new JSONObject();
		typeQueueInfo.put("1", 0);
		typeQueueInfo.put("2", 0);
		typeQueueInfo.put("3", 0);
		typeQueueInfo.put("4", 0);
		typeQueueInfo.put("5", 0);
		typeQueueInfo.put("6", 0);
		typeQueueInfo.put("7", 0);
		typeQueueInfo.put("8", 0);
		typeQueueInfo.put("9", 0);
		Set<UserSession> queueUsersSet =  new HashSet<>(queueUsers.values());
		for(UserSession user : queueUsersSet){
			Channel channel = user.getChannel();
			if(StringUtils.isBlank(user.getChannelType())) continue;
			if(StringUtils.isNotBlank(user.getAgentId())) continue;
			int quereCount = typeQueueInfo.getIntValue(channel.getChannelType()) +1;
			typeQueueInfo.put(user.getChannelType(), quereCount);
			
			JSONObject  channelJson = channelQueueInfo.getJSONObject(channel.getChannelKey());
			if(channelJson == null){
				channelJson =  new JSONObject();
				channelJson.put("channelId", channel.getChannelKey());
				channelJson.put("channelName", channel.getChannelName());
				channelJson.put("channelType", channel.getChannelType());
				channelJson.put("queueCount", 0);
				channelQueueInfo.put(channel.getChannelKey(), channelJson);
			}
			channelJson.put("queueCount", channelJson.getIntValue("queueCount")+1);
		}
		
		JSONObject queueInfo = new JSONObject();
		queueInfo.put("typeQueyInfo", typeQueueInfo);
		
		JSONObject _channelQueryInfo = new JSONObject();
		Set<String> channelKeys = new HashSet<>(channelQueueInfo.keySet());
		for(String channelKey : channelKeys){
			JSONObject channelJson = channelQueueInfo.getJSONObject(channelKey);
			JSONArray channelTypes = _channelQueryInfo.getJSONArray(channelJson.getString("channelType"));
			if(channelTypes == null){
				channelTypes = new JSONArray();
			}
			channelTypes.add(channelJson);
			_channelQueryInfo.put(channelJson.getString("channelType"), channelTypes);
		}
		queueInfo.put("channelQueueInfo", _channelQueryInfo);
		CacheUtil.put("MEDIA_QUEUE_INFO_"+entId,queueInfo.toJSONString(),120);
		
		EasyCalendar cal = EasyCalendar.newInstance();
		String sql = "select agent_name , count(*) as serviceCount  from "+getTableName("cc_media_record")+"  t2 where ent_id = ? and date_id = ?  group by agent_name  order by serviceCount desc";
		List<JSONObject> list = new ArrayList<JSONObject>();
		try {
				EasyQuery query = QueryFactory.getReadQuery();
				query.setMaxRow(15);
				list = query.queryForList(sql,new Object[]{entId,cal.getDateInt()},new JSONMapperImpl());
		} catch (Exception ex) {
				list = new ArrayList<JSONObject>();
		}
		JSONObject rankInfo = new JSONObject();
		rankInfo.put("rankInfo", list);
		CacheUtil.put("MEDIA_SERVICE_RANK_"+entId,rankInfo.toJSONString(),300);
	}
	
	/**
	 * 同步监控中的技能组信息
	 */
	private void syncMonitorInfo(){
		try {
			Set<SkillGroup> groupSet = new HashSet<>(skillGroups.values());
			for (SkillGroup group : groupSet) {
				String key = "mediacenter_monitor_group_"+group.getSkillGroupId();
				String value = group.getMonitorInfo().toJSONString();
				CacheUtil.put(key, value);
			}
			
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
	}
	
	/**
	 * 检查坐席
	 * @throws Exception
	 */
	private void checkAgent(){
		MediaCenterDeamonLogger.getLogger().info("DaemonThread->run check agent ...");
		
		try {
			Set<Agent> agentSet = new HashSet<>(agents.values());
			for (Agent agent : agentSet) {
				if(!agent.online()) continue;
				if(agent.readyTimeout()){  //如果超过多长时间没有任何消息处理，则系统自动把坐席置忙
					if(agent.isReady()){
						MediaCenterDeamonLogger.getLogger().info("Agent["+agent.getAgentId()+"] readyTimeout(180s) ,run notReadyNotify。");
						agent.notReadyNotify();  
					}
					if(agent.serviceTimeout()) {
						MediaCenterDeamonLogger.getLogger().info("Agent["+agent.getAgentId()+"] serviceTimeout(1800s) ,run logoutNotify。");
						agent.logoutNotify();
					}
				}
			}
			
		} catch (Exception e) {
		}
	}
	
	
	/**
	 * 检查用户
	 * 1.接入会话，计算坐席超时回复。不计算访客超时回复，不计算会话超时。
	 * 2.坐席超时，回复坐席超时提示语，计算访客超时，计算会话超时，不计算坐席超时。
	 * 3.回复访客超时提示语，计算会话超时，不计算坐席超时。
	 * 4.访客回复了，不计算会话超时，不计算访客超时，计算坐席超时回复。
	 * @throws Exception
	 */
	private void checkUser() throws Exception{

		Set<UserSession> userset = new HashSet<>(users.values());
		for (UserSession user : userset) {
			try {
//				MediaCenterDeamonLogger.getLogger().info("checkUser()->"+user.toString());
				if(StringUtils.isBlank(user.getAgentId())){
					MediaCenterDeamonLogger.getLogger().warn("EntContext.checkUser() ,entId["+entId+"] agentId is null:"+user.toString());
					MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] EntContext.checkUser()--->检查用户时，坐席id为空，强制结束会话:"+user.toString());
					String msg = user.getChannel().getAutoConfig().getAutoCloseMsg();
					if(StringUtils.isNotBlank(msg)) {
						this.sendMsgToUser(user.getChannel().getChannelId(), user,msg);
						this.sendSysMsgToAgent(user.getChannel().getChannelId(), user,msg);
					}
					user.logout(5);
					users.remove(user.getSessionId());
					continue;
				}
				
				//用户正在视频通话中，不检测坐席和访客文本会话超时
				if(user.hasVideo()) {
					continue;
				}
				
				// 访客回复超时标志，回复访客超时提示语
				if (user.checkUserReplyMsgTimeout()) {
					if(!user.isSendUserTimeoutFlag()){
						MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.checkUser()  user reply message is timeout:"+user.toString());
						MediaCenterDeamonLogger.getLogger().info("EntContext.checkUser()  user reply message is timeout:"+user.toString());
						String msg = user.getChannel().getAutoConfig().getUserTimeoutMsg();
						this.sendMsgToUser(user.getChannel().getChannelId(), user,msg);
						this.sendSysMsgToAgent(user.getChannel().getChannelId(), user,msg);
						user.setSendUserTimeoutFlag(true);
					}
				}

				// 用户会话超时
				if (user.checkUserTimeout()) {
					MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.checkUser()  user chat is timeout:"+user.toString());
					MediaCenterDeamonLogger.getLogger().info("EntContext.checkUser()  user chat is timeout:"+user.toString());
					String msg = user.getChannel().getAutoConfig().getAutoCloseMsg();
					this.sendMsgToUser(user.getChannel().getChannelId(), user,msg);
					this.sendSysMsgToAgent(user.getChannel().getChannelId(), user,msg);
					Thread.sleep(100);
					user.logout(3);
					users.remove(user.getSessionId());
				}

				// 如果坐席回复超时，回复坐席超时提示语
				if (user.checkAgentMsgTimeout()) {
					MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] EntContext.checkUser()  agent["+user.getAgentId()+"] reply message is timeout:"+user.toString());
					MediaCenterDeamonLogger.getLogger().info("EntContext.checkUser()  agent["+user.getAgentId()+"] reply message is timeout:"+user.toString());
					String msg = user.getChannel().getAutoConfig().getAgentTimeoutMsg();
					this.sendMsgToUser(user.getChannel().getChannelId(), user,msg);
					this.sendSysMsgToAgent(user.getChannel().getChannelId(), user,msg);
					user.sysUpdateAgentTime(System.currentTimeMillis());
//					user.updateAgentTime(System.currentTimeMillis());
				}
			} catch (Exception ex) {
				MediaCenterDeamonLogger.getLogger().error(ex.getMessage(),ex);
			}
		}
	}
	
	private void sendMsgToUser(String channelId,UserSession user,String msgContent){
//		MessageModel messageModel = new MessageModel();
//		messageModel.setEntId(user.getEntId());
//		messageModel.setAgentId(user.getAgentId());
//		messageModel.setSessionId(user.getSessionId());
//		messageModel.setChannelId(channelId);
//		messageModel.setChannelKey(user.getChannel().getChannelKey());
		
		MessageModel messageModel = user.getMessageModel();
		messageModel.setMsgType("text");
		messageModel.setEvent("System");
		messageModel.setMsgContent(msgContent);
		messageModel.setSender("system");
		String msg = messageModel.toString(RandomKit.randomStr());
		ProducerBroker.sendUserMessage(user.getSessionId(), msg);
		//系统回复语直接入库
		UserSession.getInstance(messageModel.getSessionId()).saveMessage(JSONObject.parseObject(msg),4);
	}
	/**
	 * 发送系统消息给坐席
	 * @param channelId
	 * @param user
	 * @param msgContent
	 */
	private void sendSysMsgToAgent(String channelId,UserSession user,String msgContent){
//		MessageModel messageModel = new MessageModel();
//		messageModel.setEntId(user.getEntId());
//		messageModel.setAgentId(user.getAgentId());
//		messageModel.setSessionId(user.getSessionId());
//		messageModel.setChannelId(channelId);
		MessageModel messageModel = user.getMessageModel();
		messageModel.setMsgType("event");
		messageModel.setEvent("System");
		messageModel.setMsgContent(msgContent);
		messageModel.setUserInfo(new JSONObject());
		messageModel.setSender("system");
		ProducerBroker.sendAgentMessage(user.getAgentId(),  messageModel.toAgentString(RandomKit.randomStr()));
	}
	
	/**
	 * 检查是否有新增或修改技能组
	 * @throws Exception
	 */
	private void checkSkillgroup() throws Exception{
		MediaCenterDeamonLogger.getLogger().info("DaemonThread->run check group ...");
		
		Set<String> dbGroupIds = new HashSet<>();
		String 	sql = "select * from "+getTableName( "CC_SKILL_GROUP")+" where ENT_ID = ?  and BUSI_ORDER_ID = ?";
		try {
			List<JSONObject> rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId,this.busiOrderId },new JSONMapperImpl());
			for (JSONObject row : rows) {
				String groupId = row.getString("SKILL_GROUP_ID");
				dbGroupIds.add(groupId);
				String reloadKey = "RELOAD_SKILLGROUP_"+groupId;
				String skillGroupReloadTime = CacheUtil.get(reloadKey);
				if(StringUtils.isNotBlank(skillGroupReloadTime)){
					CacheUtil.delete(reloadKey);
					MediaCenterDeamonLogger.getLogger().info("skillGroup("+groupId+") 信息已经被更新，执行重新加载技能组信息...");
					SkillGroup skillGroup = skillGroups.get(groupId);
					if(skillGroup!=null) {
						skillGroup.setSkillGroupName(row.getString("SKILL_GROUP_NAME"));
						skillGroup.setPolicy(row.getString("QUEUE_STRATEGY"));
						skillGroup.setIdxOrder(row.getIntValue("IDX_ORDER"));
						skillGroup.reload();
						continue;
					}
				}
				
				if(skillGroups.containsKey(groupId)) continue;
				
				//如果有新增技能组
				MediaCenterDeamonLogger.getLogger().info("新增技能组skillGroup("+groupId+")，执行初始化技能组信息...");
				SkillGroup skillGroup = new SkillGroup();
				skillGroup.setSkillGroupId(groupId);
				skillGroup.setSkillGroupName(row.getString("SKILL_GROUP_NAME"));
				skillGroup.setEntId(entId);
				skillGroup.setPolicy(row.getString("QUEUE_STRATEGY"));
				skillGroup.setLoadStatus("0");
				skillGroup.setIdxOrder(row.getIntValue("IDX_ORDER"));
				skillGroup.reload();
				skillGroup.reloadMemberQueue(getMemberConfig());
				if(skillGroup.getThread()==null) {
					skillGroup.initQueueThread();
				}
				skillGroups.put(groupId,skillGroup);
			}
			//检查是否有删除技能组操作
			Set<SkillGroup> groupSet = new HashSet<>(skillGroups.values());
			for (SkillGroup skillGroup : groupSet) {
				String groupId = skillGroup.getSkillGroupId();
				String delKey = "DELETE_SKILLGROUP_"+groupId;
				String cacheDeleteGroupId = CacheUtil.get(delKey);
				if(StringUtils.isNotBlank(cacheDeleteGroupId)){
					CacheUtil.delete(delKey);
					//技能组下无在线坐席
					if(!skillGroup.hasOnlineAgent()) {
						skillGroups.remove(groupId);
						CacheUtil.delete(delKey);
						MediaCenterDeamonLogger.getLogger().info("skillGroup("+groupId+") 信息已经被删除，移除技能组信息...");
						continue;
					}
					//如果技能组下的坐席有正在服务用户则不删除。
					Set<Agent> onlineAgents = skillGroup.getOnlineAgents();
					boolean hasServiceUser = false;
					for (Agent agent : onlineAgents) {
						if(!agent.getUsers().isEmpty()) {
							hasServiceUser = true;
							break;
						}
					}
					
					if(!hasServiceUser) {
						skillGroups.remove(groupId);
						CacheUtil.delete(delKey);
						MediaCenterDeamonLogger.getLogger().info("skillGroup("+groupId+") 信息已经被删除，移除技能组信息...");
					}
				}
			}
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	
	private void checkChannel() throws Exception{
		MediaCenterDeamonLogger.getLogger().info("DaemonThread->run check channel ...");
		Set<String> dbChannels = new HashSet<>();
		try {
			String sql = "select CHANNEL_ID from CC_CHANNEL where ENT_ID = ?";
			List<EasyRow> rows =  QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId });
			for(EasyRow row:rows){
				String channelId = row.getColumnValue("CHANNEL_ID");
				dbChannels.add(channelId);
				String reloadKey = "RELOAD_CHANNEL_"+channelId;
				String reoadChannelFlag = CacheUtil.get(reloadKey);
				if(StringUtils.isNotBlank(reoadChannelFlag)){
					CacheUtil.delete(reloadKey);
					MediaCenterDeamonLogger.getLogger().info("Channel("+channelId+") 信息已经被更新，执行重新加载渠道信息...");
					Channel channel = channels.get(channelId);
					if(channel!=null) {
						channel.reload();
						continue;
					}
				}
				
				if(channels.containsKey(channelId)) continue;
				//如果有新增渠道
				Channel channel = new Channel();
				channel.setChannelId(row.getColumnValue("CHANNEL_ID")); 
				channel.setChannelName(row.getColumnValue("CHANNEL_NAME"));
				channel.setEntId(this.entId);
				channel.reload();
				channels.put(row.getColumnValue("CHANNEL_ID"), channel);
			} 	
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 企业
	 */
	private void checkEnt() throws Exception{
		//如果缓存里有企业更新的信息，则做更新处理
		String _entId = CacheUtil.get("RELOAD_ENT_NOTIFY_"+entId);
		if(StringUtils.isBlank(_entId)) return;
		//获取到企业信息后，就清空缓存
		CacheUtil.delete("RELOAD_ENT_NOTIFY_"+entId);
	}

	/**
	 * 检查企业黑名单
	 * @param sessionId
	 * @return
	 */
	public boolean checkBlackList(String sessionId) {
		EasyQuery readQuery = QueryFactory.getReadQuery();
//		MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList1:"+System.currentTimeMillis());
		try {
			String dateStr = EasyDate.getCurrentDateString().substring(0,10);
			
			String sq1 = "select count(1) from CC_BLACK_LIST where ent_id=? and LIST_CLASS=? and PHONENUM=? and LIMIT_TIME>=?" ;
			int count1 = readQuery.queryForInt(sq1, new Object[] {entId,4,sessionId,dateStr});
			if(count1>0) {
//				MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList2:"+System.currentTimeMillis());
				return true;
			}
			
			String sql2 = "select PHONENUM,CUST_UID from ywdb.C_CT_CUSTOMER where weixin_no=?";
			List<EasyRow> list1 = readQuery.queryForList(sql2, new Object[] {sessionId});
			if(list1==null||list1.size()==0) {
//				MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList3:"+System.currentTimeMillis());
				return false;
			}
			
			for (EasyRow easyRow : list1) {
				String PHONENUM = easyRow.getColumnValue("PHONENUM");
				String CUST_UID = easyRow.getColumnValue("CUST_UID");
				int i1 = readQuery.queryForInt(sq1, new Object[] {entId,4,PHONENUM,dateStr});
				if(i1>0) {
//					MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList4:"+System.currentTimeMillis());
					return true;
				}
				int i2 = readQuery.queryForInt(sq1, new Object[] {entId,4,CUST_UID,dateStr});
				if(i2>0) {
//					MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList5:"+System.currentTimeMillis());
					return true;
				}
			}
//		String sql = "select count(1) from CC_BLACK_LIST where ent_id=? and LIST_CLASS=?"
//				+ " and (  PHONENUM=? or PHONENUM in (select PHONENUM from ywdb.C_CT_CUSTOMER where  weixin_no=?) or  PHONENUM in (select CUST_UID from ywdb.C_CT_CUSTOMER  where  weixin_no=?)) and LIMIT_TIME>=?" ;
//			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] checkBlackList->"+sessionId+";sql:"+sql);
//			int queryForInt = .queryForInt(sql, new Object[] { entId,4,sessionId,sessionId,sessionId,EasyDate.getCurrentDateString().substring(0,10) });
//			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] checkBlackList->"+sessionId+";sql:"+queryForInt);
//				if(queryForInt>0) {
//					return true;
//				}
			} catch (SQLException e) {
			MediaCenterDeamonLogger.getLogger().error("checkBlackList("+sessionId+") error:",e);
		}
//		MediaCenterLogger.getTimesLogger().info(sessionId+"--->checkBlackList6:"+System.currentTimeMillis());
		return false;
	}
	
	/**
	 * 视频客服配置
	 * @return
	 */
	public JSONObject getVideoConfig() {
		String sql = "select CONFIG FROM ycbusi.CC_MEDIA_CONFIG where ENT_ID=?";
		try {
			String configStr = QueryFactory.getWriteQuery(entId).queryForString(sql, new Object[] {entId});
			return JSONObject.parseObject(configStr);
		} catch (SQLException e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
		return new JSONObject();
	}

	private List<JSONObject> memberConfigList = new ArrayList<>();

	private void reloadMemberConfig() {
		Object o = CacheUtil.get("RELOAD_MEMBER_CONFIG_" + entId);
		if(o==null) {
			return;
		}
		CacheUtil.delete("RELOAD_MEMBER_CONFIG_" + entId);
		EasySQL sql = new EasySQL("select t1.MEMBER_ID,t1.QUEUE_LEVEL from CC_MEDIA_MEMBER_CONFIG t1 order by t1.QUEUE_LEVEL desc,t1.MEMBER_ID desc");
		try {
			memberConfigList = QueryFactory.getWriteQuery(entId).queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}

	}
	public List<JSONObject> getMemberConfig() {
		return memberConfigList;
	}

}
