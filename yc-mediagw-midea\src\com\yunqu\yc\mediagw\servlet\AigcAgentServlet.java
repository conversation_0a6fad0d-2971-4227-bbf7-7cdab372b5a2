package com.yunqu.yc.mediagw.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.AppBaseServlet;
import com.yunqu.yc.mediagw.base.EntContext;
import com.yunqu.yc.mediagw.base.QueryFactory;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.command.impl.GetAgentCommand;
import com.yunqu.yc.mediagw.event.EventObject;
import com.yunqu.yc.mediagw.event.impl.AigcAgentExecutor;
import com.yunqu.yc.mediagw.log.MediagwIntefaceLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.AccessRecord;
import com.yunqu.yc.mediagw.model.ChatMessage;
import com.yunqu.yc.mediagw.model.RequestDataModel;
import com.yunqu.yc.mediagw.model.VisitorModel;
import com.yunqu.yc.mediagw.util.CommonUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * AIGC数字人客服接口
 * 
 * <AUTHOR> Assistant
 */
@WebServlet("/aigcAgent")
public class AigcAgentServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    private static Logger logger = MediagwIntefaceLogger.getAigcAgentLogger();

    @Override
    protected String getResId() {
        return null;
    }

    /**
     * 打开数字人客服页面
     * GET请求：/yc-mediagw/aigcAgent?action=goAigc
     */
    public EasyResult actionForGoAigc() throws Exception {
        long thid = Thread.currentThread().getId();
        JSONObject dataByRequest = getParam();
        // 获取参数
        String channelKey = dataByRequest.getString("channelKey");
        String sessionId = dataByRequest.getString("sessionId");
        String chatSessionId = dataByRequest.getString("chatSessionId");
        String queryString = getRequest().getQueryString();
        logger.info("Thread["+thid+"] 打开数字人客服页面 << "+queryString);
        // 校验必填参数
        if (StringUtils.isAnyBlank(channelKey, sessionId, chatSessionId)) {
            return EasyResult.fail("缺少必填参数！");
        }

        // 校验会话
        EasyResult validateResult = validateSession(channelKey, sessionId, chatSessionId);
        if (validateResult.getState()==0) {
            return validateResult;
        }
        
        // 处理页面跳转
        HttpServletResponse response = this.getResponse();
        
        // 构建跳转URL，这里需要根据实际的数字人客服页面路径调整
        EntContext context = EntContext.getContext(channelKey);
        String aigcAgentUrl = context.getAigcAgentUrl();
        if(StringUtils.isBlank(aigcAgentUrl)) {
            aigcAgentUrl = "/yc-mediagw/aigc-client.jsp";
        }
        if(aigcAgentUrl.contains("?")){
            aigcAgentUrl+="&";
        }else{
            aigcAgentUrl+="?";
        }
        String redirectUrl = aigcAgentUrl + queryString;

        try {
            logger.info("Thread["+thid+"] 打开数字人客服页面 >> "+redirectUrl);
            response.sendRedirect(redirectUrl);
            return null;
            // 对于重定向，返回成功信息
//            JSONObject data = new JSONObject();
//            data.put("redirectUrl", redirectUrl);
//            return EasyResult.ok(data, "页面跳转成功");
        } catch (IOException e) {
            logger.error("Thread["+thid+"] 重定向失败: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return EasyResult.fail("fail!");
        }
    }

    /**
     * AIGC消息同步接口
     * POST请求：/yc-mediagw/aigcAgent?action=pushChatRecord
     */
    public EasyResult actionForPushChatRecord() throws Exception {
        long thid = Thread.currentThread().getId();
        JSONObject dataByRequest = getParam();

        // 获取参数
        String channelKey = dataByRequest.getString("channelKey");
        String sessionId = dataByRequest.getString("sessionId");
        String chatSessionId = dataByRequest.getString("chatSessionId");
        String msgContent = dataByRequest.getString("msgContent");
        String chatId = dataByRequest.getString("chatId");
        String msgType = dataByRequest.getString("msgType");
        Integer sender = dataByRequest.getInteger("sender");

        logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC消息同步请求开始");

        // 校验必填参数
        if (StringUtils.isAnyBlank(channelKey, sessionId, chatSessionId,msgContent)) {
            logger.warn("Thread["+thid+"] sessionId["+sessionId+"] AIGC消息同步：缺少必填参数");
            return EasyResult.fail("参数错误");
        }

        // 校验会话
        EasyResult validateResult = validateSession(channelKey, sessionId, chatSessionId);
        if (validateResult.getState()==0) {
            return validateResult;
        }
        
        try {
            if (StringUtils.isBlank(chatId)) {
                chatId = RandomKit.uniqueStr();
            }
            
            // 设置发送者，默认为用户
            int senderCode = (sender != null) ? sender : 1;

            if(senderCode == 3){
                msgContent = "AIGC客服：" + msgContent;
            }else{
                msgContent = "AIGC访客：" + msgContent;
            }
            // 构建消息数据
            JSONObject messageData = new JSONObject();
            JSONObject data = new JSONObject();
            data.put("sessionId", sessionId);
            data.put("chatSessionId", chatSessionId);
            data.put("msgContent", msgContent);
            data.put("msgType", StringUtils.isBlank(msgType) ? "text" : msgType);
            data.put("readState", 3);//消息已读状态，0：未读，1：已读 3：仅限坐席端显示
            messageData.put("data", data);
            messageData.put("serialId", chatId);
            
            // 保存消息
            ChatMessage.saveMessage(channelKey, messageData, senderCode);

            logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC消息同步成功，serialId："+chatId);
            return EasyResult.ok(null, "消息同步成功");

        } catch (Exception e) {
            logger.error("Thread["+thid+"] sessionId["+sessionId+"] AIGC消息同步失败: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return EasyResult.fail("消息同步失败");
        }
    }

    /**
     * AIGC转人工接口
     * POST请求：/yc-mediagw/aigcAgent?action=toYqAgent
     */
    public EasyResult actionForToYqAgent() throws Exception {
        long thid = Thread.currentThread().getId();
        JSONObject dataByRequest = getParam();

        // 获取参数
        String channelKey = dataByRequest.getString("channelKey");
        String sessionId = dataByRequest.getString("sessionId");
        String chatSessionId = dataByRequest.getString("chatSessionId");
        String unAnswerList = dataByRequest.getString("unAnswerList");
        String summaryTxt = dataByRequest.getString("summaryTxt");

        logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工请求开始："+dataByRequest);

        // 校验必填参数
        if (StringUtils.isAnyBlank(channelKey, sessionId, chatSessionId)) {
            logger.warn("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工：缺少必填参数");
            return EasyResult.fail("参数错误");
        }

        // 校验会话
        EasyResult validateResult = validateSession(channelKey, sessionId, chatSessionId);
        if (validateResult.getState()==0) {
            return validateResult;
        }

        try {
            EventObject eventObject = new EventObject();
            eventObject.setSessionId(sessionId);
            AigcAgentExecutor aigcExecutor = new AigcAgentExecutor(eventObject);
            // 获取访客信息
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if (visitor == null) {
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工：访客信息不存在");
                return EasyResult.fail("会话不存在");
            }

            // 1.总结内容及待回复问题合并一条消息保存到会话聊天记录中
            aigcExecutor.saveSummaryMessage(thid, channelKey, sessionId, chatSessionId, summaryTxt, unAnswerList);

            // 2.更新接入记录
            String dateTime = EasyCalendar.newInstance().getDateTime("-");
            aigcExecutor.updateAccessRecordForRobotEnd(channelKey, chatSessionId, dateTime);
            logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工：更新接入记录成功");

            // 3.通知机器人下线
            CommonUtil.notifyRobotLogout(visitor, "AigcAgentServlet:actionForToYqAgent");
            logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工：通知机器人下线成功");

            // 4.将会话触发转人工
            boolean transferSuccess = aigcExecutor.handleTransferToAgent(thid, visitor, channelKey, sessionId, chatSessionId, dateTime);

            // 5.更新洗悦家跟进记录表，标记转人工（根据转人工结果设置TO_AGENT字段）
            aigcExecutor.updateXyjFlowRecord(channelKey, sessionId, chatSessionId, null, null, null, null,
                              unAnswerList, summaryTxt, transferSuccess);
            logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工：更新洗悦家跟进记录成功，TO_AGENT=" + (transferSuccess ? 1 : 0));

            logger.info("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工成功");
            return EasyResult.ok(null, "转人工成功");

        } catch (Exception e) {
            logger.error("Thread["+thid+"] sessionId["+sessionId+"] AIGC转人工失败: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return EasyResult.fail("操作失败");
        }
    }

    /**
     * 意向跟进提交接口
     * POST请求：/yc-mediagw/aigcAgent?action=xyjFlow
     */
    public EasyResult actionForXyjFlow() throws Exception {
        long thid = Thread.currentThread().getId();
        JSONObject dataByRequest = getParam();

        // 获取参数
        String channelKey = dataByRequest.getString("channelKey");
        String sessionId = dataByRequest.getString("sessionId");
        String chatSessionId = dataByRequest.getString("chatSessionId");
        String dataMobile = dataByRequest.getString("data_mobile");
        String dataBrand = dataByRequest.getString("data_brand");
        String dataAddr = dataByRequest.getString("data_addr");
        String dataContactName = dataByRequest.getString("data_contactName");

        logger.info("Thread["+thid+"] sessionId["+sessionId+"] 意向跟进提交请求开始");

        // 校验必填参数
        if (StringUtils.isAnyBlank(channelKey, sessionId, chatSessionId)) {
            logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 意向跟进提交：缺少必填参数");
            return EasyResult.fail("参数错误");
        }

        // 校验会话
        EasyResult validateResult = validateSession(channelKey, sessionId, chatSessionId);
        if (validateResult.getState()==0) {
            return validateResult;
        }

        try {
            EventObject eventObject = new EventObject();
            eventObject.setSessionId(sessionId);
            AigcAgentExecutor aigcExecutor = new AigcAgentExecutor(eventObject);
            // 更新洗悦家跟进记录表
            aigcExecutor.updateXyjFlowRecord(channelKey, sessionId, chatSessionId, dataMobile, dataBrand,
                              dataAddr, dataContactName, null, null, false);

            logger.info("Thread["+thid+"] sessionId["+sessionId+"] 意向跟进提交成功");
            return EasyResult.ok(null, "意向跟进提交成功");

        } catch (Exception e) {
            logger.error("Thread["+thid+"] sessionId["+sessionId+"] 意向跟进提交失败: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return EasyResult.fail("操作失败");
        }
    }

    /**
     * 校验会话是否存在且有效
     */
    private EasyResult validateSession(String channelKey, String sessionId, String chatSessionId) {
        long thid = Thread.currentThread().getId();

        try {
            EntContext context = EntContext.getContext(channelKey);
            if(!context.aigcAgentSwitch()){
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 会话校验：当前渠道未开启数字人客服");
                return EasyResult.fail("会话校验：当前渠道未开启数字人客服!");
//                return EasyResult.fail("validateSession fail!");
            }

            VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);

            if (visitorModel == null) {
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 会话校验：会话不存在");
                return EasyResult.fail("会话校验：会话不存在!");
//                return EasyResult.fail("validateSession fail!");
            }

            if (!StringUtils.equals(channelKey, visitorModel.getChannelKey())) {
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 会话校验：渠道不匹配");
                return EasyResult.fail("会话校验：渠道不匹配!");
//                return EasyResult.fail("validateSession fail!");
            }
            
            // 校验chatSessionId是否匹配
            if (!StringUtils.equals(chatSessionId, visitorModel.getChatSessionId())) {
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 会话校验：会话ID不匹配");
                return EasyResult.fail("会话校验：会话ID不匹配!");
//                return EasyResult.fail("validateSession fail!");
            }

            // 校验会话状态是否有效
            String bizType = visitorModel.getBizType();
            if (!"robot".equals(bizType)) {
                logger.warn("Thread["+thid+"] sessionId["+sessionId+"] 会话校验：会话状态无效，当前状态："+visitorModel.getBizType());
                return EasyResult.fail("会话校验：会话状态无效，当前状态："+visitorModel.getBizType());

//                return EasyResult.fail("validateSession fail!");
            }

            return EasyResult.ok(null, "会话校验通过");

        } catch (Exception e) {
            logger.error("Thread["+thid+"] sessionId["+sessionId+"] 会话校验失败: " + e.getMessage());
            logger.error(e.getMessage(), e);
            return EasyResult.fail("会话校验失败: " + e.getMessage());

//            return EasyResult.fail("validateSession error!!");
        }
    }


}
