package com.yunqu.yc.mediagw.servlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.JSONPath;
import com.yunqu.yc.mediagw.base.*;
import com.yunqu.yc.mediagw.event.impl.SQLExecutor;
import com.yunqu.yc.mediagw.event.impl.UserEventDispatcher;
import com.yunqu.yc.mediagw.http.Proxy;
import com.yunqu.yc.mediagw.log.MediagwIntefaceLogger;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.message.ServerMessage;
import com.yunqu.yc.mediagw.model.*;
import com.yunqu.yc.mediagw.mqclient.ProducerBroker;
import com.yunqu.yc.mediagw.service.CommandService;
import com.yunqu.yc.mediagw.util.*;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.context.ServiceContext;
import org.easitline.common.core.resource.ServiceResource;
import org.easitline.common.core.service.IService;
import org.easitline.common.core.service.ServiceRegistor;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.core.web.render.Render;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRowMapper;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.db.impl.MapRowMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.crypt.BASE64Util;
import org.easitline.common.utils.crypt.MD5Util;
import org.easitline.common.utils.kit.MapKit;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.WebServlet;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileOutputStream;
import java.net.URLDecoder;
import java.sql.SQLException;
import java.util.*;

/**
 * 全媒体网关接口
 *
 * <AUTHOR>
 */

@WebServlet({"/mediaApi/*"})
public class MediaEventServlet extends AppBaseServlet {

    private static final long serialVersionUID = 1L;

    /**
     * 获得客户的历史会话
     *
     * @return
     */
    public EasyResult actionForCustHistChatList() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"sessionId", "channelKey"});
            String entId = getEntId();
            String sessionId = param.getString("sessionId");
//			String sql = "select  * from "+this.getTableName(entId,"CC_MEDIA_RECORD") +" where  SESSION_ID = ?  and ENT_ID = ? and SERVER_STATE = 3 order by  BEGIN_TIME desc ";
            String sql = "select  * from " + this.getTableName(entId, "CC_MEDIA_RECORD") + " where  SESSION_ID = ?  and ENT_ID = ? order by  BEGIN_TIME desc ";
            List<SessionModel> list = this.getQuery().queryForList(sql, new Object[]{sessionId, this.getEntId()}, 1, 50, new SessionRowMapper());
            result.setData(list);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error("MediaEventServlet.actionForCustHistChatList() error,cause:" + e.getMessage(), e);
            result.addFail("获取客户的历史聊天会话失败！");
        }
        return result;
    }

    /**
     * 获取用户聊天记录
     *
     * @return EasyResult；
     */
    public EasyResult actionForLoadChatRecord() {
        long startTime = System.currentTimeMillis();

        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"sessionId", "channelKey"});
            JSONObject resultJson = new JSONObject();

//			String msgTime = param.getString("msgTime");
            String entId = getEntId();
            String sessionId = param.getString("sessionId");
            String chatSessionId = param.getString("chatSessionId");
            String channelKey = param.getString("channelKey");
            int pageIndex = param.getIntValue("pageIndex");
            long tid = Thread.currentThread().getId();
//            MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() << channelKey[" + channelKey + "] sessionId[" + sessionId + "] chatSessionId[" + chatSessionId + "] start！");

            //消息引用逻辑：聊天记录中增加返回引用消息（数据来源：CC_MEDIA_CHAT_RECORD_EXT.QUOTE_DATA）
            EasySQL sql = new EasySQL();
                sql.append(" select t1.CHAT_ID ,t1.ENT_ID ,t1.DATE_ID ,t1.MSG_TIME ,t1.CHAT_SESSION_ID ,t1.CUST_SESSION_ID ,t1.SENDER ,t1.MSG_TYPE ,t1.MSG_CONTENT ," +
                        "t1.MSG_TIMESTAMP ,t1.ROBOT_DATA,t1.TEMP_CONFIG,t1.SEND_STATE,t1.SEND_FIAL_DESC,t1.READ_STATE,t1.EVALUATION_FLAG,t1.EVALUATION_DATA,");
                sql.append(" t2.AGENT_ID,t2.AGENT_NAME,t2.AGENT_PHONE,t2.AGENT_NICK_NAME,t2.CHANNEL_ID,t2.CHANNEL_KEY,t2.CHANNEL_NAME,t2.CHANNEL_TYPE");
                sql.append(" ,t3.QUOTE_DATA,t3.DATA1,t3.DATA2,t3.DATA3,t3.DATA4,t3.DATA5");
                sql.append(" from ");
                sql.append(getTableName(entId, "CC_MEDIA_CHAT_RECORD t1 "));
                sql.append(" left join " + getTableName(entId, "CC_MEDIA_RECORD t2 on t1.CHAT_SESSION_ID = t2.SERIAL_ID"));
                sql.append(" left join " + getTableName(entId, "CC_MEDIA_CHAT_RECORD_EXT t3 on t1.CHAT_ID = t3.CHAT_ID"));
                sql.append(" where 1=1");
                sql.append(entId, " and t1.ENT_ID = ?");
                sql.append(sessionId, " and (t1.CUST_SESSION_ID = ? ");
                sql.append(sessionId, " or t1.CUST_SESSION_ID = ? ||'_h5')");
                sql.append(chatSessionId, " and t1.CHAT_SESSION_ID = ?");
                EasyCalendar cal = EasyCalendar.newInstance();
                if(pageIndex<=1){
                    cal.add(EasyCalendar.MONTH, -1);
                }else{
                    cal.add(EasyCalendar.MONTH, -6);
                }
                sql.append(cal.getDateTime("-")," and t1.MSG_TIME >= ? ");
                sql.append(" and t1.SENDER <> 5 ");//不返回 5坐席事件
                sql.append(" and t1.WITHDRAW<>1 ");//不返回 已撤回的消息
                sql.append(" and t1.READ_STATE <> 3 ");//不返回 数字人客服消息
                sql.append(" order by t1.CUST_SESSION_ID DESC,t1.MSG_TIME DESC");
            List<?> list = this.queryRecordPageList(sql.getSQL(), sql.getParams(), new ChatMessageRowMapper(), param);
            if (list != null && !list.isEmpty()) {
                VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
                String currentSessionId = null;
                if(visitor!=null){
                    currentSessionId = visitor.getChatSessionId();
                }

                SessionNotifyEvents sessionNotify = SessionNotifyEvents.getInstance();
                List<ChatMessageModel> _list = new ArrayList<>();
                String eventKey = SessionNotifyEvents.getEventKey(channelKey, sessionId);
                String dbName = getSchema(entId);

                String formatStr = "yyyy-MM-dd HH:mm:ss";
                //返回给前端：降序改成升序
                for (int i = (list.size() - 1); i >= 0; i--) {
                    ChatMessageModel msgModel = (ChatMessageModel) list.get(i);
                    _list.add(msgModel);
                    String serialId = msgModel.getSerialId();
                    //读取缓存中的超长内容 begin
                    String robotData = msgModel.getRobotData();
                    if(StringUtils.isNotBlank(robotData) && robotData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
                        String str1 = CacheLongTxtUtil.get(dbName, serialId, "ROBOT_DATA",robotData);
                        if(StringUtils.isNotBlank(str1)){
                            //移除"welcomeCardList"
                            JSONObject robotDataObj = JSONObject.parseObject(str1);
                            robotDataObj.remove("welcomeCardList");
                            //最新一条需要返回"bottomNavList"，其他消息不需要
                            if(i != (list.size() - 1)) robotDataObj.remove("bottomNavList");
                            msgModel.setRobotData(robotDataObj.toJSONString());
                        }
                    }
                    String msgContent = msgModel.getMsgContent();
                    if(StringUtils.isNotBlank(msgContent) && msgContent.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
                        String str2 = CacheLongTxtUtil.get(dbName, serialId, "MSG_CONTENT",msgContent);
                        if(StringUtils.isNotBlank(str2)){
                            msgModel.setMsgContent(str2);
                        }
                    }
                    String tempConfig = msgModel.getTempConfig();
                    if(StringUtils.isNotBlank(tempConfig) && tempConfig.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
                        String str3 = CacheLongTxtUtil.get(dbName, serialId, "TEMP_CONFIG",tempConfig);
                        if(StringUtils.isNotBlank(str3)){
                            msgModel.setTempConfig(str3);
                        }
                    }
                    //消息引用逻辑：读取被引用消息的超长文本内容
                    String quoteData = msgModel.getQuoteData();
                    if(StringUtils.isNotBlank(quoteData) && quoteData.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
                        String str4 = CacheLongTxtUtil.get(dbName, serialId, "QUOTE_DATA",quoteData);
                        if(StringUtils.isNotBlank(str4)){
                            msgModel.setQuoteData(str4);
                        }
                    }

                    //读取缓存中的超长内容 end

                    //部分留言回复内容消息MSG_TIMESTAMP字段是空的，这里必须要保持timestamp不等于0，不然会影响冒泡排序
                    long timestamp = msgModel.getTimestamp();
                    String msgTime = msgModel.getMsgTime();
                    if(timestamp==0 && StringUtils.isNotBlank(msgTime)){
                        EasyCalendar msgTimeCal = EasyCalendar.newInstance(msgTime, formatStr);
                        msgModel.setTimestamp(msgTimeCal.getTimeInMillis());
                    }

                    //历史会话的未评价机器人消息不需要显示点赞/点踩
                    if(msgModel.getEvalFlag()==2 && "robot".equals(msgModel.getSender()) && !StringUtils.equals(currentSessionId,msgModel.getChatSessionId())){
                        msgModel.setEvalFlag(3);
                    }

                    //清理待接收的消息
                    if ("user".equals(msgModel.getSender())) {
                        continue;
                    }
                    if (msgModel.getReadState() == 1) {
                        continue;
                    }
                    JSONObject msgObj = sessionNotify.clearMsgBymsgId(eventKey, serialId);
                    if (msgObj != null) {
                        MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() 清理内存中的消息 >> channelKey[" + channelKey + "] sessionId[" + sessionId + "] chatSessionId[" + chatSessionId + "] serialId[" + serialId + "]");
                    } else {
                        msgObj = new JSONObject();
                        msgObj.put("agentId", msgModel.getAgentId());
                        msgObj.put("serialId", msgModel.getSerialId());
                        msgObj.put("timestamp", timestamp);
                        msgObj.put("data", MapKit.create()
                                .set("serialId", msgModel.getSerialId())
                                .set("chatSessionId", msgModel.getChatSessionId())
                                .set("sessionId", msgModel.getSessionId())
                                .set("agentId", msgModel.getAgentId()));
                    }
                    //发送消息已读事件
                    ServerMessage.replyReadResult(msgObj);
                }

                // 冒泡排序，根据 timestamp 排序，越小放越前面
                for (int j = 0; j < _list.size() - 1; j++) {
                    for (int k = 0; k < _list.size() - 1 - j; k++) {
                        if (_list.get(k).getTimestamp() > _list.get(k + 1).getTimestamp()) {
                            // 交换位置
                            ChatMessageModel temp = _list.get(k);
//                            MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() >> channelKey[" + channelKey + "] sessionId[" + sessionId + "] chatSessionId[" + chatSessionId + "] 触发冒泡排序，"+temp.getSerialId()+" <==> "+_list.get(k + 1).getSerialId());
                            _list.set(k, _list.get(k + 1));
                            _list.set(k + 1, temp);
                        }
                    }
                }
                resultJson.put("pageIndex", pageIndex);
                resultJson.put("data", _list);
                resultJson.put("pageType", 1);
            }
            MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() >> channelKey[" + channelKey + "] sessionId[" + sessionId + "] chatSessionId[" + chatSessionId + "]  finish, time:" + (System.currentTimeMillis() - startTime) + " ms");
            result.setData(resultJson);
            CacheUtil.delete("agentToCustMsg_" + sessionId);//坐席最后发送消息时间
        } catch (Exception e) {
            result.addFail("获取用户聊天记录失败！");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }


    /**
     * 获取渠道按键信息
     *
     * @return EasyResult；
     */
    public EasyResult actionForGetChannelKeys() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey"});
            String channelKey = param.getString("channelKey");
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                throw new Exception("未找到channelKey=" + channelKey + "的渠道信息");
            }

            List<JSONObject> channelKeys = context.getChannelKeys();
            List<JSONObject> newList = new ArrayList<>();
            if (channelKeys != null) {
                for (JSONObject keyObj : channelKeys) {
                    keyObj.remove("CREATE_TIME");
                    keyObj.remove("CREATOR");
                    keyObj.remove("ENT_ID");
                    keyObj.remove("CHANNEL_ID");
                    newList.add(keyObj);
                }
            }
            JSONObject config = new JSONObject();
            config.put("version", "v2");
            config.put("keys", newList);
            config.put("title", context.getH5KeyListTitle());
            result.setData(config);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 客户端输入引导接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForRobotInputGuide() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey", "question", "sessionId"});

            String serviceId = MediaConstants.ROBOTGW_SERVICE;
            IService service = ServiceContext.getService(serviceId);

            if (service == null) {
                result.addFail("未找到接口服务[" + serviceId + "]");
                return result;
            }
            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            EntContext entContext = EntContext.getContext(channelKey);
            if (StringUtils.isBlank(entContext.getChannelId())) {
                MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 渠道信息不存在！");
                return result;
            }

            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if (visitor == null) {
                MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 会话信息不存在，请先接入会话！");
                return result;
            }
//			if(!StringUtils.equalsIgnoreCase(visitor.getBizType(),"robot")){
//				MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey["+channelKey+"]，sessionId["+sessionId+"] 当前会话状态["+visitor.getBizType()+"]不是[robot]，不允许使用引导功能！");
//				return result;
//			}

            boolean checkUserTag = entContext.checkUserTag(visitor);
            if (entContext.checkInWhite(sessionId) || !entContext.isOpenRobot() || checkUserTag) {
//                MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不调用机器人的输入引导接口：1.白名单用户(" + entContext.checkInWhite(sessionId) + ")；" +
//                        "2.渠道关闭机器人时(" + entContext.isOpenRobot() + ")；3.用户标签满足渠道转人工标签(" + checkUserTag + ")。");
                result.addFail("当前用户限制使用该功能");
                return result;
            }

            JSONObject styleConf = entContext.getStyleConf();
            //机器人会话智能联想（转人工前）
            int h5InputASwitch = styleConf.getIntValue("H5_INPUT_A_SWITCH");
            if ("robot".equals(visitor.getBizType()) && h5InputASwitch == 0) {
                MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不调用机器人的输入引导接口：当前用户处于机器人流程状态，渠道已关闭“机器人会话智能联想（转人工前）。");
                result.addFail("当前用户处于机器人流程状态，渠道已关闭“机器人会话智能联想（转人工前）”");
                return result;
            }

            //人工会话智能联想（转人工后）
            int h5InputBSwitch = styleConf.getIntValue("H5_INPUT_B_SWITCH");
            if (("queue".equals(visitor.getBizType()) || "zxkf".equals(visitor.getBizType())) && h5InputBSwitch == 0) {
                MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不调用机器人的输入引导接口：当前用户处于人工会话状态，渠道已关闭“人工会话中智能联想（转人工后）。");
                result.addFail("当前用户处于人工会话状态，渠道已关闭“人工会话中智能联想（转人工后）”");
                return result;
            }

            //是否特殊白名单
            try {
                JSONObject jsonObject = QueryFactory.getQuery().queryForRow("select * from CC_H5_ROBOT_WHITE_LIST where CUST_ID=? and CHANNEL_ID = ? and STATUS = 1", new Object[]{sessionId, entContext.getChannelId()}, new JSONMapperImpl());

                if (jsonObject != null && !jsonObject.isEmpty()) {
                    String loseTime = jsonObject.getString("LOSE_TIME");
                    //失效时间为空 或者 当前时间小于失效时间
                    if (StringUtils.isBlank(loseTime) || !EasyDate.getCurrentDate().after(EasyDate.stringToDate("yyyy-MM-dd HH:mm:ss", loseTime))) {
                        MediagwIntefaceLogger.getLogger().warn("客户端输入引导接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 不调用机器人的输入引导接口：当前用户属于输入引导白名单。");
                        result.addFail("当前用户属于输入引导白名单");
                        return result;
                    }
                }
            } catch (Exception ignored) {
            }


            JSONObject userInfo = visitor.getUserInfo();
            String entId = getEntId();
            //所有接口服务公共参数
            param.put("entId", entId);
            param.put("serialId", param.getString("msgId"));
            param.put("timestamp", System.currentTimeMillis());
            param.put("command", "robotInputGuide");
            param.put("clientId", sessionId);
            param.put("keyCode", userInfo.getString("keyCode"));
            param.put("mobile", userInfo.getString("mobile"));
            param.put("levelName", userInfo.getString("levelName"));
            param.put("nickname", userInfo.getString("nickname"));

            JSONObject invoke = service.invoke(param);
            result.setData(invoke);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }

        return result;
    }

    /**
     * 坐席咨询机器人接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForRobotAgentAsk() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey", "question", "userAcc"});

            String serviceId = MediaConstants.ROBOTGW_SERVICE;
            IService service = ServiceContext.getService(serviceId);

            if (service == null) {
                result.addFail("未找到接口服务[" + serviceId + "]");
                return result;
            }

            String entId = getEntId();
            param.put("entId", entId);
            param.put("serialId", param.getString("msgId"));
            param.put("timestamp", System.currentTimeMillis());
            param.put("clientId", param.getString("userAcc"));
            param.put("command", "robotAgentAsk");

            JSONObject invoke = service.invoke(param);
            result.setData(invoke);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 常用语查询接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForSrhPhrase() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey", "keyword", "userAcc"});

            String sender = "01";//接口标识
            String password = "YQ_85521717";//接入密码,由业务平台在配置管理模块统一分配,默认为YQ_85521717
            String serialId = RandomKit.uniqueStr();//请求序列号，可以为空
            String busiType = "01";//业务类型 01-在线客服敏感词
            String type = param.getString("type");//类型 01-公共常用语 02-个人常用语
            String channelKey = param.getString("channelKey");//渠道号码，云呼里的channelKey对应；不能为空
            String channelType = param.getString("channelType");//渠道：按机器人需要的渠道传入：1 网页， 2 微信， 3 微博， 5APP
            if (StringUtils.isBlank(channelType)) channelType = "1";
            String keyword = param.getString("keyword");//搜索关键字,关键字长度超过50后，会默认按最后50-2个字符搜索，该值以默认配置值为准
            String userAcc = param.getString("userAcc");//调用的坐席用户账号
            String entId = getEntId();

            String serviceId = "AGENTCONFIG_INTERFACE";

            IService service = ServiceContext.getService(serviceId);
            if (service == null) {
                result.addFail("未找到接口服务[" + serviceId + "]");
                return result;
            }

            param.put("command", "agentConfigSrhPhrase");
            param.put("serviceId", serviceId);
            param.put("sender", sender);
            param.put("password", password);
            param.put("serialId", serialId);
            param.put("busiType", busiType);
            param.put("type", type);
            param.put("channelKey", channelKey);
            param.put("channelType", channelType);
            param.put("keyword", keyword);
            param.put("userAcc", userAcc);
            param.put("entId", entId);
            //2.5.1#20200206-1
            param.put("busiOrderId", param.getString("busiOrderId"));
            param.put("skillGroupId", param.getString("skillGroupId"));

            JSONObject invoke = service.invoke(param);

            result.setData(invoke);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 检测敏感词接口
     *
     * @return EasyResult；
     */
    public void actionForSrhSensitive() {
        JSONObject data = new JSONObject();
        data.put("serialId", "");
        data.put("respCode", "000");//处理结果 000：成功
        data.put("respDesc", "成功");
        data.put("total", "0");
        data.put("sensitiveWords", "");
        data.put("msg", "");
        data.put("code", "200");
        Render.renderText(getRequest(), getResponse(), data.toJSONString());
    }

    /**
     * 常用语使用接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForUserPhrase() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey", "id", "userAcc"});

            //敏感词过滤开关
            String srhSensitive = Constants.getContext().getProperty("SRHSENSITIVE", "false");
            if ("false".equals(srhSensitive)) {
                return result;
            }

            String sender = "01";//接口标识
            String password = "YQ_85521717";//接入密码,由业务平台在配置管理模块统一分配,默认为YQ_85521717
            String serialId = RandomKit.uniqueStr();//请求序列号，可以为空
            String busiType = "01";//业务类型 01-在线客服敏感词
            String type = "01";//类型 01-公共常用语 02-个人常用语
            String id = param.getString("id");//常用语id
            String channelKey = param.getString("channelKey");//渠道号码，云呼里的channelKey对应；不能为空
            String channelType = param.getString("channelType");//渠道：按机器人需要的渠道传入：1 网页， 2 微信， 3 微博， 5APP
            if (StringUtils.isBlank(channelType)) channelType = "1";
            String userAcc = param.getString("userAcc");//使用者用户账号
            String groupId = param.getString("groupId");//使用者技能组
            String entId = getEntId();

            String serviceId = "AGENTCONFIG_INTERFACE";

            IService service = ServiceContext.getService(serviceId);
            if (service == null) {
                result.addFail("未找到接口服务[" + serviceId + "]");
                return result;
            }
            param.put("command", "agentConfigUsePhrase");
            param.put("serviceId", serviceId);
            param.put("sender", sender);
            param.put("password", password);
            param.put("serialId", serialId);
            param.put("busiType", busiType);
            param.put("type", type);
            param.put("channelKey", channelKey);
            param.put("channelType", channelType);
            param.put("id", id);
            param.put("userAcc", userAcc);
            param.put("groupId", groupId);
            param.put("entId", entId);
            param.put("busiOrderId", param.getString("busiOrderId"));
            param.put("skillGroupId", param.getString("skillGroupId"));
            JSONObject invoke = service.invoke(param);

            result.setData(invoke);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取坐席侧边栏菜单
     *
     * @return EasyResult；
     */
    public EasyResult actionForAgentOpMenu() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            //从各个门户搜集全媒体的菜单集合，比如企业呼叫中心，从cc-workbench里取
            JSONArray array = new JSONArray();
            List<String> serviceIds = ServiceContext.findByPrefix("YC_MEDIA_OP_MENU_");
            for (String serviceId : serviceIds) {
                try {
                    IService service = ServiceContext.getService(serviceId);
                    JSONObject json = new JSONObject();
                    json.put("msgId", param.getString("msgId"));
                    json.put("serialId", param.getString("msgId"));
                    json.put("sender", "yc-mediagw");
                    json.put("serviceId", serviceId);
                    json.put("command", "opMenu");
                    if (param != null) {
                        json.put("entId", param.getString("entId"));
                        json.put("userAcc", param.getString("userAcc"));
                        json.put("busiOrderId", param.getString("busiOrderId"));
                        json.put("skillGroupId", param.getString("skillGroupId"));
                    }

                    JSONObject rs = service.invoke(json);
                    if (rs != null && rs.getJSONArray("data") != null) {
                        array.addAll(rs.getJSONArray("data"));
                    }
                } catch (Exception e) {
                    MediagwIntefaceLogger.getLogger().error("<agentOpMenu> ->invoke serviceId >> error:" + e.getMessage(), e);
                }
            }
            result.setData(array);
        } catch (Exception e) {
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * 生成并发送邀请码接口
     * 生成邀请码并通过短信发送给用户，用户在全媒体渠道输入并发送邀请码可接入指定坐席
     *
     * @return EasyResult；
     */
    public EasyResult actionForGenerateCode() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"entId", "busiOrderId", "agentId", "phone", "channelKey"});

            String channelKey = param.getString("channelKey");
            String entId = param.getString("entId");
            String busiOrderId = param.getString("busiOrderId");
            String agentId = param.getString("agentId");//坐席账号，如：8001@1000
            String phone = param.getString("phone");

            EntContext context = EntContext.getContext(channelKey);
            JSONObject channelAutoConf = context.getChannelAutoConf();
            //客服#agentNickName#邀请你在线聊天，请点击链接http://172.16.64.221:9060/yc-media/pages/web-chat.jsp?channelKey=#channelKey#&message=#CODE_MSG#
            String smsContent = channelAutoConf.getString("INVITE_SMS_MSG");
            if (StringUtils.isBlank(smsContent)) {
                MediagwIntefaceLogger.getLogger().warn("<GenerateCode>  >> 渠道[" + channelKey + "]未配置短信模板，无法发送短信");
                result.addFail("渠道[" + channelKey + "]未配置短信模板，无法发送短信");
                return result;
            }

            //生成随机码
            int time = channelAutoConf.getIntValue("INVITATION_CODE_TIMEOUT");
            time = time <= 0 ? 30 : time;
            int hour = time / 60;
            int minute = time % 60;
            String timestr = "";
            if (hour > 0) {
                timestr += hour + "小时";
            }
            if (minute > 0) {
                timestr += minute + "分钟";
            }

            String code = RandomKit.smsAuthCode(6);
            CacheManager.getMemcache().put(code, agentId, time * 60);
            MediagwIntefaceLogger.getLogger().info("<GenerateCode>  >> code:" + code);

            String agentNickName = agentId.indexOf("@") > -1 ? agentId.substring(0, agentId.indexOf("@")) : agentId;
            String sql = "select t1.EXT_CONF"
                    + "	from " + context.getTableName("CC_BUSI_USER") + " t1 , CC_USER t2  "
                    + " where t1.USER_ID = t2.USER_ID and t1.ENT_ID = ? and t1.BUSI_ORDER_ID = ? and t2.USER_ACCT = ?";
            String extConf = getQuery().queryForString(sql, new Object[]{context.getEntId(), busiOrderId, agentId});
            if (StringUtils.isNoneBlank(extConf)) {
                JSONObject extConfJson = JSONObject.parseObject(extConf);
                agentNickName = extConfJson.getString("NICK_NAME");
            }
            String codeMsg = "{\"content\":\"#code#\",\"msgType\":\"text\"}";
            codeMsg = BASE64Util.encode(codeMsg.replace("#code#", code));
            smsContent = smsContent.replace("#agentNickName#", agentNickName).replace("#channelKey#", channelKey).replace("#time#", timestr).replace("#CODE_MSG#", codeMsg);

            JSONObject receiver = new JSONObject();
            receiver.put("receiver", phone);
            receiver.put("content", smsContent);
            receiver.put("userType", "2");//用户类型：1-系统用户，2自定义用户
            JSONArray receivers = new JSONArray();
            receivers.add(receiver);

            JSONObject jsonObject = new JSONObject();
            jsonObject.put("msgId", param.getString("msgId"));
            jsonObject.put("sender", "yc-mediagw");
            jsonObject.put("password", "YQ_85521717");
            jsonObject.put("serialId", RandomKit.randomStr());
            jsonObject.put("serviceId", "SMSGW_INTEFACE");
            jsonObject.put("command", "sendMessage");
            jsonObject.put("source", "1");
            jsonObject.put("busiId", "yc-mediagw");
            jsonObject.put("userAcc", agentId);
            jsonObject.put("receivers", receivers.toJSONString());
            jsonObject.put("schema", context.getSchemaId());
            jsonObject.put("epCode", entId);
            jsonObject.put("busiOrderId", busiOrderId);
            jsonObject.put("chanelId", "");
            jsonObject.put("model", "");
            jsonObject.put("category", "");
            jsonObject.put("sendTime", "");
            MediagwIntefaceLogger.getLogger().info("<GenerateCode> <SMSGW_INTEFACE> SMSCONTENT >> " + jsonObject.toJSONString());
            IService service = ServiceContext.getService("SMSGW_INTEFACE");
            if (service != null) {
                MediagwIntefaceLogger.getLogger().debug("<GenerateCode> <SMSGW_INTEFACE>  >> " + jsonObject.toJSONString());
                JSONObject invoke = service.invoke(jsonObject);
                MediagwIntefaceLogger.getLogger().debug("<GenerateCode> <SMSGW_INTEFACE>  << " + invoke);
            }

            //响应
            result.put("code", code);
            result.setData(code);
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取机器人多伦对话流程列表
     */
    public EasyResult actionForGetRobotFlow() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"channelKey"});
            IService service = ServiceContext.getService(MediaConstants.ROBOTGW_SERVICE);
            if (service != null) {
                param.put("serviceId", MediaConstants.ROBOTGW_SERVICE);
                param.put("command", "robotServiceFlow");
                MediagwIntefaceLogger.getLogger().debug("<GetRobotFlow> <ROBOTGW_INTERFACE>  >> " + param.toJSONString());
                JSONObject invoke = service.invoke(param);
                MediagwIntefaceLogger.getLogger().debug("<GetRobotFlow> <ROBOTGW_INTERFACE>  << " + invoke);
                result.setData(invoke);
            }
        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取坐席所属渠道列表
     *
     * @return EasyResult；
     */
    public EasyResult actionForGetMyChannels() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            checkParam(new String[]{"entId", "agentId", "busiOrderId"});
            String entId = param.getString("entId");
            String busiOrderId = param.getString("busiOrderId");
            String agentId = param.getString("agentId");//坐席账号，如：8001@1000
            //查询坐席所属技能组
            EasySQL sql = new EasySQL("SELECT DISTINCT t4.CHANNEL_ID,t4.CHANNEL_KEY,t4.CHANNEL_NAME,t4.CHANNEL_STATE,t4.CHANNEL_TYPE");
            sql.append(" FROM " + getTableName(entId, "cc_skill_group_user") + " t1");
            sql.append(" LEFT JOIN cc_user t2 ON t1.USER_ID = t2.USER_ID");
            sql.append(" LEFT JOIN " + getTableName(entId, "cc_skill_group_channel") + " t3 ON t1.SKILL_GROUP_ID = t3.SKILL_GROUP_ID");
            sql.append(" LEFT JOIN cc_channel t4 ON t3.CHANNEL_ID = t4.CHANNEL_ID");
            sql.append(" WHERE 1=1");
            sql.append(entId, " AND t1.ENT_ID = ?");
            sql.append(busiOrderId, " AND t1.BUSI_ORDER_ID = ?");
            sql.append(agentId, " AND t2.USER_ACCT = ?");
            sql.append(" AND t3.CHANNEL_ID IS NOT NULL");
            sql.append(" AND t4.CHANNEL_STATE = 0");
            List<JSONObject> channels = QueryFactory.getQuery(entId).queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            if (channels == null || channels.size() == 0) {
                sql = new EasySQL("SELECT DISTINCT t5.CHANNEL_ID,t5.CHANNEL_KEY,t5.CHANNEL_NAME,t5.CHANNEL_STATE,t5.CHANNEL_TYPE");
                sql.append(" FROM " + getTableName(entId, "cc_skill_group_user t1"));
                sql.append(" LEFT JOIN cc_user t2 ON t1.USER_ID = t2.USER_ID");
                sql.append(" LEFT JOIN " + getTableName(entId, "CC_SKILL_QUEUE_GROUP t3") + " ON t1.SKILL_GROUP_ID = t3.SKILL_GROUP_ID");
                sql.append(" LEFT JOIN CC_CHANNEL_KEY t4 ON t3.QUEUE_ID = t4.QUEUE_ID");
                sql.append(" LEFT JOIN CC_CHANNEL t5 ON t4.CHANNEL_ID = t5.CHANNEL_ID");
                sql.append(" WHERE 1=1");
                sql.append(entId, " AND t1.ENT_ID = ?");
                sql.append(busiOrderId, " AND t1.BUSI_ORDER_ID = ?");
                sql.append(agentId, " AND t2.USER_ACCT = ?");
                sql.append(" AND t3.QUEUE_ID IS NOT NULL");
                sql.append(" AND t5.CHANNEL_STATE = 0");
                channels = QueryFactory.getQuery(entId).queryForList(sql.getSQL(), sql.getParams(), new JSONMapperImpl());
            }
            result.setData(channels);

        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    public EasyResult actionForCloseLongPolling() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        String sessionId = param.getString("sessionId");
        String _msgId = param.getString("_msgId");
        MediagwIntefaceLogger.getLogger().info("<CloseLongPolling> ->>>客户端主动关闭长连接，sessionId：" + sessionId + "，_msgId：" + _msgId);
        try {
            checkParam(new String[]{"sessionId"});

            String key = MediaConstants.LONG_POLLING_CLOSE_CACHE + sessionId;
            CacheUtil.put(key, sessionId, 3);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 获取渠道信息，禁止返回CHANNEL_CONF中的涉密信息。
     *
     * @return
     */
    public EasyResult actionForChannelConfig() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().debug("<actionForChannelConfig> << " + param);

        try {
            checkParam(new String[]{"channelKey", "configList"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            JSONArray configList = param.getJSONArray("configList");//配置
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }

            JSONObject channelAutoConf = context.getChannelAutoConf();
            if (channelAutoConf == null || channelAutoConf.isEmpty()) {
                result.addFail("渠道channelKey=" + channelKey + " 无配置信息，请联系管理员");
                return result;
            }

            JSONObject config = new JSONObject();
            for (Object object : configList) {
                config.put(object.toString(), channelAutoConf.getString(object.toString()));
            }

            //H5页面配置，删除非必要的配置防止信息泄露
            JSONObject styleConf = context.getStyleConf();
            JSONObject newStyleConf = (styleConf == null ? new JSONObject() : styleConf.clone());
            newStyleConf.remove("CONTACT_ORDER_URL");
            newStyleConf.remove("H5_SATISFY_SHOW_COUNT");
            newStyleConf.remove("H5_SHOW_TODO");
            newStyleConf.remove("H5_INPUT_B_SWITCH");
            newStyleConf.remove("H5_INPUT_A_SWITCH");
            newStyleConf.remove("H5_ROBOT_TYPE");
            newStyleConf.remove("H5_SHOW_ORDERS");
            newStyleConf.remove("WX_USER_INFO_URL");
            newStyleConf.remove("H5_END_SHOW_COUNT");
            newStyleConf.remove("WX_AUTH_URL");
            newStyleConf.remove("WX_GENERATE_SCHEME_URL");
            newStyleConf.remove("WX_JSAPI_URL");
            newStyleConf.remove("H5_ROBOT_SWITCH");
            newStyleConf.remove("ROBOT_SATISFY_SHOW_COUNT");
            newStyleConf.remove("AIGC_OCR_FLAG");
            newStyleConf.put("openAigcOcr", context.openAigcOcr());

            //CHANNEL_STATE 渠道状态， 0 正常 1 停用
            config.put("APP_ID", context.getChannelState());
            config.put("CHANNEL_STATE", context.getChannelState());
            config.put("ADVERT_MSG", channelAutoConf.getString("ADVERT_MSG"));//ADVERT_MSG 渠道广告
            config.put("version", "v2");
            config.put("styleConf", newStyleConf);
//            config.put("satisfConf", context.getSatisf6Data());
            result.setData(config);
        } catch (Exception e) {
            result.addFail("获取渠道配置信息失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }


    /**
     * 渠道公告列表
     *
     * @return
     */
    public EasyResult actionForNoticeList() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().debug("<actionForNoticeList> << " + param);

        try {
            checkParam(new String[]{"channelKey"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }
            String sql = "select * from CC_H5_NOTICE where CHANNEL_ID = ? and LOSE_TIME >= ? and STATE = 1 order by SORT_NUM,CREATE_TIME DESC";
            EasyCalendar cal = EasyCalendar.newInstance();
            List<JSONObject> noticeList = this.getQuery().queryForList(sql, new Object[]{context.getChannelId(), cal.getDateTime("-")}, new JSONMapperImpl());

            result.setData(noticeList);
        } catch (Exception e) {
            result.addFail("查询渠道公告列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 事项待办列表
     *
     * @return
     */
    @Deprecated
    public EasyResult actionForTodoList() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().info("<actionForTodoList> << " + param);

        try {
            checkParam(new String[]{"channelKey", "sessionId"});
            String channelKey = param.getString("channelKey");
            String sessionId = param.getString("sessionId");
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }
            JSONObject data = new JSONObject();
            result.setData(data);
            if (context.isShowTodo()) {
                EasyCalendar cal = EasyCalendar.newInstance();
                String dateTime = cal.getDateTime("-");
                // 1.01#20211201-1
                String sql = "select * from CC_H5_USER_TODO where CHANNEL_ID = ? and SEND_TIME<=? and LOSE_TIME >= ? and SESSION_ID=? and IS_HIDE = 0 and STATE = 1 order by SORT_NUM,CREATE_TIME DESC";
                List<JSONObject> todoList = this.getQuery().queryForList(sql, new Object[]{context.getChannelId(), dateTime, dateTime, sessionId}, new JSONMapperImpl());
                data.put("todo", todoList);
            }

        } catch (Exception e) {
            result.addFail("查询事项待办列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    private void doQueryContactOrder(String sessionId, JSONObject data) throws Exception {
        long thid = Thread.currentThread().getId();
        VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
        if (visitorModel == null) {
            MediagwIntefaceLogger.getLogger().warn("Thread["+thid+"] <doQueryContactOrder> ，sessionId[" + sessionId + "] 请求工单查询接口，用户会话信息不存在！ ");
            return;
        }

        String mobile = visitorModel.getUserInfo().getString("mobile");
        if (StringUtils.isBlank(mobile)) {
            MediagwIntefaceLogger.getLogger().warn("Thread["+thid+"] <doQueryContactOrder> channelKey[" + visitorModel.getChannelKey() + "]，sessionId[" + sessionId + "] 请求工单查询接口，手机号码为空！ ");
            return;
        }

        EasyCalendar cal = EasyCalendar.newInstance();
        String endTime = cal.getDateTime("-");
        cal.add(EasyCalendar.DAY, -7);
        String startTime = cal.getDateTime("-");
        String channelKey = visitorModel.getChannelKey();

        JSONObject json = new JSONObject();
        json.put("serviceId", "CSSGW-CONTACT");
        json.put("command", "doQueryContactOrder");
        json.put("from", "frommediagw");
        json.put("sender", "MIDEA_AGENT");
        json.put("password", "YQ_85521717");
        json.put("timestamp", System.currentTimeMillis());
        json.put("busiType", "01");
        json.put("userAcc", "jiangxl6");
        json.put("channelNo", "000000");
        json.put("serialId", RandomKit.uniqueStr());
        JSONObject params = new JSONObject();
        params.put("customerTel", mobile);//13715683094
//		params.put("customerCode", sessionId);//客户档案编号，暂时无渠道获取，示例：YH21070153319799
        params.put("pubCreateDateStar", startTime);//客户档案编号为空，只能查近7天的数据
        params.put("pubCreateDateEnd", endTime);//
        params.put("contactOrderBuyDateStar", startTime);//
        params.put("contactOrderBuyDateEnd", endTime);//
        json.put("params", params);
        String reqUrl = Constants.getInReceiveUrl();
        MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] <doQueryContactOrder> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求工单查询接口：reqUrl:" + reqUrl + " >> " + json);
        String reqResult = Proxy.doPostJson(reqUrl, json);
        MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] <doQueryContactOrder> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求工单查询接口 << 请求完成！");

        if (StringUtils.isBlank(reqResult)) {
            MediagwIntefaceLogger.getLogger().warn("Thread["+thid+"] <doQueryContactOrder> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求工单查询接口 << 查询数据为空！");
            return;
        }
        JSONObject parseObject = JSONObject.parseObject(reqResult);
        if (parseObject == null || !"000".equals(parseObject.getString("respCode"))) {
            return;
        }
        JSONArray list = (JSONArray) JSONPath.eval(parseObject, "$.respData.list");
        if (list == null) {
            return;
        }

        data.put("showCount", data.getIntValue("showCount") + list.size());
        List<JSONObject> orders = new ArrayList<>();
        data.put("order", orders);
        EntContext context = EntContext.getContext(channelKey);
        String openLinkUrl = "https://weixincs.midea.com/apps/midea-service/?pathName=/progressDetail/#ORDER_CODE#/#ORG_CODE#/#SALE_CODE#";
        openLinkUrl = (String) context.getStyleConf().getOrDefault("CONTACT_ORDER_URL", openLinkUrl);
        //微信渠道需要拉起授权
        if ("1".equals(context.getChannelType()) || "2".equals(context.getChannelType())) {
            openLinkUrl = getWxAuthData(channelKey, openLinkUrl);
        }

        for (int i = 0; i < list.size(); i++) {
            JSONObject obj = list.getJSONObject(i);
            JSONArray requireVOList = obj.getJSONArray("contactUserRequireVOList");
            if (requireVOList == null) {
                return;
            }
            JSONObject requireVO = (JSONObject) requireVOList.get(0);
            if (requireVO == null) {
                return;
            }
            JSONObject contactOrderVO = obj.getJSONObject("contactOrderVO");
            if (contactOrderVO == null) {
                return;
            }
            //替换回调地址链接参数
            openLinkUrl = openLinkUrl
                    .replaceAll("#ORDER_CODE#", contactOrderVO.getString("contactOrderCode").replace("JR", "FW"))    //工单编号，FW230700075094
                    .replaceAll("#ORG_CODE#", contactOrderVO.getString("orgCode"))
                    .replaceAll("#SALE_CODE#", (String) contactOrderVO.getOrDefault("saleCode", "M-SALES-APP"));    //营销渠道编码
            JSONObject newObj = new JSONObject();
            orders.add(newObj);
            newObj.put("title", "报单时间：" + contactOrderVO.getString("pubCreateDateStr") + "，服务内容：" + requireVO.getString("prodNameFirst") + "-" + requireVO.getString("contactOrderServTypeName"));
            newObj.put("openLink", openLinkUrl);
            newObj.put("contactOrderCode", contactOrderVO.getString("contactOrderCode"));
            newObj.put("sourceOrderCode", contactOrderVO.getString("sourceOrderCode"));
            newObj.put("contactOrderId", contactOrderVO.getString("contactOrderId"));
            newObj.put("customerCode", contactOrderVO.getString("customerCode"));
        }
        MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] <doQueryContactOrder> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求工单查询接口 << 处理完成！");

    }

    /**
     * 通知栏
     *
     * @return
     */
    public EasyResult actionForNoticeBoard() {
        EasyResult result = getResult();
        JSONObject param = getParam();
//        MediagwIntefaceLogger.getLogger().info("<actionForNoticeBoard> << " + param);

        try {
            checkParam(new String[]{"channelKey", "sessionId"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            String sessionId = param.getString("sessionId");
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }
            JSONObject data = new JSONObject();

            EasyCalendar cal = EasyCalendar.newInstance();
            // 1.01#20211201-1
            String dateTime = cal.getDateTime("-");
            String sql = "select * from CC_H5_NOTICE where CHANNEL_ID = ? and SEND_TIME <= ? and LOSE_TIME >= ? and STATE = 1 order by SORT_NUM,CREATE_TIME DESC";
            List<JSONObject> noticeList = this.getQuery().queryForList(sql, new Object[]{context.getChannelId(), dateTime, dateTime}, new JSONMapperImpl());
            data.put("noticeList", noticeList);
            data.put("showCount", noticeList.size());

            if (context.isShowTodo()) {
                // 1.01#20211201-1
                sql = "select * from CC_H5_USER_TODO where CHANNEL_ID = ? and SEND_TIME < ? and LOSE_TIME >= ? and SESSION_ID=? and IS_HIDE = 0 and STATE = 1  order by SORT_NUM,CREATE_TIME DESC";
                List<JSONObject> todoList = this.getQuery().queryForList(sql, new Object[]{context.getChannelId(), dateTime, dateTime, sessionId}, new JSONMapperImpl());
                data.put("todoList", todoList);
                data.put("showCount",data.getIntValue("showCount")+todoList.size());
            }

            //未完结工单查询，2024-12-05 未开启“CSS工单自动推送”时才去查接入单
            if (context.getCcData().getIntValue("CSS_QUERY_FLAG")==0 && context.isShowOrders()) {
                doQueryContactOrder(sessionId, data);
            }
            result.setData(data);
        } catch (Exception e) {
            result.addFail("查询通知栏列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 阅读事项待办
     *
     * @return
     */
    public EasyResult actionForReadTodo() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().debug("<actionForReadTodo> << " + param);

        try {
            checkParam(new String[]{"channelKey", "sessionId", "todoId"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            String sessionId = param.getString("sessionId");
            String todoId = param.getString("todoId");
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }
            String sql = "select * from CC_H5_USER_TODO where CHANNEL_ID = ? and SESSION_ID=? and ID=?";
            JSONObject todoObj = this.getQuery().queryForRow(sql, new Object[]{context.getChannelId(), sessionId, todoId}, new JSONMapperImpl());
            if (todoObj == null) {
                result.addFail("未找到事项待办");
                return result;
            }

            if ("0".equals(todoObj.getString("HIDE_TYPE"))) {
                this.hideTodo(todoId);
            }
            if ("0".equals(todoObj.getString("READ_STATE"))) {
                this.getQuery().execute("update CC_H5_USER_TODO set READ_STATE=1 where ID=?", todoId);
            }

        } catch (Exception e) {
            result.addFail("查询事项待办列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    public EasyResult actionForHideTodo() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().debug("<actionForHideTodo> << " + param);

        try {
            checkParam(new String[]{"channelKey", "sessionId", "todoId"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            String sessionId = param.getString("sessionId");
            String todoId = param.getString("todoId");
            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }
            String sql = "select * from CC_H5_USER_TODO where CHANNEL_ID = ? and SESSION_ID=? and ID=?";
            JSONObject todoObj = this.getQuery().queryForRow(sql, new Object[]{context.getChannelId(), sessionId, todoId}, new JSONMapperImpl());
            if (todoObj == null) {
                result.addFail("未找到事项待办");
                return result;
            }

            if ("1".equals(todoObj.getString("HIDE_TYPE"))) {
                this.hideTodo(todoId);
            }

        } catch (Exception e) {
            result.addFail("查询事项待办列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    private void hideTodo(String todoId) throws Exception {
        this.getQuery().execute("update CC_H5_USER_TODO set IS_HIDE=1 where ID=?", todoId);
    }

    /**
     * 自助服务专区列表
     *
     * @return
     */
    public EasyResult actionForSelfHelpList() {
        long thid = Thread.currentThread().getId();
        EasyResult result = getResult();
        JSONObject param = getParam();
        long startTime = System.currentTimeMillis();
        try {
            checkParam(new String[]{"channelKey","sessionId"});
            String channelKey = param.getString("channelKey");//全媒体渠道标识
            String sessionId = param.getString("sessionId");

            EntContext context = EntContext.getContext(channelKey);
            if (context == null) {
                MediagwIntefaceLogger.getLogger().warn("Thread["+ thid +"]sessionId["+sessionId+"]自助服务专区查询 未找到channelKey=" + channelKey + "的渠道信息 " + param.getString("msgId"));
                result.addFail("未找到channelKey=" + channelKey + "的渠道信息");
                return result;
            }

            String sql = "select * from CC_H5_SELF_HELP_CLASS where CHANNEL_ID = ? and STATE=1 order by SORT_NUM,CREATE_TIME DESC";
            EasyQuery query = this.getQuery();
            List<JSONObject> classList = query.queryForList(sql, new Object[]{context.getChannelId()}, new JSONMapperImpl());

            if (classList == null || classList.isEmpty()) {
                MediagwIntefaceLogger.getLogger().warn("Thread["+ thid +"]sessionId["+sessionId+"]自助服务专区查询 未找到channelKey=" + channelKey + "的自助服务专区信息 " + param.getString("msgId"));
                result.addFail("未找到channelKey=" + channelKey + "的自助服务专区信息");
                return result;
            }

            //判断访客角色，如果访客非“商家”角色，不展示“商家入口”
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            String sql1 = "select * from CC_H5_SELF_HELP where CLASS_ID = ? and STATE = 1 order by SORT_NUM,CREATE_TIME DESC";
            if(visitor==null){
                result.addFail("当前未接入服务，请刷新页面！");
                return result;
            }
            sql1 = CommonUtil.getSelfSql(visitor);
            MediagwIntefaceLogger.getLogger().info("Thread["+ thid +"]sessionId["+sessionId+"] 自助服务专区查询，SQL："+sql1);

            for (int i = 0; i < classList.size(); i++) {
                JSONObject classObj = classList.get(i);
                List<JSONObject> selfList = query.queryForList(sql1, new Object[]{classObj.getString("CLASS_ID")}, new JSONMapperImpl());
                ArrayList<JSONObject> newSelfList = new ArrayList<>();
                if (selfList != null && !selfList.isEmpty()) {
                    for (int j = 0; j < selfList.size(); j++) {
                        JSONObject selfObj = selfList.get(j);
                        String linkUrl = selfObj.getString("LINK_URL");
                        String linkType = selfObj.getString("LINK_TYPE");

                        //“清洗保养”服务，链接地址填yc-mediagw的接口地址。
                        //uat：https://ccuatgw.midea.com:9061/yc-mediagw/aigcAgent?action=goAigc
                        //生产：https://ccuatgw.midea.com:9061/yc-mediagw/aigcAgent?action=goAigc
                        if(!"iframe".equals(linkType) || !linkUrl.contains("/yc-mediagw/aigcAgent?action=goAigc")){
                            newSelfList.add(selfObj);
                            continue;
                        }
                        linkUrl = linkUrl+"&channelKey="+channelKey+"&sessionId="+sessionId+"&chatSessionId="+visitor.getChatSessionId();
                        selfObj.put("LINK_URL", linkUrl);
                        if(context.aigcAgentSwitch()){
                            newSelfList.add(selfObj);
                        }
                    }
                    classObj.put("SELF_LIST", newSelfList);
                }
            }

            result.setData(classList);
            MediagwIntefaceLogger.getLogger().info("Thread["+ thid +"]sessionId["+sessionId+"]自助服务专区查询 完成，耗时：" + (System.currentTimeMillis()-startTime)+" ms");

        } catch (Exception e) {
            result.addFail("查询自助服务专区列表失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 检查会话是否存在
     * 是否正在人工会话中，如果是返回坐席id和chatSessionId
     * 是否在排队中，排队中返回chatSessionId
     *
     * @return EasyResult；
     */
    public EasyResult actionForCheckSession() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        try {
            String sessionId = param.getString("sessionId");
            JSONObject cmdJson = param.getJSONObject("cmdJson");
            if (StringUtils.isBlank(sessionId) && cmdJson != null) {
                sessionId = cmdJson.getString("sessionId");
            }
            if (StringUtils.isBlank(sessionId)) {
                throw new Exception("必填参数[sessionId]不能为空");
            }

            VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
            String bizTypeStr="queue,zxkf,robot,word,satisfy";
            if (visitorModel != null && bizTypeStr.contains(visitorModel.getBizType())) {
                JSONObject jsonobject = new JSONObject(true);
                jsonobject.put("sessionId", sessionId);
                jsonobject.put("agentId", visitorModel.getAgentId());
                jsonobject.put("chatSessionId", visitorModel.getChatSessionId());
                jsonobject.put("bizType", visitorModel.getBizType());

                if ("queue".equals(visitorModel.getBizType())) {
                    String queueNo = CacheUtil.get("lastNotifyQueueNo_" + sessionId);
                    jsonobject.put("queueNo", queueNo);
                    jsonobject.put("inQueueTime", CacheUtil.get("userInQueueTime_" + sessionId));
                    jsonobject.put("timestamp", System.currentTimeMillis());
                }

                EntContext context = EntContext.getContext(visitorModel.getChannelKey());

                //是否显示”满意度“胶囊
                jsonobject.put("agentEvaluation", context.showAgentEvaluation(sessionId));
                jsonobject.put("robotEvaluation", context.showRobotEvaluation(sessionId));

                //是否显示”结束会话“胶囊
                jsonobject.put("closeChat", context.showCloseChat(sessionId));
                jsonobject.put("inviteVideoFlag", getVideoInviteFlag(visitorModel.getEntId(), visitorModel.getChatSessionId()));

                result.setData(jsonobject);

                return result;
            }

            result.addFail("当前不在会话中!");

        } catch (Exception e) {
            result.addFail("接口调用失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    private boolean getVideoInviteFlag(String entId, String chatSessionId) {
        try {
            EasySQL sql2 = new EasySQL("select ID,CLEAR_CAUSE from ");
            sql2.append(this.getTableName(entId, "CC_MEDIA_VIDEO_RECORD t1"));
            sql2.append(" where 1=1");
            sql2.append(chatSessionId, " and t1.CHAT_SESSION_ID = ? ", false);
            sql2.append(" order by t1.CALL_TIME desc ");
            JSONObject jsonObject = this.getQuery().queryForRow(sql2.getSQL(), sql2.getParams(), new JSONMapperImpl());
            if (jsonObject == null || !jsonObject.containsKey("ID")) {
                return false;
            }
            String clearCause = jsonObject.getString("CLEAR_CAUSE");
            return StringUtils.isBlank(clearCause);
        } catch (Exception e) {
            MediagwLogger.getLogger().error(e.getMessage(), e);
        }
        return false;
    }

    /**
     * 客户端输入实时推送接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForCustInputPush() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        long thid = Thread.currentThread().getId();
        String channelKey = param.getString("channelKey");
        String sessionId = param.getString("sessionId");
        try {
            checkParam(new String[]{"channelKey", "sessionId"});
            String msgContent = param.getString("msgContent");
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if (visitor == null || !"zxkf".equals(visitor.getBizType()) || StringUtils.isBlank(visitor.getAgentId())) {
//                MediagwIntefaceLogger.getLogger().warn("Thread["+thid+"] channelKey["+channelKey+"] sessionId["+sessionId+"] 客户端输入实时推送接口  >> 当前未接入在线客服，无法推送消息");
                result.addFail("当前未接入在线客服，无法推送消息");
                return result;
            }
            //1.01#20211201-1
            CacheUtil.put("MSG_TMP_" + sessionId, msgContent, 10);
            //发送实时输入内容到坐席端
            JSONObject messageObj = new JSONObject();
            JSONObject data = new JSONObject();
            messageObj.put("data", data);
            messageObj.put("serialId", RandomKit.randomStr());
            messageObj.put("agentId", visitor.getAgentId());
            messageObj.put("entId", visitor.getEntId());
            messageObj.put("messageId", "userTmpMsg");
            messageObj.put("channelKey", channelKey);
            messageObj.put("command", "custInputPush");
            messageObj.put("timestamp", System.currentTimeMillis());
            data.put("chatSessionId", visitor.getChatSessionId());
            data.put("channelKey", channelKey);
            data.put("sessionId", sessionId);
            data.put("msgContent", msgContent);
            data.put("msgType", "text");
            ProducerBroker.sendAgentMessage(visitor.getAgentId(), messageObj.toJSONString());
        } catch (Exception e) {
            result.addFail("客户端输入实时推送失败");
//            MediagwIntefaceLogger.getLogger().warn("Thread["+thid+"] channelKey["+channelKey+"] sessionId["+sessionId+"] 客户端输入实时推送接口  >> 客户端输入实时推送失败");
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }
        return result;
    }

    /**
     * 消息确认，H5客户端收到服务端消息后，调用该接口，确认已读
     *
     * @return
     */
    public EasyResult actionForMsgConfirm() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        String sessionId = param.getString("sessionId");
        String channelKey = param.getString("channelKey");

        String _msgId = param.getString("_msgId");
//        MediagwIntefaceLogger.getLogger().info("actionForMsgConfirm-------->" + param);
        try {
            checkParam(new String[]{"data"});
            String datastr = param.getString("data");
            datastr = URLDecoder.decode(datastr,"UTF-8");
            JSONArray data = JSONArray.parseArray(datastr);
            SessionNotifyEvents sessionNotify = SessionNotifyEvents.getInstance();
            String eventKey = SessionNotifyEvents.getEventKey(channelKey, sessionId);
            for (int i = 0; i < data.size(); i++) {
                JSONObject msgObj = data.getJSONObject(i);
                msgObj.put("sessionId",sessionId);
                String serialId = msgObj.getString("serialId");
                JSONObject _msgObj = new JSONObject();
                _msgObj.put("serialId", serialId);
                _msgObj.put("data", msgObj);
                sessionNotify.clearMsgBymsgId(eventKey, serialId);
                ServerMessage.replyReadResult(_msgObj);
            }
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
//        MediagwIntefaceLogger.getLogger().info("actionForMsgConfirm-------->agentToCustMsg_" + sessionId + "==" + CacheUtil.get("agentToCustMsg_" + sessionId));
        CacheUtil.delete("agentToCustMsg_" + sessionId);//坐席最后发送消息时间
        return result;
    }


    /**
     * 请求中控接口-获取网页授权地址
     */
    public EasyResult actionForGetWXAuthData() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            checkParam(new String[]{"redirectUrl", "channelKey"});
            String channelKey = param.getString("channelKey");
            String redirectUrl = param.getString("redirectUrl");
            String wxAuthData = this.getWxAuthData(channelKey, redirectUrl);
            JSONObject parseObject = JSONObject.parseObject(wxAuthData);
            result.setData(parseObject);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    private String getWxAuthData(String channelKey, String redirectUrl) throws Exception {
        long thid = Thread.currentThread().getId();
        String apiAuthUrl = EntContext.getContext(channelKey).getWXpiAuthUrl();
        JSONObject params = new JSONObject();
        params.put("url", redirectUrl);
        params.put("scope", "snsapi_userinfo");//显示授权snsapi_userinfo，静默授权snsapi_base
        params.put("state", "");
        MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] 请求中控接口-获取网页授权地址:" + apiAuthUrl + "，请求参数：" + JSONObject.toJSONString(params));
        String wxAuthDataStr = Proxy.doPost2(apiAuthUrl, params, null);
        MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] 请求中控接口-获取网页授权地址，响应结果：" + wxAuthDataStr);
        return wxAuthDataStr;
    }

    /**
     * 请求中控接口-查询用户
     */
    public EasyResult actionForGetWXUserInfo() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            checkParam(new String[]{"openId", "channelKey"});
            String openId = param.getString("openId");
            String channelKey = param.getString("channelKey");
            String userInfoUrl = EntContext.getContext(channelKey).getWXApiUserInfoUrl() + "?openId=" + openId;

            Map<String, String> params = new HashMap<String, String>();
            params.put("openId", openId);
            MediagwIntefaceLogger.getLogger().info("请求中控接口-查询用户:" + userInfoUrl + "，请求参数：" + JSONObject.toJSONString(params));
            String doGetNotSSL = Proxy.doGet(userInfoUrl);
            MediagwIntefaceLogger.getLogger().info("请求中控接口-查询用户，响应结果：" + doGetNotSSL);
            JSONObject parseObject = JSONObject.parseObject(doGetNotSSL);
            result.setData(parseObject);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * 求中控接口-获得Jsapi签名
     */
    public EasyResult actionForGetWXJsapi() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
//			checkParam(new String[] {"openId"});
            String channelKey = param.getString("channelKey");
            String url = param.getString("url");
            String apiJsapiUrl = EntContext.getContext(channelKey).getWXApiJsapiUrl();

            JSONObject params = new JSONObject();
            params.put("url", url);
            MediagwIntefaceLogger.getLogger().info("请求中控接口-获得Jsapi签名:" + apiJsapiUrl + "，请求参数：" + JSONObject.toJSONString(params));
            String reqResult = Proxy.doPost2(apiJsapiUrl, params, null);
            MediagwIntefaceLogger.getLogger().info("请求中控接口-获得Jsapi签名，响应结果：" + reqResult);
            JSONObject parseObject = JSONObject.parseObject(reqResult);
            result.setData(parseObject);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * 机器人满意度评价
     */
    public EasyResult actionForRobotEval() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            MediagwIntefaceLogger.getLogger().info("actionForRobotEval  << " + param);
            checkParam(new String[]{"serialId", "sessionId", "chatSessionId", "answerId", "cluid", "evaluation"});
            String sessionId = param.getString("sessionId");
            String cluid = param.getString("cluid");
            String chatSessionId = param.getString("chatSessionId");
            String cacheKey = "RobotEval:" + chatSessionId + "_" + cluid;
            if (CacheUtil.get(cacheKey) != null) {
                MediagwIntefaceLogger.getLogger().warn("actionForRobotEval() 重复提交--->" + param);
                return null;
            }
            CacheUtil.put(cacheKey, 1, 3);

            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);

            if (visitor == null || !"robot".equals(visitor.getBizType())) {
                result.addFail("当前未接入机器人服务，无法评价");
                return result;
            }
            JSONObject data = new JSONObject();
            data.put("msgType", "text");
            data.put("userInfo", visitor.getUserInfo());
            data.put("msgContent", param.getString("question"));
            data.put("sessionId", visitor.getSessionId());
            data.put("keyCode", visitor.getSelectKey());

            param.put("clientId", param.getString("sessionId"));
            param.put("channelKey", visitor.getChannelKey());
            param.put("channelType", visitor.getChannelType());
            param.put("chatSessionId", param.getString("chatSessionId"));
            param.put("command", "robotEval");//用于onlinegw区分是满意度评价，还是机器人问答
            param.put("data", data);

            MediagwIntefaceLogger.getLogger().info("actionForRobotEval 机器人答案评价 -> " + param);
            IService service = ServiceContext.getService(MediaConstants.ONLINEGW_SERVICE);
            JSONObject robotResult = service.invoke(param);
            MediagwIntefaceLogger.getLogger().info("actionForRobotEval 机器人答案评价 结果：" + robotResult);
            String message = (String) JSONPath.eval(robotResult, "$.data.message");
            result.setMsg(message);

            String serialId = param.getString("serialId");
            //评价类型 0-满意， 1-不满意，2-撤回评价
            String evaluation = param.getString("evaluation");
            String causeId = param.getString("causeId");
            String content = param.getString("content");

            String entId = getEntId();
            //更新聊天记录的点评标识
//			alter table CC_MEDIA_CHAT_RECORD add EVALUATION_FLAG number default 2;
//			comment on column CC_MEDIA_CHAT_RECORD.EVALUATION_FLAG is '会话点评标识，0 点赞， 1 点踩，2 未点评，3无需点评';
//			alter table CC_MEDIA_CHAT_RECORD add EVALUATION_DATA varchar2(255);
//			comment on column CC_MEDIA_CHAT_RECORD.EVALUATION_DATA is '会话点评结果，json字符串，可为空';

            //评价满意
            if ("0".equals(evaluation)) {
                EasyRecord record2 = new EasyRecord(getTableName(entId, "CC_MEDIA_CHAT_RECORD"), "CHAT_ID");
                record2.setPrimaryValues(serialId);
                record2.set("EVALUATION_FLAG", evaluation);
                SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "update");
                UserEventDispatcher.addEvent(sqlExecutor2);
                return result;
            }

            //评价不满意，是否需要转人工
            if ("1".equals(evaluation)) {
                JSONObject evalData = new JSONObject();
                evalData.put("causeId", causeId);
                evalData.put("content", content);
//				this.getQuery().execute("update "+getTableName(entId, "CC_MEDIA_CHAT_RECORD")+" set EVALUATION_FLAG=?,EVALUATION_DATA=? where CHAT_ID=?", new Object[] {evaluation,evalData.toJSONString(),serialId});
                EasyRecord record2 = new EasyRecord(getTableName(entId, "CC_MEDIA_CHAT_RECORD"), "CHAT_ID");
                record2.setPrimaryValues(serialId);
                record2.set("EVALUATION_FLAG", evaluation);
                record2.set("EVALUATION_DATA", evalData.toJSONString());
                SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "update");
                UserEventDispatcher.addEvent(sqlExecutor2);

                JSONObject _data = robotResult.getJSONObject("data");
                if ("zxkf".equalsIgnoreCase(_data.getString("command"))) {//机器人解答不了自动转人工，或者主动转人工
                    visitor.setBizType("getAgent");
                    String dateTime = EasyCalendar.newInstance().getDateTime("-");
                    JSONObject accesssData = new JSONObject();
                    accesssData.put("CHAT_SESSION_ID", visitor.getChatSessionId());
                    accesssData.put("END_TIME", dateTime);
                    accesssData.put("ROBOT_END_TIME", dateTime);
                    accesssData.put("CLEAR_CAUSE", 1);//1.机器人正常结束
                    AccessRecord.getInstance().updateAccessRecord(visitor.getChannelKey(), accesssData);

                    //发送消息到CommandService处理
                    JSONObject jsonObject = new JSONObject();
                    jsonObject.put("data", _data);
                    jsonObject.put("channelKey", visitor.getChannelKey());
                    jsonObject.put("serialId", RandomKit.randomStr());
                    jsonObject.put("sign", "");
                    jsonObject.put("callbackUrl", visitor.getCallbackUrl());
                    jsonObject.put("key", "123456");
                    new CommandService().invoke(jsonObject);

                    //保存机器人小结，继续转人工
                    if (StringUtils.isNotBlank(_data.getString("content"))) {
                        _data.put("msgContent", _data.getString("content"));
                        result.put("data", _data);
                        ChatMessage.saveMessage(visitor.getChannelKey(), jsonObject, ChatMessage.MSG_SENDER_ROBOT);
                    }
                }
                //第二次提交不满意（带有不满意原因选项时，写死提示内容：您的评价反馈已提交”）
                if (causeId != null) {
                    result.setMsg("您的评价反馈已提交");
                }
                return result;
            }

            //撤回评价
            if ("2".equals(evaluation)) {
                EasyRecord record2 = new EasyRecord(getTableName(entId, "CC_MEDIA_CHAT_RECORD"), "CHAT_ID");
                record2.setPrimaryValues(serialId);
                record2.set("EVALUATION_FLAG", evaluation);
                record2.set("EVALUATION_DATA", "");
                SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "update");
                UserEventDispatcher.addEvent(sqlExecutor2);
                return result;
            }

        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;

    }

    /**
     * 收集点击事件，用户点击“引导胶囊，机器人答案点赞/点踩，机器人答案，服务专区，猜你想问”区域都要收集
     *
     * @return
     */
    public EasyResult actionForClickItem() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
//            MediagwIntefaceLogger.getLogger().info("actionForClickItem  << " + param);
            checkParam(new String[]{"channelKey", "sessionId", "tagType"});
            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String tagId = param.getString("tagId");//目标id
            String tagType = param.getString("tagType");
            String tagTypeName = (String) param.getOrDefault("tagTypeName",ChatClickTagLog.getTagTypeName(tagType));
            String tagName = param.getString("tagName");//目标名称
            String ptagName = param.getString("ptagName");//目标父级级名称
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if(visitor==null){
                result.addFail("当前未接入服务，请刷新页面！");
                return result;
            }
            visitor.setLastChatTime(System.currentTimeMillis());
            String chatSessionId = visitor.getChatSessionId();
            String entId = visitor.getEntId();

            Object lock = SyncLockUtil.getLock(chatSessionId);
            synchronized (lock){
                String cacheKey = "firstClick:"+chatSessionId;
                String bizType = visitor.getBizType();
                if(CacheUtil.get(cacheKey) == null && "robot,zxkf,word,queue".contains(bizType)){
                    CacheUtil.put(cacheKey,System.currentTimeMillis(),3*3600);
                    ChatClickTagLog.getInstance().setFirstClick(chatSessionId,param);
                }
            }
            if (StringUtils.isBlank(tagId)) {//tagId不能为空
                tagId = tagType;
            }
            //进入排队后，点击“猜你想问专区”和“机器人消息”中的按钮，链接，点赞/点踩，自动发送系统消息
            EntContext context = EntContext.getContext(channelKey);
            String queueOperMsg = context.getQueueOperMsg();
            String queueOperMsgTags = "2,3,5,6,7";
            if ("queue".equals(visitor.getBizType()) && queueOperMsgTags.contains(tagType) && StringUtils.isNotBlank(queueOperMsg)) {
                RequestDataModel requestDataModel = new RequestDataModel();
                requestDataModel.setSerialId(RandomKit.uniqueStr());
                requestDataModel.setChannelKey(visitor.getChannelKey());
                requestDataModel.setEntId(visitor.getEntId());
                requestDataModel.setTimestamp(System.currentTimeMillis() + "");
                requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
                requestDataModel.setEvent("System");
                JSONObject data = new JSONObject();
                data.put("msgTime", EasyCalendar.newInstance().getDateTime("-"));//消息发送时间
                data.put("msgType", "text");
                data.put("msgContent", queueOperMsg);
                data.put("sender", "system");
                data.put("chatSessionId", chatSessionId);
                data.put("sessionId", visitor.getSessionId());
                data.put("channelKey", visitor.getChannelKey());
                data.put("channelId", context.getChannelId());
                data.put("channelName", context.getChannelName());
                data.put("channelType", context.getChannelType());
                requestDataModel.setData(data);
                ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());
            }

            EasyCalendar cal = EasyCalendar.newInstance();
            //入库
            EasyRecord record = new EasyRecord(getTableName(entId, "CC_MEDIA_H5_CLICK_RECORD"), "ID");
            record.setPrimaryValues(RandomKit.uniqueStr());
            record.set("CREATE_TIME", cal.getDateTime("-"));
            record.set("CHAT_SESSION_ID", chatSessionId);
            record.set("ENT_ID", entId);
            record.set("TAG_ID", tagId);//点击标识：问题id，胶囊id，点赞或点踩对应答案id
            record.set("TAG_NAME", tagName);//点击名称
            record.set("TAG_TYPE", tagType);
            record.set("SESSION_ID", sessionId);
            record.set("CHANNEL_NO", channelKey);
            record.set("MONTH_ID", cal.getFullMonth());//202110
            record.set("DATE_ID", cal.getDateInt());//20211027
            DataTimeUtil.setDataTime(record);

            if ("1".equals(tagType) || "2".equals(tagType) || "3".equals(tagType)) {
                String serviceId = MediaConstants.ROBOTGW_SERVICE;
                ServiceResource serviceResource = ServiceRegistor.getRegistor().findServiceByID(serviceId);
                if (serviceResource == null) {
                    result.addFail("未找到接口服务[" + serviceId + "]");
                    return result;
                }
                IService service = (IService) serviceResource.newInstance();
                //所有接口服务公共参数
                JSONObject robotParam = new JSONObject();
                robotParam.putAll(param);
                robotParam.put("serialId", param.remove("msgId"));
                robotParam.put("timestamp", System.currentTimeMillis());
                robotParam.put("command", "trigger");
                robotParam.put("channelKey", channelKey);
                robotParam.put("sessionId", sessionId);
                robotParam.put("clientId", sessionId);
                robotParam.put("name", ptagName);
                robotParam.put("content", tagName);
                robotParam.put("ruleType", "1");
                robotParam.put("keyCode", visitor.getSelectKey());
                service.invoke(robotParam);
            }

            SQLExecutor sqlExecutor = new SQLExecutor(sessionId, record, "insert");
            UserEventDispatcher.addEvent(sqlExecutor);

            //20230424 点踩 需要调用机器人满意度评价接口
            if ("6".equals(tagType)) {
//				param.put("evaluation",1);
                MediagwIntefaceLogger.getLogger().info("actionForClickItem  >> 点击“踩”按钮，跳转到机器人评价逻辑：" + param);
                return actionForRobotEval();
            }
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * H5客户端提交满意度，只提交tempId：satisfy7（卡片模板），satisfy6（满意度H5页面）
     * @return
     */
    public EasyResult actionForSubmitSatisfy() {
        EasyResult result = getResult();
        long thid = Thread.currentThread().getId();
        try {
            JSONObject param = getParam();
            MediagwIntefaceLogger.getLogger().info("Thread["+thid+"] actionForSubmitSatisfy  << " + param);
            checkParam(new String[]{"tempId", "serialId", "channelKey", "sessionId", "chatSessionId", "satisfyCode"});
            String tempId = param.getString("tempId");//satisfy7-表情卡片,satisfy6-星星卡片

            if ("satisfy7".equals(tempId)) {
                return saveResultSatisfy7();
            }

            //其他tempId，目前其他tempId只有satisfy6
            return saveResultSatisfy6();

        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * 保存tempId=satisfy6的满意度
     * @return
     */
    private EasyResult saveResultSatisfy6(){
        long thid = Thread.currentThread().getId();
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            checkParam(new String[]{"tempId", "serialId", "channelKey", "sessionId", "chatSessionId", "satisfyCode"});
            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String serialId = param.getString("serialId");
            String chatSessionId = param.getString("chatSessionId");
            String satisfyCode = param.getString("satisfyCode");//一级满意度选中code，单选
            String satisfyItemCode = param.getString("satisfyItemCode");//二级满意度细项选中code，多选 ，示例：1,2,3
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if(visitor==null){
                result.addFail("当前未接入服务，请刷新页面！");
                return result;
            }
            String content = param.getString("content");
            String entId = getEntId();
            EasyCalendar cal = EasyCalendar.newInstance();
            int dateInt = cal.getDateInt();
            String satisfTime = cal.getDateTime("-");
            EasyQuery query = this.getQuery();
            EntContext context = EntContext.getContext(channelKey);
            //获取满意度指标项
            JSONObject sasisfObj = new JSONObject();
            List<JSONObject> satisfList = context.getSatisf6Data();
            for (JSONObject object : satisfList) {
                if (object.getString("CODE").equals(satisfyCode)) {
                    sasisfObj = object;
                    break;
                }
            }

            //更新聊天记录满意度
            String dbName = getSchema(entId);
            String str = query.queryForString("select TEMP_CONFIG from " + getTableName(entId, "CC_MEDIA_CHAT_RECORD") + " where CHAT_ID=?", new Object[]{serialId});
            if (StringUtils.isNotBlank(str)) {
                if(str.startsWith(CacheLongTxtUtil.CACHE_PREFIX)){
                    String str3 = CacheLongTxtUtil.get(dbName, serialId, "TEMP_CONFIG",str);
                    if(StringUtils.isNotBlank(str3)){
                        str = str3;
                    }
                }
                JSONObject tempCofig = JSONObject.parseObject(str);
                if (tempCofig != null) {
                    tempCofig.put("satisfyCode", satisfyCode);
                    tempCofig.put("satisfyItemCode", satisfyItemCode);
                    tempCofig.put("content", content);
                    tempCofig.put("chatSessionId", chatSessionId);
//					query.execute("update "+getTableName(entId, "CC_MEDIA_CHAT_RECORD")+" set TEMP_CONFIG=? where CHAT_ID=?", new Object[] {tempCofig.toJSONString(),serialId});
                    EasyRecord record2 = new EasyRecord(getTableName(entId, "CC_MEDIA_CHAT_RECORD"), "CHAT_ID");
                    record2.setPrimaryValues(serialId);
                    record2.set("TEMP_CONFIG", tempCofig.toJSONString());
                    SQLExecutor sqlExecutor2 = new SQLExecutor(sessionId, record2, "update");
                    UserEventDispatcher.addEvent(sqlExecutor2);
                }
            }
            String satisfy7DataStr = param.getString("satisfy7Data");
            JSONObject satisfy7Data = JSONObject.parseObject(satisfy7DataStr);
            String busiType = (String) JSONPath.eval(satisfy7Data,"$.busiType");//满意度卡片提交类型：机器人满意度：10，人工会话满意度：2（会话中），6（会话结束后）
            if("10".equals(busiType)){
                visitor.setSatisfyRobot(true);
            }

            //查询是否存在人工会话记录，人工会话是否已评价
            JSONObject mediaRecord = query.queryForRow("select SATISF_CODE,END_TIME from " + getTableName(entId, "CC_MEDIA_RECORD") + "  WHERE SERIAL_ID=?", new Object[]{chatSessionId}, new JSONMapperImpl());
            if(mediaRecord==null||StringUtils.isNotBlank(mediaRecord.getString("SATISF_CODE"))){
                MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] actionForSubmitSatisfy  >> 会话不存在或已评价！！！！ ");
                result.setMsg("感谢你的评价!");
                return result;
            }

            //是否超时未评价，当前时间 > END_TIME + 5分钟
            String recordEndTime = mediaRecord.getString("END_TIME");
            if(StringUtils.isNotBlank(recordEndTime)){
                EasyCalendar recordEndCal = EasyCalendar.newInstance(recordEndTime,"yyyy-MM-dd HH:mm:ss");
                int satisfyTimeout = context.getSatisfyTimeout();
                recordEndCal.add(EasyCalendar.MINUTE, satisfyTimeout);
                if(EasyDate.getCurrentDate().after(recordEndCal.getCalendar().getTime())){
                    MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] actionForSubmitSatisfy  >> 会话超过评价时间！！！！ ");
                    result.setMsg("感谢你的评价!");
                    return result;
                }
            }

            //更新会话的满意度
            EasyRecord record = new EasyRecord(getTableName(entId, "CC_MEDIA_RECORD"), "SERIAL_ID");
            record.setPrimaryValues(chatSessionId);
            record.set("SATISF_CODE", satisfyCode);
            record.set("SATISF_NAME", sasisfObj.getString("NAME"));
            record.set("SATISF_CAUSE", satisfyCode);
            record.set("SATISF_USER_REMARKS", content);
            record.set("SATISF_TIME", satisfTime);
            SQLExecutor sqlExecutor = new SQLExecutor(sessionId, record, "update");
            UserEventDispatcher.addEvent(sqlExecutor);

            //添加二级满意度细项评价记录
            if (StringUtils.isNotBlank(satisfyItemCode)) {
                String[] items = satisfyItemCode.split(",");
                //获取满意度细项指标
                JSONArray itemList = sasisfObj.getJSONArray("itemList");
                for (String itemCode : items) {
                    JSONObject itemObj = new JSONObject();
                    for (int i = 0; i < itemList.size(); i++) {
                        if (itemCode.equals(itemList.getJSONObject(i).getString("CODE"))) {
                            itemObj = itemList.getJSONObject(i);
                            break;
                        }
                    }

                    EasyRecord record3 = new EasyRecord(getTableName(entId, "CC_MEDIA_SATISF_EXT_RECORD"), "ID");
                    record3.setPrimaryValues(RandomKit.randomStr());
                    record3.set("CHAT_SESSION_ID", chatSessionId);
                    record3.set("SATISF_NAME", sasisfObj.getString("NAME"));
                    record3.set("SATISF_CODE", sasisfObj.getString("CODE"));
                    record3.set("SATISF_ITEM_CODE", itemCode);
                    record3.set("SATISF_ITEM_NAME", itemObj.getString("NAME"));
                    record3.set("SATISF_TIME", satisfTime);
                    record3.set("SATISF_VALUE", 5);
                    record3.set("DATE_ID", dateInt);
                    record3.set("ENT_ID", entId);
                    record3.set("CHANNEL_NO", channelKey);
                    SQLExecutor sqlExecutor3 = new SQLExecutor(sessionId, record3, "insert");
                    UserEventDispatcher.addEvent(sqlExecutor3);
                }
            }
            //如果是同一个会话
            if(StringUtils.equals(visitor.getChatSessionId(),chatSessionId)){
                visitor.setSatisfy(true);
            }
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * H5客户端点击“满意度评价”胶囊，获取满意度评价模板
     * @return
     */
    public EasyResult actionForUserReqSatisfy() {
        long thid = Thread.currentThread().getId();
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] actionForUserReqSatisfy << " + param);
            checkParam(new String[]{"channelKey", "sessionId"});
            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String busiType = (String) param.getOrDefault("busiType","2");//机器人满意度胶囊，传10，短信入口人工满意度 传2

            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if(visitor==null){
                result.addFail("当前未接入服务，请刷新页面！");
                return result;
            }
            EntContext context = EntContext.getContext(channelKey);
            String entId = getEntId();
            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] actionForUserReqSatisfy << visitor.getBizType()" + visitor.getBizType());

            if (!"zxkf".equalsIgnoreCase(visitor.getBizType()) && !"robot".equalsIgnoreCase(visitor.getBizType())) {
                result.addFail("当前不在人工或机器人会话中，无法主动评价");
                return result;
            }

//			{
//			    "data":{
//			        "userInfo":Object{...},
//			        "msgType":"text",
//			        "msgContent":"亲爱哒，本次与小美的会话结束了哟，感谢您对美的对支持，期待您的再次光临，再见.",
//			        "tempConfig":"{\"tempData\":[{\"ID\":\"83705717366722359030595\",\"NAME\":\"不满意\",\"CODE\":\"4\",\"itemList\":[{\"ID\":\"gh_58f8a50fddbb4002\",\"NAME\":\"答非所问\",\"CODE\":\"9\",\"SATISF_ID\":\"83705717366722359030595\"},{\"ID\":\"gh_58f8a50fddbb4001\",\"NAME\":\"响应慢\",\"CODE\":\"8\",\"SATISF_ID\":\"83705717366722359030595\"},{\"ID\":\"gh_58f8a50fddbb4003\",\"NAME\":\"不耐烦\",\"CODE\":\"10\",\"SATISF_ID\":\"83705717366722359030595\"}]},{\"ID\":\"83705717658682359113640\",\"NAME\":\"一般\",\"CODE\":\"3\",\"itemList\":[{\"ID\":\"gh_58f8a50fddbb3003\",\"NAME\":\"答非所问\",\"CODE\":\"7\",\"SATISF_ID\":\"83705717658682359113640\"},{\"ID\":\"gh_58f8a50fddbb3002\",\"NAME\":\"专业知识不强\",\"CODE\":\"6\",\"SATISF_ID\":\"83705717658682359113640\"},{\"ID\":\"gh_58f8a50fddbb3001\",\"NAME\":\"响应一般\",\"CODE\":\"5\",\"SATISF_ID\":\"83705717658682359113640\"}]},{\"ID\":\"83705717950222359244647\",\"NAME\":\"满意\",\"CODE\":\"2\",\"itemList\":[{\"ID\":\"gh_58f8a50fddbb2003\",\"NAME\":\"表达清晰\",\"CODE\":\"4\",\"SATISF_ID\":\"83705717950222359244647\"},{\"ID\":\"gh_58f8a50fddbb2001\",\"NAME\":\"响应速度\",\"CODE\":\"3\",\"SATISF_ID\":\"83705717950222359244647\"},{\"ID\":\"gh_58f8a50fddbb2002\",\"NAME\":\"态度友好\",\"CODE\":\"1\",\"SATISF_ID\":\"83705717950222359244647\"}]},{\"ID\":\"83705718200822359496219\",\"NAME\":\"非常满意\",\"CODE\":\"1\",\"itemList\":[{\"ID\":\"gh_58f8a50fddbb1003\",\"NAME\":\"响应速度\",\"CODE\":\"3\",\"SATISF_ID\":\"83705718200822359496219\"},{\"ID\":\"gh_58f8a50fddbb1002\",\"NAME\":\"专业知识过硬\",\"CODE\":\"2\",\"SATISF_ID\":\"83705718200822359496219\"},{\"ID\":\"gh_58f8a50fddbb1001\",\"NAME\":\"态度友好\",\"CODE\":\"1\",\"SATISF_ID\":\"83705718200822359496219\"}]}],\"tempId\":\"satisfy6\",\"tempType\":\"2\"}",
//			        "sender":"system",
//			        "videoSatisfyUrl":"",
//			        "sessionId":"d7e877911c44a34d7998a84114cfae4a",
//			        "chatSessionId":"83702902466878870234945"
//			    },
//			    "channelKey":"gh_58f8a50fddbb",
//			    "entId":"1000",
//			    "serialId":"83702901181118867616728",
//			    "sign":"FF51CC3D2E2DB479673261519F0051BB",
//			    "callbackUrl":"WEBCHAT-CALLBACK-SERVICE",
//			    "event":"satisfy",
//			    "key":"123456",
//			    "timestamp":"1629709881878"
//			}
            String serialId = RandomKit.uniqueStr();

            JSONObject tempConfig = context.getSatisfTempConfig(sessionId,visitor.getChatSessionId(),visitor.getAgentPhone(),busiType);
            JSONObject data = new JSONObject();
            data.put("serialId", serialId);
            data.put("msgType", "text");
            data.put("msgContent", "主动评价");
            data.put("sender", "system");
            data.put("sessionId", sessionId);
            data.put("userInfo", visitor.getSimpleUserInfo());
            data.put("chatSessionId", visitor.getChatSessionId());
            data.put("tempConfig", tempConfig.toJSONString());
            //是否显示”结束会话“胶囊
            data.put("closeChat", context.showCloseChat(sessionId));

            //发送消息到客户端
            RequestDataModel requestDataModel = new RequestDataModel();
            requestDataModel.setSerialId(serialId);
            requestDataModel.setChannelKey(channelKey);
            requestDataModel.setEntId(visitor.getEntId());
            requestDataModel.setCallbackUrl(visitor.getCallbackUrl());
            requestDataModel.setEvent("satisfy");
            requestDataModel.setData(data);
            requestDataModel.setTimestamp(System.currentTimeMillis() + "");

            requestDataModel.setSerialId(serialId);
            requestDataModel.getData().put("tempConfig", tempConfig.toJSONString());
            ChatMessage.saveMessage(visitor.getChannelKey(), requestDataModel.toJSON(), ChatMessage.MSG_SENDER_SYS);//系统消息
            ServerMessage.getInstanse("GW_SERVER").invoke(requestDataModel.toString());

        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
//			result.addFail(e.getMessage());
            result.setMsg(e.getMessage());
        }
        return result;
    }

    /**
     * 语音转写
     *
     * @return
     */
    public EasyResult actionForAsrTransfer() {
        EasyResult result = getResult();
        try {
            JSONObject param = getParam();
            MediagwIntefaceLogger.getLogger().info("actionForAsrTransfer << " + param);

            checkParam(new String[]{"channelKey", "sessionId", "filePath", "fileUrl"});
            String asrUrl = Constants.getAsrUrl();
            String serialId = RandomKit.uniqueStr();
            String filePath = param.getString("filePath");
            String fileUrl = param.getString("fileUrl");
            String rootPath = Constants.getFileSysRootPath();
            filePath = rootPath + File.separator + filePath;

            File file = new File(filePath);

            if (file == null || !file.exists()) {
                MediagwIntefaceLogger.getLogger().error("actionForAsrTransfer >> 文件不存在，filePath：" + filePath);
                result.addFail("录音文件不存在");
                return result;
            }
            FileInputStream inputFromFile = new FileInputStream(file);
            FileOutputStream outputToFile = new FileOutputStream(file);
            byte[] src = new byte[inputFromFile.available()];
            inputFromFile.read(src);
            byte[] encodedBytes = Base64.getEncoder().encode(src);

//			action	String	(必填)消息类型：asrTransfer
//			serialId	String	(必填)消息流水号
//			timestamp	String	(必填)时间戳（格式：YYYYMMDDhhmmss）
//			sign	String	(必填)签名信息
//			data	JSON	(必填)数据查询条件
//			data.voiceBase64	String	(必填)录音文件base64编码串
//			data.recordUrl	String	(必填)录音文件路径
//			data.voiceType	String	(必填)录音文件格式
//			data.synFlag	String	(必填)是否实时返回
//			userData	JSON 	(可选）用户自定义参数
//			data.callBackUrl	String	(非必填)非实时时，需要传入该参数，当AI平台得到转写结果后通过调接口回传转写文本
            JSONObject params = new JSONObject();
            JSONObject data = new JSONObject();
            params.put("action", "asrTransfer");
            params.put("serialId", serialId);
            params.put("data", data);
            params.put("timestamp", EasyDate.getCurrentDateString());//时间戳（格式：YYYYMMDDhhmmss）
            data.put("voiceBase64", encodedBytes.toString());//(必填)录音文件base64编码串
            data.put("recordUrl", fileUrl);//(必填)录音文件路径
            data.put("voiceType", "arm");//(必填)录音文件格式
            data.put("synFlag", "");//(必填)是否实时返回
//			data.put("callBackUrl", "");//(非必填)非实时时，需要传入该参数，当AI平台得到转写结果后通过调接口回传转写文本
            String doPost = Proxy.doPost(asrUrl, params);
            JSONObject asrResult = JSONObject.parseObject(doPost);
            String respCode = asrResult.getString("result");
            if (!"000".equals(respCode)) {
                result.addFail("转写失败，响应码：" + respCode);
                return result;
            }
            result.setData(asrResult.getJSONArray("data"));
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;

    }


    /**
     * 获取机器人欢迎语列表（猜你想问）数据接口
     *
     * @return EasyResult；
     */
    public EasyResult actionForGetWelcomeList() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        MediagwIntefaceLogger.getLogger().info("获取机器人欢迎语列表（猜你想问）数据接口 << " + param);

        try {
            checkParam(new String[]{"channelKey", "sessionId"});

            String serviceId = MediaConstants.ROBOTGW_SERVICE;
            IService service = ServiceContext.getService(serviceId);

            if (service == null) {
                result.addFail("未找到接口服务[" + serviceId + "]");
                MediagwIntefaceLogger.getLogger().warn("获取机器人欢迎语列表（猜你想问）数据接口 >> 未找到接口服务[" + serviceId + "] " + param.getString("msgId"));

                return result;
            }
            String sessionId = param.getString("sessionId");
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            String channelKey = param.getString("channelKey");
            EntContext entContext = EntContext.getContext(channelKey);
            if (StringUtils.isBlank(entContext.getChannelId())) {
                MediagwIntefaceLogger.getLogger().warn("获取机器人欢迎语列表（猜你想问）数据接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 渠道信息不存在！" + param.getString("msgId"));
                return result;
            }
            if (visitor == null) {
                MediagwIntefaceLogger.getLogger().warn("获取机器人欢迎语列表（猜你想问）数据接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 会话信息不存在，请先接入会话！" + param.getString("msgId"));
                return result;
            }
            if (!StringUtils.equalsIgnoreCase(visitor.getBizType(), "robot")) {
                MediagwIntefaceLogger.getLogger().warn("获取机器人欢迎语列表（猜你想问）数据接口 >> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 当前会话状态[" + visitor.getBizType() + "]不是[robot]！" + param.getString("msgId"));
                return result;
            }

            String entId = getEntId();
            //所有接口服务公共参数
            param.put("entId", entId);
            param.put("serialId", param.getString("msgId"));
            param.put("timestamp", System.currentTimeMillis());
            param.put("command", "welcomeCardList");
            param.put("clientId", sessionId);
            param.put("mobile", visitor.getUserInfo().getString("mobile"));
            param.put("levelName", visitor.getUserInfo().getString("levelName"));
            param.put("nickname", visitor.getUserInfo().getString("nickname"));

            JSONObject invoke = service.invoke(param);
            if (invoke != null) {
                result.setData(invoke.get("welcomeCardList"));
            }
            MediagwIntefaceLogger.getLogger().info("获取机器人欢迎语列表（猜你想问）数据接口 >> finish " + param.getString("msgId"));

        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }

        return result;
    }

    /**
     * 获取用户提醒列表
     *
     * @Description :根据用户手机号码查询提醒列表
     * <AUTHOR>
     * @Datetime 2022/3/29 11:05
     * @return: org.easitline.common.core.web.EasyResult
     */
    public EasyResult actionForRemindList() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        long thid = Thread.currentThread().getId();
        try {
            checkParam(new String[]{"channelKey", "sessionId"});

            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String custPhone = param.getString("custPhone");
//            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] <actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] param：" + param);

            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            EntContext context = EntContext.getContext(channelKey);

            if (visitor == null) {
                MediagwIntefaceLogger.getLogger().error("Thread[" + thid + "] <actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒列表接口，用户不存在");
                result.addFail("sessionId[" + sessionId + "]用户不存在！");
                return result;
            }
            if (StringUtils.isBlank(custPhone)) {
                custPhone = visitor.getUserInfo().getString("mobile");
            }

            if (StringUtils.isBlank(custPhone)) {
                MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] <actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒列表接口：用户手机号码为空！");
//				result.addFail("用户手机号码为空，无法获取提醒列表！");
//				return result;
            }
            //账号类型，微信用户:“openid”,  网页电商用户: “uin”  agentId 坐席
            String accountType = visitor.getUserInfo().getString("accountType");

            String sender = "H5";
            String password = "YQ_85521717";
            String timestampStr = EasyCalendar.newInstance().getDateTime("-");
            String serialId = RandomKit.randomStr();
            String signature = MD5Util.getHexMD5(sender + password + timestampStr + serialId);

            JSONObject json = new JSONObject(false);
            json.put("sender", sender);
            json.put("password", password);
            json.put("timestamp", timestampStr);
            json.put("serialId", serialId);
            json.put("signature", signature);
            json.put("command", "getH5CustPhoneCrowdPackList");
            json.put("serviceId", "ACTIVE-SERVICE-CROWD-INTEFACE");
            json.put("param", MapKit.create().set("custPhone", custPhone).set("customerId", sessionId).set("accountType", accountType).set("channelKey", channelKey));
            String reqUrl = Constants.getReceiveUrl();

//            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] <actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒列表接口：reqUrl:" + reqUrl + " >> " + json);
            String resultStr = Proxy.doPostJson(reqUrl, json,null,1500);
//            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] <actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒列表响应结果 << " + resultStr);

            JSONObject resultObj = JSONObject.parseObject(resultStr);
            if (!StringUtils.equals("000", resultObj.getString("respCode"))) {
                result.addFail(resultObj.getString("respDesc"));
                return result;
            }

            JSONArray data = (JSONArray) JSONPath.eval(resultObj, "$.respData");
            if (data == null) {
                data = new JSONArray();
            }

            result.setData(data);

        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }

        return result;
    }

    /**
     * 获取提醒明细
     *
     * @Description :
     * <AUTHOR>
     * @Datetime 2022/3/29 11:05
     * @return: org.easitline.common.core.web.EasyResult
     */
    public EasyResult actionForRemindDetail() {
        EasyResult result = getResult();
        JSONObject param = getParam();
        long thid = Thread.currentThread().getId();
        try {
            checkParam(new String[]{"channelKey", "sessionId", "id"});

            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String custPhone = param.getString("custPhone");
            String id = param.getString("id");
            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            EntContext context = EntContext.getContext(channelKey);

            if (StringUtils.isBlank(custPhone)) {
                custPhone = visitor.getUserInfo().getString("mobile");
            }

            if (visitor == null) {
                MediagwIntefaceLogger.getLogger().error("<actionForRemindList> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒列表接口，用户不存在");
                result.addFail("sessionId[" + sessionId + "]用户不存在！");
                return result;
            }

            if (StringUtils.isBlank(custPhone)) {
                MediagwIntefaceLogger.getLogger().warn("Thread[" + thid + "] <actionForRemindDetail> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒明细接口：用户手机号码为空！");
//				result.addFail("用户手机号码为空，无法获取提醒列表！");
//				return result;
            }


            //账号类型，微信用户:“openid”,  网页电商用户: “uin”  agentId 坐席
            String accountType = visitor.getUserInfo().getString("accountType");

            String sender = "H5";
            String password = "YQ_85521717";
            String timestampStr = EasyCalendar.newInstance().getDateTime("-");
            String serialId = RandomKit.randomStr();
            String signature = MD5Util.getHexMD5(sender + password + timestampStr + serialId);

            JSONObject json = new JSONObject(false);
            json.put("sender", sender);
            json.put("password", password);
            json.put("timestamp", timestampStr);
            json.put("serialId", serialId);
            json.put("signature", signature);
            json.put("command", "getH5CrowdPackScript");
            json.put("serviceId", "ACTIVE-SERVICE-CROWD-INTEFACE");
            json.put("param", MapKit.create().set("custPhone", custPhone).set("id", id).set("customerId", sessionId).set("accountType", accountType).set("channelKey", channelKey));

            String reqUrl = Constants.getReceiveUrl();

            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] <actionForRemindDetail> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒明细接口：reqUrl:" + reqUrl + " >> " + json);
            String resultStr = Proxy.doPostJson(reqUrl, json);
            MediagwIntefaceLogger.getLogger().info("Thread[" + thid + "] <actionForRemindDetail> channelKey[" + channelKey + "]，sessionId[" + sessionId + "] 请求获取用户提醒明细响应结果 << " + resultStr);

            JSONObject resultObj = JSONObject.parseObject(resultStr);
            if (!StringUtils.equals("000", resultObj.getString("respCode"))) {
                result.addFail(resultObj.getString("respDesc"));
                return result;
            }

            String data = (String) JSONPath.eval(resultObj, "$.respData");
            if (StringUtils.isBlank(data)) {
                result.addFail("未查到用户提醒明细数据！");
                return result;
            }

            result.setData(data);

        } catch (Exception e) {
            result.addFail(e.getMessage());
            MediagwIntefaceLogger.getLogger().error(e.getMessage(), e);
        }

        return result;
    }

    /**
     * 视频客服满意度评价，打开新满意度页面
     */
    public void actionForOpenSatisfy() {
        try {
            checkParam(new String[]{"channelKey", "serialId", "sessionId", "agentPhone"});
            JSONObject param = getParam();
            String channelKey = param.getString("channelKey");
            String chatSessionId = param.getString("serialId");
            String agentPhone = param.getString("agentPhone");
            EntContext context = EntContext.getContext(channelKey);
            //satisfy7-表情卡片,satisfy6-星星卡片
            String satisfyType = context.getChannelConf().getString("SATISFY_TYPE");
            //新版满意度地址
            String satisfyUrl = Constants.getContext().getProperty("SATISFY_CONFIG_URL", "");
            if(StringUtils.isBlank(satisfyUrl)){
                MediagwIntefaceLogger.getLogger().error("channelKey[" + channelKey + "]打开新满意度页面，satisfyUrl不存在！");
                renderText("链接不存在1！");
                return;
            }
            if("satisfy6".equals(satisfyType)){
                keepPara();
                String hoststr = satisfyUrl.substring(0,satisfyUrl.indexOf("yc-mediagw"));
                redirect(hoststr+"/yc-mediagw/pages/appraise/appraise.jsp?serialId="+chatSessionId+"&agentPhone="+agentPhone);
                return;
            }
//            String sessionId = param.getString("sessionId");

            //1.Command:saveSatisfy
            JSONObject serParam = new JSONObject();
            serParam.put("serviceId", "NEW-SATISFY-MEDIA-SERVICE");
            serParam.put("command", "saveSatisfy");
            serParam.put("sessionId", chatSessionId);//会话id
            serParam.put("workNo", agentPhone);//接待坐席工号
            serParam.put("channelKey", channelKey);//渠道id
            serParam.put("busiType", "4");//1热线 2在线 3服务号 4视频

            MediagwIntefaceLogger.getLogger().info("channelKey[" + channelKey + "]打开新满意度页面，保存满意度模板明细接口 >> "+serParam);
            IService service = ServiceContext.getService("NEW-SATISFY-MEDIA-SERVICE");
            JSONObject result = service.invoke(serParam);
            MediagwIntefaceLogger.getLogger().info("channelKey[" + channelKey + "]打开新满意度页面，保存满意度模板明细接口 << "+result);

            if (result == null || StringUtils.isBlank(result.getString("id"))) {
                MediagwIntefaceLogger.getLogger().error("channelKey[" + channelKey + "]打开新满意度页面，获取[satisfy7-表情卡片]满意度模板配置失败，saveSatisfy接口返回结果为空！");
                renderText("链接不存在2！");
                return;
            }

            if(satisfyUrl.indexOf("?")>=0) {
                satisfyUrl = satisfyUrl+"&id="+result.getString("id");
            }else {
                satisfyUrl = satisfyUrl+"?id="+result.getString("id");
            }
            redirect(satisfyUrl);
        } catch (Exception e) {
            MediagwIntefaceLogger.getLogger().error(e.getMessage(),e);
            renderText("链接失效！");
        }

    }

    private static final String bizTypeStr = "robot,zxkf,queue,getAgent";
    private static final String newWoParamCmdStr = "getUrge,addUrge,cancelService,addSupplement";
    private static final String saveCssCmdStr = "addUrge,cancelService,changeTime,addSupplement";
    /**
     * CSS工单操作事件
     */
    public EasyResult actionForCssOrderOper() {
        long thid = Thread.currentThread().getId();
        EasyResult result = getResult();
        String method = getRequest().getMethod();
        if("get".equalsIgnoreCase(method)){
            return EasyResult.fail("不支持get请求！");
        }
        try {

            JSONObject param = getParam();
            checkParam(new String[]{"channelKey", "sessionId", "chatSessionId", "cssCmd", "woParam"});

//            服务单操作事件：
//            查询服务单进度：getProgress
//            查询催办项：getUrge（CSS）
//            提交催办：addUrge（CSS）
//            取消服务：cancelService（CSS）
//            修改上门时间：changeTime（CSS）
//            查询补充信息：getSupplement（CSS）
//            提交补充信息：addSupplement（CSS）
//            上传补充信息图片：addSupplementImg
//            联系师傅：contactWorker
//            20241203新增工单卡片按钮点击事件：
//            催促上门：clickUrge
//            修改上门时间：clickChangeTime
//            补充信息：clickSupplement
//            取消服务：clickCancelService
            String cssCmd = param.getString("cssCmd");
            JSONObject pageWoParam = param.getJSONObject("woParam");
            String serviceOrderNo = (String) pageWoParam.getOrDefault("serviceOrderNo",pageWoParam.getString("serviceOrderNO"));
            String answerId = param.getString("answerId");
            String sessionId = param.getString("sessionId");
            String channelKey = param.getString("channelKey");
            String cluid = param.getString("cluid");//本次机器人会话聊天的uid
            String serialId = (String) param.getOrDefault("msgId",RandomKit.randomStr());
            String chatSessionId = param.getString("chatSessionId");

            VisitorModel visitor = VisitorInfos.getInstance().getVisitorModel(sessionId);
            if (visitor == null) {
                MediagwIntefaceLogger.getCssOrderLogger().warn("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper() 当前未接入服务，无法操作！");
                result.addFail("请重新接入服务或刷新页面！");
                return result;
            }
            visitor.setLastChatTime(System.currentTimeMillis());

            //保存工单按钮事件消息--客户消息
            String clickCmdMsg2 = getClickCmdMsg2(cssCmd);
            if(StringUtils.isNotBlank(clickCmdMsg2)){
                saveCssChatMsg(channelKey, sessionId, chatSessionId, clickCmdMsg2, cssCmd, serviceOrderNo,"user",ChatMessage.MSG_SENDER_CUST);
            }

//            if (toCssCmdStr.contains(cssCmd) && !bizTypeStr.contains(visitor.getBizType())) {
            if (!bizTypeStr.contains(visitor.getBizType())) {
                MediagwIntefaceLogger.getCssOrderLogger().warn("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper() 当前未接入服务，无法操作！" + sessionId);
                result.addFail("请重新接入服务或刷新页面！");
                return result;
            }

            //补充信息调试模式，可删除
            if("addSupplement".equals(cssCmd) && Constants.getCssDebugAddsup()){
                String errorMsg = "抱歉，系统出现异常，您可尝试重新操作或用文字描述一下您的问题，如：查询服务进度！";
                String msgContent =  "（调试模式）服务单号：" + serviceOrderNo +"，" + errorMsg;
                MediagwIntefaceLogger.getCssOrderLogger().warn("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper CSS工单操作事件 补充信息调试模式，直接返回失败！");
                result.addFail(errorMsg);
                JSONObject saveChatMsg = saveCssChatMsg(channelKey, sessionId, chatSessionId, msgContent, cssCmd, serviceOrderNo,"robot",ChatMessage.MSG_SENDER_ROBOT);
                //发送消息
                JSONPath.set(saveChatMsg,"$.data.robotData.content",errorMsg);//上屏消息
                ServerMessage.getInstanse("GW_SERVER").invoke(saveChatMsg.toString());
                return result;
            }

            EntContext context = EntContext.getContext(channelKey);
            JSONObject userInfo = visitor.getUserInfo();
            String entId = context.getEntId();
            //机器人网关服务参数
            JSONObject reqRobotJson = new JSONObject();
            reqRobotJson.put("entId", entId);
            reqRobotJson.put("serialId", serialId);
            reqRobotJson.put("timestamp", System.currentTimeMillis());
            reqRobotJson.put("clientId", sessionId);
            reqRobotJson.put("sessionId", sessionId);
            reqRobotJson.put("chatSessionId", chatSessionId);
            reqRobotJson.put("channelKey", channelKey);
            reqRobotJson.put("answerId", answerId);
            reqRobotJson.put("cluid", cluid);
            reqRobotJson.put("command", "cssData");
            reqRobotJson.put("serviceOrderNo", serviceOrderNo);
            reqRobotJson.put("cssCmd", cssCmd);
            pageWoParam.put("serviceOrderNo",serviceOrderNo);
            pageWoParam.put("serviceOrderNO",serviceOrderNo);
            reqRobotJson.put("woParam", pageWoParam);
            //催办查询、催办、取消工单、补充信息 要传woParam={"body":{woParam参数}},其他传 woParam={woParam参数}
            if(newWoParamCmdStr.contains(cssCmd)){
                JSONObject woParam = new JSONObject();
                woParam.put("body",pageWoParam);
                reqRobotJson.put("woParam", woParam);
            //查询补充信息
            }else if("getSupplement".equals(cssCmd)) {
                if (!pageWoParam.containsKey("restParams")) {
                    JSONObject woParam = new JSONObject();
                    woParam.put("serviceOrderNo", serviceOrderNo);
                    woParam.put("restParams", pageWoParam);
                    reqRobotJson.put("woParam", woParam);
                }
            }

            if (userInfo != null) {
                reqRobotJson.put("mobile", userInfo.getString("mobile"));
                reqRobotJson.put("levelName", userInfo.getString("levelName"));
                reqRobotJson.put("nickname", userInfo.getString("nickname"));
            }

            //操作人（operator）：只有取消服务接口operator字段传openId，补充信息和改约接口传 渠道名称 不然会影响CSS工单后台的工单动态-操作人
            if("cancelService".equals(cssCmd)){
                pageWoParam.put("operator",sessionId);
            }else if("changeTime,addSupplement".contains(cssCmd)){
                String operator = context.getChannelName()+"_online";
                if(operator.getBytes().length >=32 ){
                    operator = "online";
                }
                pageWoParam.put("operator", operator);
            }

            MediagwIntefaceLogger.getCssOrderLogger().info("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper CSS工单操作事件 请求参数 -> " + reqRobotJson);
            IService service = ServiceContext.getService(MediaConstants.ROBOTGW_SERVICE);
            JSONObject robotResult = service.invoke(reqRobotJson);
            MediagwIntefaceLogger.getCssOrderLogger().info("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper CSS工单操作事件 结果：" + robotResult);

            JSONObject cssData = robotResult.getJSONObject("cssData");
            if(cssData==null){
                MediagwIntefaceLogger.getCssOrderLogger().warn("Thread["+thid+"] sessionId["+sessionId+"] serviceOrderNo["+serviceOrderNo+"] actionForCssOrderOper CSS工单操作事件 机器人网关返回cssData为空：" + robotResult);
                return result;
            }

            String respCssCmd = (String) cssData.getOrDefault("cssCmd",cssCmd);
            String status = cssData.getString("status");//成功标识1：true：成功，false：失败
            String code = cssData.getString("code");//成功标识2：000000：成功，其他：失败
            String errorMsg = cssData.getString("errorMsg");

            if("true".equals(status)||"000000".equals(code)){
                errorMsg = getCssCmdSuccMsg(cssCmd,errorMsg);
            }else{
                //接口失败时，先errorMsg，再title，再CC默认
                if(StringUtils.isBlank(errorMsg)){
                    errorMsg = cssData.getString("title");
                }
                errorMsg = getCssCmdDefMsg(cssCmd,errorMsg);
            }

            cssData.put("errorMsg",errorMsg);
            result.setData(cssData);

            //保存CSS工单操作事件提交结果，格式：服务单：FW231198070574，改约
            if(saveCssCmdStr.contains(cssCmd)){
                String msgContent = "服务单号：" + serviceOrderNo +"，" + (StringUtils.isNotBlank(errorMsg) ? errorMsg : getCssCmdSaveMsg(cssCmd,status));
                JSONObject saveChatMsg = saveCssChatMsg(channelKey, sessionId, chatSessionId, msgContent, cssCmd, serviceOrderNo,"robot",ChatMessage.MSG_SENDER_ROBOT);
                //发送消息
                JSONPath.set(saveChatMsg,"$.data.robotData.content",errorMsg);//上屏消息
                ServerMessage.getInstanse("GW_SERVER").invoke(saveChatMsg.toString());
            }
        } catch (Exception e) {
            MediagwIntefaceLogger.getCssOrderLogger().error(e.getMessage(), e);
            result.addFail(e.getMessage());
        }
        return result;
    }

    /**
     * 保存工单事件聊天记录
     * @param channelKey 渠道标识
     * @param sessionId 访客标识
     * @param chatSessionId 会话id
     * @param msgContent 内容
     * @param cssCmd 工单事件
     * @param serviceOrderNo 工单编号
     * @return
     */
    private JSONObject saveCssChatMsg(String channelKey, String sessionId, String chatSessionId, String msgContent, String cssCmd, String serviceOrderNo, String senderStr, int sender){
        String serialId = RandomKit.randomStr();
        JSONObject saveJson = new JSONObject();
        JSONObject saveDataJson = new JSONObject();
        saveJson.put("data", saveDataJson);
        saveJson.put("serialId", serialId);
        saveJson.put("channelKey", channelKey);
        saveDataJson.put("serialId", serialId);
        saveDataJson.put("sender", StringUtils.isNotBlank(senderStr)?senderStr:"robot");
        saveDataJson.put("msgType", "text");
        saveDataJson.put("sessionId", sessionId);
        saveDataJson.put("channelKey", channelKey);
        saveDataJson.put("chatSessionId", chatSessionId);
        saveDataJson.put("msgContent", msgContent);
        if(sender==ChatMessage.MSG_SENDER_ROBOT){
            JSONObject saveRobotDataJson = new JSONObject();
            saveDataJson.put("msgContent", "");//sender=robot时，页面不使用该字段，而是使用robotData.content
            saveDataJson.put("robotData", saveRobotDataJson);
            saveRobotDataJson.put("content",msgContent);
        }
        ChatMessage.saveMessage(channelKey, saveJson, sender);
        return saveJson;
    }

    /**
     * 格式化CSS接口返回结果描述：提交成功 上屏展示
     * @param cssCmd 工单事件
     * @param errorMsg CSS接口返回结果描述
     * @return
     */
    private String getCssCmdSuccMsg(String cssCmd, String errorMsg){
        if(errorMsg!=null){
            return errorMsg;
        }
        switch (cssCmd){
            case "addUrge":
                errorMsg = "亲，您的催办已接收！";
                break;
            case "cancelService":
                errorMsg = "亲，您的服务已经取消！";
                break;
            case "changeTime":
                errorMsg = "亲，您的改约请求已提交！";
                break;
            case "addSupplement":
                errorMsg = "亲，您的补充信息已提交成功！";
                break;
            case "getProgress":
                errorMsg = "正在跳转工单详情！";
                break;
//            case "getUrge":
//                break;
//            case "getSupplement":
//                break;
//            case "contactWorker":
//                break;
            default:
                errorMsg = "成功！";
                break;
        }
        return errorMsg;
    }

    /**
     * 格式化CSS接口返回结果描述：提交失败 上屏展示
     * @param cssCmd 工单事件
     * @param errorMsg CSS接口返回结果描述
     * @return
     */
    private String getCssCmdDefMsg(String cssCmd, String errorMsg){
        if(StringUtils.isNotBlank(errorMsg)){
            return errorMsg;
        }
        String returnStr;
        switch (cssCmd){
            case "addUrge":
                returnStr = "催单失败";
                break;
            case "cancelService":
                returnStr = "服务取消失败";
                break;
            case "changeTime":
                returnStr = "改约失败";
                break;
            case "addSupplement":
//                returnStr = "补充失败";
                returnStr = "提交补充失败，请稍后重新提交";
                break;
            case "getProgress":
                returnStr = "打开服务进度失败";
                break;
            case "getUrge":
                returnStr = "获取催办信息失败";
                break;
            case "getSupplement":
                returnStr = "获取补充项失败";
                break;
            case "contactWorker":
                returnStr = "联系师傅网点失败";
                break;
            default:
                returnStr = "网络繁忙";
                break;
        }
        returnStr = returnStr + "，" + "请稍后重试！";
        return returnStr;
    }

    /**
     * 格式化CSS接口返回结果描述：聊天记录内容
     * @param cssCmd 工单事件
     * @param status 工单事件提交结果
     * @return
     */
    private String getCssCmdSaveMsg(String cssCmd,String status){
        String errorMsg = "";
        switch (cssCmd){
            case "addUrge":
                errorMsg = "催单";
                break;
            case "cancelService":
                errorMsg = "服务取消";
                break;
            case "changeTime":
                errorMsg = "改约";
                break;
            case "addSupplement":
                errorMsg = "补充";
                break;
            default:
                break;
        }
        return errorMsg + ("true".equals(status)?"成功！":"失败");
    }



//工单操作事件：
//    工单进度：getProgress （卡片按钮点击）
//    联系师傅网点：contactWorker （卡片按钮点击）
//    查询催办项：getUrge（CSS）
//    提交催办：addUrge（CSS）
//    取消服务：cancelService（CSS）
//    修改上门时间：changeTime（CSS）
//    查询补充信息：getSupplement（CSS）
//    提交补充信息：addSupplement（CSS）
//    上传补充信息图片：addSupplementImg
//
//----（20241203新增）弹出卡片时触发，当卡片未提交时不允许重复触发:
//    催促上门：clickUrge
//    修改上门时间：clickChangeTime
//    补充信息：clickSupplement
//    取消服务：clickCancelService

    /**
     * 工单卡片操作事件，需要记录到聊天记录
     * @param cssCmd
     * @return
     */
    private String getClickCmdMsg2(String cssCmd){
        String clickBtnMsg;
        switch (cssCmd){
            case "getProgress":
                clickBtnMsg = "工单进度";
                break;
            case "clickUrge":
                clickBtnMsg = "催促上门";
                break;
            case "contactWorker":
                clickBtnMsg = "联系售后";
                break;
            case "clickSupplement":
                clickBtnMsg = "补充信息";
                break;
            case "clickCancelService":
                clickBtnMsg = "取消服务";
                break;
            case "clickChangeTime":
                clickBtnMsg = "修改时间";
                break;
            default:
                clickBtnMsg = "";
                break;
        }
        return clickBtnMsg;
    }

    @Override
    protected String getResId() {
        return null;
    }

    /**
     * 分页查询聊天记录
     * @param sql
     * @param params
     * @param rowMapper
     * @param busiParam
     * @return
     * @throws Exception
     */
    protected List<?> queryRecordPageList(String sql, Object[] params, EasyRowMapper<?> rowMapper, JSONObject busiParam) throws Exception{
        long tid = Thread.currentThread().getId();
        long startTime = System.currentTimeMillis();
        if (rowMapper == null) rowMapper = new MapRowMapperImpl();
        int pageIndex,pageSize,pageType;
        String sessionId = busiParam.getString("sessionId");
        List<?> list;
        try {
            pageType = busiParam.getIntValue("pageType");
            pageType = pageType == 0 ? 1 : pageType;
            pageIndex = busiParam.getIntValue("pageIndex");
//            pageSize = busiParam.getIntValue("pageSize");
//            pageSize = pageSize == 0 ? 20 : pageSize;
            pageSize = 20;//写死每页20条。
            list = getQuery().queryForList(sql, params,pageIndex,pageSize,rowMapper);
            long excTime = System.currentTimeMillis() - startTime;
            if(excTime > 1000){
                MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() sessionId[" + sessionId + "] query before sql: " + sql + ", param: " + JSONObject.toJSONString(params));
            }
            MediagwIntefaceLogger.getLogger().info("Thread[" + tid + "] actionForLoadChatRecord() sessionId[" + sessionId + "] query after,time:" + excTime + " ms");
        }catch (SQLException ex) {
            throw new SQLException(ex);
        }
        return list;
    }
}
