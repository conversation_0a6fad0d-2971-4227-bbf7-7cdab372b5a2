package com.yunqu.yc.mediagw.command;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.model.VisitorModel;
import com.yunqu.yc.mediagw.util.MediaCacheUtil;
import org.apache.commons.lang3.StringUtils;


/**
 
{
	"userInfo": {
		"channelID": "84758078982319999715571",
		"city": "广州",
		"country": "中国",
		"custID": "ovyxRxETudAP6FRRvhQ-NlL40CB4",
		"headImgPath": "/iccresource/res/icc_file/iccmsg/im/20180424/CB4/ovyxRxETudAP6FRRvhQ-NlL40CB4/resources/0.jpg",
		"nickName": "小平头",
		"province": "广东",
		"sex": 1,
		"vipFlag": 0
	},
	"msgType": "event",
	"msgContent": "zxkf",
	"msgId": "f1b7a9533d5babf8d0a1fc0b0393d6a7",
	"channelType": 2,
	"sessionId": "ovyxRxETudAP6FRRvhQ-NlL40CB4",
	"callbackService": "ICC-MSG-PROCESSOR-SERVICE",
	"channelId": "gh_e04bca23d882",
	"channelKey": "1",
	"command": "requestService"
}
}
 
 
 */


public abstract class Command {
	

	/**
	 * 当前会话ID
	 */
	private String sessionId ;
	
	private String agentId;
	
	public abstract void handleCommand(JSONObject commandJson) throws Exception;
	
	public void service(JSONObject commandJson) throws Exception{
		this.sessionId 	 = commandJson.getJSONObject("data").getString("sessionId");
		//获得当前用户的服务坐席ID
		this.agentId = MediaCacheUtil.get("MEDIA_USER_AGENT_"+sessionId);
		if(StringUtils.isNotBlank(agentId)) {
			commandJson.put("agentId", agentId);
		}else {
			//1.01#20191220 lijianping 避免在memcache中找不到agentId，从当前内存中获取
			VisitorModel visitorModel = VisitorInfos.getInstance().getVisitorModel(sessionId);
			if(visitorModel!=null) {
				String agentId2 = visitorModel.getAgentId();
				if(StringUtils.isNotBlank(agentId2)) {
					commandJson.put("agentId", agentId);
					this.agentId = agentId2;
//					CacheManager.getMemcache().put("MEDIA_USER_AGENT_"+sessionId, agentId2);
				}
			}
		}
		this.handleCommand(commandJson);
	}

	
	public String getSessionId() {
		return sessionId;
	}

	public String getAgentId(){
		return this.agentId;
	}
}
