package com.yunqu.yc.mediagw.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.Constants;
import com.yunqu.yc.mediagw.log.MediagwLogger;
import com.yunqu.yc.mediagw.log.VisitorModelStateLogger;
import com.yunqu.yc.mediagw.util.CacheUtil;
import com.yunqu.yc.mediagw.util.MediaCacheUtil;
import org.easitline.common.utils.string.StringUtils;

public class VisitorModel{

	private String sessionId;
	/**
	 * 坐席id，成功接入到坐席才会绑定对应的坐席id
	 */
	private String agentId;
	
	private String agentName;
	
	private String agentPhone;
	
	private String agentNickName;
	
	private String bizType;//会话事件，robot 机器人  agent 人工  satisfy 满意度  word 留言  end 会话结束
	
	private long   updateTime;
	
	private JSONObject userInfo;
	
	private String callbackUrl ;
	
	private String channelKey;
	
	private String entId;
	
	private String channelId;
	
	private String channelType;
	
	private String seq;
	
	private String brokerName;
	
	private String chatSessionId;
	
	private String selectKey;
	
	private JSONObject data;

	//是否已评价人工会话满意度
	private boolean isSatisfy = false;

	//坐席回复消息数
	private int agentChatCount = 0;

	//机器人回复消息数
	private int robotChatCount = 0;
	//是否已评价机器人会话满意度
	private boolean isSatisfyRobot = false;

	//转人工成功后消息数
	private int zxkfUserChatCount = 0;

	public String getSelectKey() {
		return selectKey;
	}

	public void setSelectKey(String selectKey) {
		this.selectKey = selectKey;
	}

	public String getChatSessionId() {
		return chatSessionId;
	}

	public void setChatSessionId(String chatSessionId) {
		this.chatSessionId = chatSessionId;
		this.cache();
	}

	private long lastChatTime;//最后会话时间

	public String getBrokerName() {
		return brokerName;
	}

	public void setBrokerName(String brokerName) {
		this.brokerName = brokerName;
	}

	public String getSeq() {
		return seq;
	}

	public void setSeq(String seq) {
		this.seq = seq;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public String getChannelId() {
		return channelId;
	}

	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}

	public String getChannelKey() {
		return channelKey;
	}

	public void setChannelKey(String channelKey) {
		this.channelKey = channelKey;
	}

	public String getEntId() {
		return entId;
	}

	public void setEntId(String entId) {
		this.entId = entId;
	}

	public String getCallbackUrl() {
		return callbackUrl;
	}

	public void setCallbackUrl(String callbackUrl) {
		this.callbackUrl = callbackUrl;
	}

	public String getSessionId() {
		return sessionId;
	}

	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}

	public String getBizType() {
		return bizType;
	}

	public synchronized void setBizType(String _bizType) {
		VisitorModelStateLogger.getLogger().info("VisitorModel<"+sessionId+">.bizType<"+this.bizType+"> is change to <"+_bizType+">,lastChatTime<"+this.lastChatTime+">threadId:"+Thread.currentThread().getId());
		//1.01#20210527-1
		if("zxkf".equals(this.bizType)) {
			if("selectKey".equals(_bizType)
					||"robot".equals(_bizType)
							||"queue".equals(_bizType)
									||"getAgent".equals(_bizType)) {
				MediagwLogger.getLogger().error("VisitorModel["+this.sessionId+"]setBizType() 设置会话状态["+_bizType+"]异常---->当前状态[zxkf],threadId:"+Thread.currentThread().getId());
				return;
			}
		}
		if("welcome".equals(_bizType)){
			this.setSatisfy(false);
			this.setAgentChatCount(0);
			this.setSatisfyRobot(false);
			this.setRobotChatCount(0);
			this.setZxkfUserChatCount(0);
		}

		this.bizType = _bizType;
		this.cache();
	}

	public long getUpdateTime() {
		return updateTime;
	}

	public void setUpdateTime(long updateTime) {
		this.updateTime = updateTime;
	}

	
	public JSONObject getUserInfo() {
		return userInfo;
	}
	/**
	 * 获取简单的userInfo
	 * @Description :获取简单userInfo，传输过程中移除非必要数据，避免内容过大导致无法写入数据库
	 * <AUTHOR>
	 * @Datetime 2021/9/29 20:41
	 * @return: com.alibaba.fastjson.JSONObject
	 */
	public JSONObject getSimpleUserInfo() {
		JSONObject simpleObj = new JSONObject();
		if(userInfo==null){
			return simpleObj;
		}
		simpleObj.putAll(userInfo);
		simpleObj.remove("tags");//移除用户标签
		return simpleObj;
	}

	public void setUserInfo(JSONObject userInfo) {
		this.userInfo = userInfo;
	}

	public String toString(){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sessionId", this.getSessionId());
		jsonObject.put("bizType", this.getBizType());
		jsonObject.put("brokerName", this.getBrokerName());
		jsonObject.put("callbackUrl", this.getCallbackUrl());
		jsonObject.put("chatSessionId", this.getChatSessionId());
		return  jsonObject.toJSONString();
	}
	
	public String toJSONString(){
		return  JSONObject.toJSONString(this);
	}
	public String toSimpleJSONString(){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("sessionId", this.getSessionId());
		jsonObject.put("chatSessionId", this.getChatSessionId());
		jsonObject.put("bizType", this.getBizType());
		jsonObject.put("brokerName", this.getBrokerName());
		jsonObject.put("callbackUrl", this.getCallbackUrl());
		jsonObject.put("userInfo", this.getSimpleUserInfo());
		jsonObject.put("entId", this.getEntId());
		jsonObject.put("channelId", this.getChannelId());
		jsonObject.put("channelKey", this.getChannelKey());
		jsonObject.put("agentId", this.getAgentId());
		jsonObject.put("agentName", this.getAgentName());
		jsonObject.put("agentPhone", this.getAgentPhone());
		jsonObject.put("agentNickName", this.getAgentNickName());
		jsonObject.put("updateTime", this.getAgentNickName());
		jsonObject.put("seg", this.getSeq());
		return jsonObject.toJSONString();
	}

	public JSONObject getData() {
		return data;
	}

	public void setData(JSONObject data) {
		this.data = data;
	}

	public long getLastChatTime() {
		return lastChatTime;
	}

	public void setLastChatTime(long lastChatTime) {
		this.lastChatTime = lastChatTime;
	}
	public String getAgentId() {
		return agentId;
	}

	public String getAgentName() {
		return agentName;
	}

	public String getAgentPhone() {
		return agentPhone;
	}

	public String getAgentNickName() {
		return agentNickName;
	}

	public void setAgent(JSONObject data) {
		this.agentId = data.getString("agentId");
		this.agentName = data.getString("agentName");
		this.agentPhone = data.getString("agentPhone");
		this.agentNickName = data.getString("agentNickName");
	}

	public boolean isSatisfy() {
		return isSatisfy;
	}

	public void setSatisfy(boolean satisfy) {
		isSatisfy = satisfy;
	}

	public int getAgentChatCount() {
		return agentChatCount;
	}

	@Deprecated
	public void setAgentChatCount(int agentChatCount) {
		this.agentChatCount = agentChatCount;
	}

	public void addAgentChatCount() {
		this.agentChatCount++;
	}

	public int getRobotChatCount() {
		return robotChatCount;
	}

	public void setRobotChatCount(int robotChatCount) {
		this.robotChatCount = robotChatCount;
	}

	public void addRobotChatCount() {
		this.robotChatCount++;
	}

	public boolean isSatisfyRobot() {
		return isSatisfyRobot;
	}

	public void setSatisfyRobot(boolean satisfyRobot) {
		isSatisfyRobot = satisfyRobot;
	}


	public int getZxkfUserChatCount() {
		return zxkfUserChatCount;
	}

	public void setZxkfUserChatCount(int zxkfUserChatCount) {
		this.zxkfUserChatCount = zxkfUserChatCount;
	}

	public void addZxkfUserChatCount() {
		this.zxkfUserChatCount++;
	}


	/**
	 * 清除坐席信息
	 * @Description :人工会话结束时，清空坐席信息
	 * <AUTHOR>
	 * @Datetime 2021/10/8 11:49
	 * @return: void
	 */
	public void clearAgent() {
		this.agentId = null;
		this.agentName = null;
		this.agentPhone = null;
		this.agentNickName = null;
	}

	/**
	 * 清除按键信息
	 * @Description :会话状态重置为初始状态时（bizType=welcome），清除接入时绑定的按键信息
	 * <AUTHOR>
	 * @Datetime 2021/10/8 11:50

	 * @return: void
	 */
	public void clearSelectKey(){
		this.selectKey = "";
		this.getUserInfo().remove("keyCode");
		this.getUserInfo().remove("keyName");
		//清理进入按键流程标识
		CacheUtil.delete("intoSelectKeyFlag:"+sessionId);
	}

	public synchronized void cache(){
		if(StringUtils.isNotBlank(sessionId)){
//			long startmills = System.currentTimeMillis();
//			VisitorInfosLogger.getLogger().info("VisitorModel.cache("+sessionId+") start");
//			CacheUtil.put(Constants.CACHE_VISITOR_NAME+sessionId, this.toJSONString(), Constants.getVisitorCacheTimeOut());
			MediaCacheUtil.put(Constants.CACHE_VISITOR_NAME+sessionId, this.toJSONString(), Constants.getVisitorCacheTimeOut());
//			VisitorInfosLogger.getLogger().info("VisitorModel.cache("+sessionId+") finish,time:"+(System.currentTimeMillis()-startmills)+" ms");
		}
	}
}
