package com.yunqu.yc.center.dao.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;

/**
 * 渠道会员身份回复语配置DAO
 * <AUTHOR>
 */
@WebObject(name="channelMemberAutoConfig")
public class ChannelMemberAutoConfigDao extends AppDaoContext {

    /**
     * 获取渠道会员身份回复语配置列表
     */
    @WebControl(name="list", type=Types.LIST)
    public JSONObject list(){
        String channelKey = param.getString("channelKey");
        String confKey = param.getString("confKey");
        
        EasySQL sql = new EasySQL("select * from YCUSER.CC_CHANNEL_MEMBER_AUTOCONF where 1=1");
        sql.append(channelKey, " and CHANNEL_KEY = ?");
        sql.append(confKey, " and CONF_KEY = ?");
        sql.append(" order by CREATE_TIME desc");
        
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 获取渠道会员身份回复语配置（用于加载）
     */
    @WebControl(name="getConfig", type=Types.LIST)
    public JSONObject getConfig(){
        String channelKey = param.getString("channelKey");
        String confKey = param.getString("confKey");
        
        EasySQL sql = new EasySQL("select MEMBER_ID, TXT_CONTENT from YCUSER.CC_CHANNEL_MEMBER_AUTOCONF where 1=1");
        sql.append(channelKey, " and CHANNEL_KEY = ?");
        sql.append(confKey, " and CONF_KEY = ?");
        sql.append(" order by CREATE_TIME desc");
        
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 会员等级字典
     */
    @WebControl(name="memberDict", type=Types.DICT)
    public JSONObject memberDict(){
        return getDictByQuery(
            "select MEMBER_ID, MEMBER_NAME from YCUSER.CC_MEDIA_MEMBER_CONFIG order by RANK_INDEX desc",
            new Object[]{}
        );
    }
}
