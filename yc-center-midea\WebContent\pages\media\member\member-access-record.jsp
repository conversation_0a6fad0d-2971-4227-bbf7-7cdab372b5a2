<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
    <title>进线接入记录</title>
    <style>
        .table td {
            vertical-align: middle !important;
        }
        .search-form .form-group {
            margin-right: 10px;
            margin-bottom: 10px;
        }
        .search-form .input-group {
            width: 200px;
        }
    </style>
</EasyTag:override>
<EasyTag:override name="content">
    <form action="" method="post" name="searchForm" class="form-inline search-form" id="searchForm" data-toggle="render">
        <input type="hidden" name="entId" value="${param.entId}">
        <div class="ibox">
            <div class="ibox-title clearfix">
                <div class="form-group">
                    <h5>进线接入记录</h5>
                </div>
            </div>
            <div class="ibox-content">
                <div class="row">
                    <div class="col-sm-12">
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">会员身份</span>
                                <select name="configId" class="form-control" data-mars="memberAccessRecord.memberStrategyDict">
                                    <option value="">全部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">会员名称</span>
                                <input type="text" name="memberName" class="form-control" placeholder="请输入身份名称">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">进线时间</span>
                                <input type="text" name="startTime" class="form-control" placeholder="开始时间" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">至</span>
                                <input type="text" name="endTime" class="form-control" placeholder="结束时间" onclick="WdatePicker({dateFmt:'yyyy-MM-dd HH:mm:ss'})">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">访客标识</span>
                                <input type="text" name="sessionId" class="form-control" placeholder="请输入访客标识">
                            </div>
                        </div>
                        <div class="form-group">
                            <div class="input-group input-group-sm">
                                <span class="input-group-addon">渠道</span>
                                <select name="channelKey" class="form-control" data-mars="channel.channelDict">
                                    <option value="">全部</option>
                                </select>
                            </div>
                        </div>
                        <div class="form-group">
                            <button type="button" class="btn btn-sm btn-default" onclick="MemberAccessRecord.searchData()">
                                <span class="glyphicon glyphicon-search"></span> 搜索
                            </button>
                            <button type="button" class="btn btn-sm btn-success" onclick="MemberAccessRecord.exportData()">
                                <span class="glyphicon glyphicon-export"></span> 导出Excel
                            </button>
                        </div>
                    </div>
                </div>
                <br/>
                <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="5" id="tableHead" data-mars="memberAccessRecord.list">
                    <thead>
                        <tr>
                            <th>访客标识</th>
                            <th>渠道名称</th>
                            <th>匹配身份</th>
                            <th>进线时间</th>
                            <th>访客身份</th>
                        </tr>
                    </thead>
                    <tbody id="dataList">
                    </tbody>
                </table>
                <script id="list-template" type="text/x-jsrender">
                    {{for list}}
                        <tr>
                            <td>{{:SESSION_ID}}</td>
                            <td>{{:CHANNEL_NAME || '-'}}</td>
                            <td>{{:MEMBER_NAME || '-'}}</td>
                            <td>{{:CREATE_TIME}}</td>
                            <td>{{:LEVEL_NAMES || '-'}}</td>
                        </tr>
                    {{/for}}
                </script>
                <div class="row paginate">
                    <jsp:include page="/pages/common/pagination.jsp"/>
                </div>
            </div>
        </div>
    </form>
</EasyTag:override>

<EasyTag:override name="script">
    <script type="text/javascript">
        jQuery.namespace("MemberAccessRecord");
        var entId = '${param.entId}';
        
        MemberAccessRecord.searchData = function(){
            $("#searchForm").searchData();
        }

        MemberAccessRecord.exportData = function(){
            var data = form.getJSONObject("#searchForm");
            data.entId = entId;

            // 构建导出URL
            var url = "${ctxPath}/servlet/export?action=memberAccessRecord";
            var params = [];
            for(var key in data) {
                if(data[key] !== null && data[key] !== undefined && data[key] !== '') {
                    params.push(key + "=" + encodeURIComponent(data[key]));
                }
            }
            if(params.length > 0) {
                url += "?" + params.join("&");
            }

            // 直接跳转下载
            window.open(url, '_blank');
        }

        $(function(){
            $("#searchForm").render();
        });
    </script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>
