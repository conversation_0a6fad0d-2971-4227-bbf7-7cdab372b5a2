<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>自动回复语配置</title>
	<link type="text/css" rel="stylesheet" href="layui.css" />
	<style>
	    .blank-page{padding: 20px 40px;overflow-y: hidden;border: solid 1px #cccccc; background-color: #ffffff;margin: 20px auto;box-shadow: -2px -2px 7px #cccccc;width:890px;margin-bottom: 100px}
        .blank-page .header-title{text-align:center;color:#333;font-size:19px}
        .blank-page .p-title{border-bottom:1px solid #cccccc;font-size:16px;color:#555;margin-top:30px;margin-bottom:15px}
        .blank-page .p-title>span{border-bottom:2px solid #00a0f0;padding:2px 6px}
       .layui-elem-field legend {
		    width: inherit;
		    border-bottom: none;
		    margin-bottom: 0px;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
     <div class="blank-page">
     	<form id="autoConfigForm" class="form-horizontal" data-mars="channel.getAutoConfig" data-mars-prefix="autoReplyConfig.">
     		<input type="hidden" name="entId" value="${param.entId}">
			<input type="hidden" name="channelId" value="${param.channelId}">
			<h4 class="header-title">自动回复语配置</h4>
			<table class="table table-vzebra">
		        <tbody>
		            <tr>
		               	<td class="required">系统欢迎语</td>
		               	<td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.WELCOME_MSG" placeholder="请填写内容 ...">输入序号选择要服务的产品：
							[1]家用空调
							[2]商用空调
							[3]冰箱
							[4]洗衣机
							[5]热水器
							</textarea>
		               	</td>
		            </tr>
		            <tr>
		               	<td class="required">客服回复超时时间</td>
		               	<td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.AGENT_TIMEOUT" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)">
		               		<span>分钟&nbsp;&nbsp;当客服在此时间内(系统时延15秒内)未回复，系统自动发送客服提示语。</span>
		               	</td>
		            </tr>
		            <tr>
		               	<td>客服回复超时提示语</td>
		               	<td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.AGENT_TIMEOUT_MSG" placeholder="请填写内容 ...">我现在临时有事需要离开电脑前，您可以先浏览一下网站，或者留下您的联系方式等我回复，给您带来的不便请多谅解。 </textarea>
		               	</td>
		            </tr>
		            <tr>
		               	<td class="required">访客回复超时时间</td>
		               	<td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.VISITOR_TIMEOUT" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;当访客在此时间内(系统时延15秒内)未回复，系统自动发送系统询问提示语。</span>
		               	</td>
		            </tr>
		            <tr>
		               	<td class="required">访客回复超时提示语</td>
		               	<td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.VISITOR_TIMEOUT_MSG" placeholder="请填写内容 ...">您好，请问您还在线吗？如随后#time#分钟仍未收到您的消息，本次会话将暂时结束；如您的问题已解决，输入88可结束本次会话。 </textarea>
		               	</td>
		            </tr>
		            <%--<tr class="hidden">
		                <td>开启自动关闭对话功能</td>
		               	<td colspan="8">
		               	    <div class="label-item">
		               	    	<label class="checkbox checkbox-success checkbox-inline">
		               	    		<input type="checkbox" name="autoReplyConfig.AUTO_CLOSESESSION_FLAG"><span> </span>
		               	    	</label>
		               	    </div>
		               	</td>
		            </tr>--%>
		            <tr>
		               	<td class="required">自动关闭对话时间</td>
		               	<td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.AUTO_CLOSESESSION_TIMEOUT" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;访客在此时间内未回复坐席消息(系统时延15秒内)，系统自动关闭对话</span>
		               	</td>
		            </tr>
		            <tr>
		               	<td class="required">自动关闭对话提示语</td>
		               	<td colspan="8">
		               		<textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.AUTO_CLOSESESSION_MSG" placeholder="请填写内容 ...">系统自动关闭对话 </textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">访客接入排队时，系统自动回复内容
		                </br><a href="JavaScript:return false;"  onclick="AutoConfig.updateVisitorAccessMsgList('visitorQueueMsgList','您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。')">更多</a>
		                </br><a href="JavaScript:return false;"  onclick="AutoConfig.updateMemberAccessMsgList('VISITOR_QUEUE_MSG','您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。')">会员身份配置</a>
		                </td>
		                <td colspan="8">
		                	<input  type="text" name="autoReplyConfig.VISITOR_QUEUE_MSG_LIST" value="" id="visitorQueueMsgList"  class="form-control input-sm hidden" >
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.VISITOR_QUEUE_MSG" placeholder="请填写内容 ...">您好，客服繁忙，您当前的排队号为#sortPos#,输入88可取消当前排队。 </textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">访客接入排队后提示语</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.IN_QUEUE_MSG" placeholder="请填写内容 ...">排队过程中，您可先提前输入您的问题或需求</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>访客正在排队，输入除88以外的其他内容，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.VISITOR_QUEUE_IN_MSG" placeholder="请填写内容 ...">您当前正在排队，排队号为#sortPos#，请耐心等待，输入88可取消当前排队。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">访客取消排队时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.VISITOR_CANCELQUE_MSG" placeholder="请填写内容 ...">您好，由于当前系统排队人数过多，给您带来的不便，敬请原谅，请稍后再试。 </textarea>
		               	</td>
		            </tr>
		            <%--<tr>
		                <td>访客排队通知</td>
		                <td colspan="8">
			               	<span>当访客排队号在</span>
			               	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_MIN_NO" value="5" data-rules="required|digits" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
			               	<span>到</span>
			               	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_MAX_NO" value="10" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
			               	<span>之间时，系统自动回复：</span>
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.QUEUE_CONFIRM_MSG" placeholder=" ...">请问您是否还需要客服MM为您服务，如果需要请输入并发送“是”</textarea>
		               	 	<span>访客在</span>
		               	 	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_CONFIRM_TIMEOUT" value="1" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟(系统时延15秒内)内未回复“是”，则结束排队</span>
		               	</td>
		            </tr>
		            <tr>
		                <td>访客排队人数到达上限时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.QUEUE_FULL_MSG" placeholder="请填写内容 ...">您好，当前排队人数已达系统极限，请稍后再试。</textarea>
		               	</td>
		            </tr>--%>
		            <tr>
		                <td class="required">访客成功接入客服时，系统自动回复内容
		                  </br><a href="JavaScript:return false;"  onclick="AutoConfig.updateVisitorAccessMsgList('visitorAccessMsgList','您好，我是客服#agentName#，请问有什么可以帮您的？输入88可结束本次会话。')">更多</a>
		                </td>
		                <td colspan="8">
		                	<input type="text" name="autoReplyConfig.VISITOR_ACCESS_MSG_LIST" value="" id="visitorAccessMsgList"  class="form-control input-sm hidden" >
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.VISITOR_ACCESS_MSG" placeholder="请填写内容 ...">您好，我是客服#agentName#，请问有什么可以帮您的？输入88可结束本次会话。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>人工排队中点击机器消息提示语</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.QUEUE_OPER_MSG" placeholder="请填写内容 ...">若需要与智能客服交互，您可点击“退出排队”退出人工排队</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>访客接入人工客服后系统回复内容</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.IN_AGENT_AFTER_MSG" placeholder="请填写内容 ...">这里是人工客服，请问有什么可以帮到您？</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">客服繁忙，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.AGENT_BUSY_MSG" placeholder="客服置忙中，系统自动回复内容">您好，当前客服繁忙，请稍后再试。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">客服服务人数达上限，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.AGENT_MAX_SERVER_MSG" placeholder="访客输入客服工号或邀请码接入指定客服，客服服务人数达上限，系统自动回复内容">您好，当前客服服务人数过多，请稍后再试。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">无客服在线时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.AGENT_OFFLINE_MSG" placeholder="请填写内容 ...">您好，当前无客服在线，请稍后再试。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td class="required">访客排队超时时间</td>
		                <td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.VISITOR_QUEUE_TIMEOUT" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;当访客排队超过此时间(系统时延15秒内)，系统自动发送超时提示语</span>
		               	</td>
		            </tr>
		            <tr>
		                <td>访客排队超时提示语</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.QUEUE_TIMEOUT_MSG" placeholder="请填写内容 ...">您好，当前排队已超时，请稍后再试。您可以写下留言，我们会尽快跟进处理，输入88结束留言。 </textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>访客排队超时，自动进入留言</td>
		               	<td colspan="8">
							<label class="radio-inline">
								<input type="radio" checked="checked" name="autoReplyConfig.TIMEOUT_INTO_WORD_FLAG" value="1"> 开启
							</label>
							<label class="radio-inline">
								<input type="radio" name="autoReplyConfig.TIMEOUT_INTO_WORD_FLAG" value="0"> 关闭
							</label>
		               	</td>
		            </tr>
		            <tr>
		                <td>非客服工作时间，开启留言功能</td>
		               	<td colspan="8">
							<label class="radio-inline">
								<input type="radio" checked="checked" name="autoReplyConfig.IS_WORD_FLAG" value="1"> 开启
							</label>
							<label class="radio-inline">
								<input type="radio" name="autoReplyConfig.IS_WORD_FLAG" value="0"> 关闭
							</label>
		               	</td>
		            </tr>
		            <%--<tr>
		                <td>留言表单地址</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.WORD_FORM_URL" placeholder="请填写留言地址 ，用于H5客户端展示留言表单"></textarea>
		               	</td>
		            </tr>--%>
		            <tr>
		                <td>留言超时时间</td>
		                <td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.WORD_TIME_OUT_TIME" value="5" data-rules="required|digits" placeholder="留言超时时间小于0，不进入留言，直接结束会话" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;当访客最后留言超过此时间内(系统时延15秒内)，系统自动发送留言超时回复语</span>
		               	</td>
		            </tr>
		            <tr>
		                <td>留言超时回复语</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.WORD_TIME_OUT_MSG" placeholder="请填写内容 ...">您好，当前留言已超时，感谢你的留言。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>非客服工作时间时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.SYSTEM_NOTINSERVICE_MSG" placeholder="请填写内容 ...">您好，当前为系统非工作时间，服务时间为#serviceTime#，您可以写下留言，我们会尽快跟进处理，输入88结束留言。  </textarea>
		               	</td>
		            </tr>
		            
		            <tr>
		                <td class="required">会话结束时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.GOODBYE_MSG" placeholder="请填写内容 ...">您好，本次会话结束，感谢您使用我们的系统，再见。 </textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>机器人欢迎语
		                <br><a href="javascript:;"  onclick="AutoConfig.updateVisitorAccessRobotMsg('visitorAccessRobotMsg','请一句话描述您的问题，我们来帮您解决并转到合适的人工服务。')">更多</a>
		                </td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" id="visitorAccessRobotMsg" name="autoReplyConfig.VISITOR_ACCESS_ROBOT_MSG" placeholder="请填写内容 ...">请一句话描述您的问题，我们来帮您解决并转到合适的人工服务。</textarea>
		               	</td>
		            </tr>
		            <tr>
		                <td>机器人超时时间</td>
		                <td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.ROBOT_TIME_OUT_TIME" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;当访客与机器人会话超过此时间内(系统时延15秒内)，系统自动发送超时回复语</span>
		               	</td>
		            </tr>
		            <tr>
		                <td>机器人会话超时回复语</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.ROBOT_TIME_OUT_MSG" placeholder="请填写内容 ...">您好，当前机器人会话已超时，感谢你的咨询。</textarea>
		               	</td>
		            </tr>

					<tr>
		                <td class="required">满意度推送提示语</td>
		                <td colspan="8">
		               	    <textarea data-rules="required" rows="3" class="form-control input-sm" name="autoReplyConfig.SATISFY_PUSH_MSG" placeholder="请填写内容 ...">为了我们更好服务，请您对我们的服务进行评价</textarea>
		               	</td>
		            </tr>
		            
		            <tr>
		                <td>满意度回复超时时间</td>
		                <td colspan="8">
		               		<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.SATISFY_TIME_OUT_TIME" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();">
		               		<span>分钟&nbsp;&nbsp;当访客未回复满意度过此时间内(系统时延15秒内)，系统自动发送满意度超时回复语</span>
		               	</td>
		            </tr>
		            <tr>
		                <td>满意度超时回复语</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.SATISFY_TIME_OUT_MSG" placeholder="请填写内容 ...">您好，本次会话结束，感谢您使用我们的系统，再见。 </textarea>
		               	</td>
		            </tr>
		            
		            <tr>
		                <td>白名单自动转人工</td>
		               	<td colspan="8">
		               	    <div class="label-item">
		               	    	<label class="checkbox checkbox-success checkbox-inline">
		               	    		<input type="checkbox" name="autoReplyConfig.WHITE_LIST_IN_AGENT_FLAG"><span> </span>
		               	    	</label>
		               	    </div>
		               	</td>
		            </tr>
		            
		            <%--<tr>
		                <td>访客排队号通知间隔时间</td>
		               	<td colspan="8">
	               	    	<input style="display: inline-block; width:60px" type="text" name="autoReplyConfig.QUEUE_NOTIFY_TIME" value="5" data-rules="required|digits" placeholder="分钟" class="form-control input-sm" onkeyup="(this.v=function(){this.value=this.value.replace(/[^0-9-]+/,'');}).call(this)" onblur="this.v();"> 分钟
		               	</td>
		            </tr>--%>
		            <tr>
		                <td>访客属于红黑名单时，系统自动回复内容</td>
		                <td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.BLACKLIST_MSG" placeholder="请填写内容 ...">您好，当前系统不方便接入，请稍后再试 。</textarea>
		               	</td>
		            </tr>
					
					<tr><td>接入视频客服时,工作时间1</td>
					    <td><input type="text" style="display: inline-block; width:100px" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'webchatEnd1\')}'})" value="08:30" id="webchatStart1" name="autoReplyConfig.VIDEO_BEGIN_WORK_TIME1" class="form-control input-sm Wdate"> - <input type="text" style="display: inline-block; width:100px" onClick="WdatePicker({dateFmt:'HH:mm',minDate:'#F{$dp.$D(\'webchatStart1\')}'})" value="12:00" id="webchatEnd1"   name="autoReplyConfig.VIDEO_END_WORK_TIME1" class="form-control input-sm Wdate"></td>
					</tr>
					<tr><td>接入视频客服时,工作时间2</td>
					   <td><input type="text" style="display: inline-block; width:100px" onClick="WdatePicker({dateFmt:'HH:mm',maxDate:'#F{$dp.$D(\'webchatEnd2\')}'})" value="13:30" id="webchatStart2" name="autoReplyConfig.VIDEO_BEGIN_WORK_TIME2" class="form-control input-sm Wdate"> - <input type="text" style="display: inline-block; width:100px" onClick="WdatePicker({dateFmt:'HH:mm',minDate:'#F{$dp.$D(\'webchatStart2\')}'})" value="22:30" id="webchatEnd2" name="autoReplyConfig.VIDEO_END_WORK_TIME2" class="form-control input-sm Wdate"></td>
					</tr>
					<tr>
					  <td>接入视频客服时,非工作时间提示语</td>
						<td colspan="8">
		               	    <textarea rows="3" class="form-control input-sm" name="autoReplyConfig.VIDEO_NOTINSERVICE_MSG" placeholder="请填写内容 ...">您好，当前非视频客服接入时间，您可以写下留言，我们会尽快跟进处理，输入88结束留言。 。</textarea>
		               	</td>
					</tr>
		        </tbody>
		    </table>
		    <p class="text-c" style="margin-top:50px">
			      <button class="btn btn-sm btn-primary" id="saveChannelConfig" type="button" onclick="AutoConfig.update()">保存</button>
				  <button class="btn btn-sm btn-default ml-20"  type="button" id="backbut" onclick="popup.closeTab();">关闭</button>
			</p>   
			
	    </form>
    </div> 
    <div></div>
    <script type="text/x-jsrender" id="visitorAccessMsgJs">
					<div class="layui-form-item">
						<div class="layui-inline">
							<div class="layui-input-inline" style="width:150px"> 
								<select class="form-control input-sm" style="width: 140px;display:block" name="index" >
	                      			<option value="vip1">vip1用户</option>
	                      			<option value="pro">PRO会员</option>
	                      			<option value="v6">钻石会员</option>
	                      			<option value="v5">铂金会员</option>
	                      			<option value="v4">黄金会员</option>
	                      			<option value="v3">白银会员</option>
	                      			<option value="v2">美的会员</option>
	                      			<option value="v1">普通会员</option>
	                      		</select>
							</div> 
							<div class="layui-input-inline" style="width:520px"> 
								<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." name="content">{{:content}}</textarea>
							</div> 
							<div class="layui-input-inline" style="margin-top: 5px;width:50px"> 
								<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteRuntime(this)" style="" type="button">删除</button> 
							</div> 
						</div>
					</div>
	</script>
    <script type="text/x-jsrender" id="visitorAccessMsgListJs">
		<form id="updateForm"    method="post"  autocomplete="off" >
		<fieldset class="layui-elem-field layui-form">
  				<legend>
  				<span >系统自动回复内容</span>
  				<button class="btn btn-xs btn-info" type="button" onclick="AutoConfig.addRuntime('{{:content}}')"
  							style="">新增</button>
  				</legend>
				<div class="layui-field-box" id="runtimeDiv">
					{{for data}}
      				<div class="layui-form-item"> 
						<div class="layui-inline"> 
							<div class="layui-input-inline" style="width:150px"> 
								<select class="form-control input-sm" style="width: 140px;display:block" name="index" >
									<option value="vip1" {{if code=="vip1"}}selected = "selected"{{/if}}>vip1用户</option>
	                      			<option value="pro" {{if code=="pro"}}selected = "selected"{{/if}}>PRO会员</option>
	                      			<option value="v6" {{if code=="v6"}}selected = "selected"{{/if}}>钻石会员</option>
	                      			<option value="v5" {{if code=="v5"}}selected = "selected"{{/if}}>铂金会员</option>
	                      			<option value="v4" {{if code=="v4"}}selected = "selected"{{/if}}>黄金会员</option>
	                      			<option value="v3" {{if code=="v3"}}selected = "selected"{{/if}}>白银会员</option>
	                      			<option value="v2" {{if code=="v2"}}selected = "selected"{{/if}}>美的会员</option>
	                      			<option value="v1" {{if code=="v1"}}selected = "selected"{{/if}}>普通会员</option>
	                      		</select>
							</div> 
							<div class="layui-input-inline" style="width:520px"> 
								<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." name="content">{{:content}}</textarea>
							</div> 
							<div class="layui-input-inline" style="margin-top: 5px;width:50px"> 
								<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteRuntime(this)" style="" type="button">删除</button> 
							</div> 
						</div>
					</div>
					{{/for}}
  				</div>
  			</fieldset>
		</form>
	</script>
	<script type="text/x-jsrender" id="robotWelcomeMsgJs">
	<form id="robotWelcome"  method="post"  autocomplete="off" >
		<fieldset class="layui-elem-field layui-form">
  			<legend>
  				<span>机器人回复内容</span>
  				<button class="btn btn-xs btn-info" type="button" onclick="AutoConfig.addRobotMsg('{{:content}}')">新增</button>
  			</legend>
			<div class="layui-field-box" id="robotMsgDiv">
				{{for data}}
      			<div class="layui-form-item"> 
					<div class="layui-inline"> 
						<div class="layui-input-inline" style="width:150px"> 
							<select class="form-control input-sm" style="width: 140px;display:block" onchange="AutoConfig.changeRobotMsg(this)">
								<option value="">请选择</option>
								<option value="1" {{if select=="1"}}selected = "selected"{{/if}}>早上</option>
								<option value="2" {{if select=="2"}}selected = "selected"{{/if}}>上午</option>
								<option value="3" {{if select=="3"}}selected = "selected"{{/if}}>中午</option>
								<option value="4" {{if select=="4"}}selected = "selected"{{/if}}>下午</option>
								<option value="5" {{if select=="5"}}selected = "selected"{{/if}}>晚上</option>
	                      	</select>
						</div> 
						<div class="layui-input-inline contentDiv" style="width:500px" > 
							<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." >{{:content}}</textarea>
						</div> 
						<div class="layui-input-inline" style="margin-top: 5px;width:50px"> 
							<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteRobotMsg(this)" style="" type="button">删除</button> 
						</div> 
					</div>
				</div>
				{{/for}}
  			</div>
  		</fieldset>
	</form>
	</script>
    <script type="text/x-jsrender" id="robotSelectJS">
      	<div class="layui-form-item"> 
			<div class="layui-inline"> 
				<div class="layui-input-inline" style="width:150px"> 
					<select class="form-control input-sm" style="width: 140px;display:block" onchange="AutoConfig.changeRobotMsg(this)" >
						<option value="">请选择</option>
						<option value="1">早上</option>
						<option value="2">上午</option>
						<option value="3">中午</option>
						<option value="4">下午</option>
						<option value="5">晚上</option>
					</select>
				</div> 
				<div class="layui-input-inline contentDiv" style="width:500px" > 
					<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." >{{:content}}</textarea>
				</div> 
				<div class="layui-input-inline" style="margin-top: 5px;width:50px"> 
					<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteRobotMsg(this)" style="" type="button">删除</button> 
				</div> 
			</div>
		</div>
	</script>
	<script type="text/x-jsrender" id="memberAccessMsgListJs">
		<form id="memberUpdateForm" method="post" autocomplete="off">
			<fieldset class="layui-elem-field layui-form">
				<legend>
					<span>会员身份回复语配置</span>
					<button class="btn btn-xs btn-info" type="button" onclick="AutoConfig.addMemberRuntime('{{:content}}')">新增</button>
				</legend>
				<div class="layui-field-box" id="memberRuntimeDiv">
					{{for data}}
					<div class="layui-form-item">
						<div class="layui-inline">
							<div class="layui-input-inline" style="width:150px">
								<select class="form-control input-sm" style="width: 140px;display:block" name="memberId" data-mars="channelMemberAutoConfig.memberDict">
									<option value="{{:MEMBER_ID}}" selected="selected">{{:MEMBER_NAME}}</option>
								</select>
							</div>
							<div class="layui-input-inline" style="width:520px">
								<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." name="content">{{:TXT_CONTENT}}</textarea>
							</div>
							<div class="layui-input-inline" style="margin-top: 5px;width:50px">
								<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteMemberRuntime(this)" type="button">删除</button>
							</div>
						</div>
					</div>
					{{/for}}
				</div>
			</fieldset>
		</form>
	</script>
	<script type="text/x-jsrender" id="memberAccessMsgJs">
		<div class="layui-form-item">
			<div class="layui-inline">
				<div class="layui-input-inline" style="width:150px">
					<select class="form-control input-sm" style="width: 140px;display:block" name="memberId" data-mars="channelMemberAutoConfig.memberDict">
						<option value="">请选择会员身份</option>
					</select>
				</div>
				<div class="layui-input-inline" style="width:520px">
					<textarea rows="3" class="form-control input-sm" placeholder="请填写内容 ..." name="content">{{:content}}</textarea>
				</div>
				<div class="layui-input-inline" style="margin-top: 5px;width:50px">
					<button class="btn btn-xs btn-danger" onclick="AutoConfig.deleteMemberRuntime(this)" type="button">删除</button>
				</div>
			</div>
		</div>
	</script>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript" src="/easitline-static/lib/My97DatePicker/WdatePicker.js"></script>
	<script type="text/javascript">
	jQuery.namespace("AutoConfig");
	AutoConfig.isFirst = false;
	AutoConfig.entId = "${param.entId}";
	AutoConfig.channelId = "${param.channelId}";
	AutoConfig.channelName = "${param.channelName}";
	var visitorAccessMsgList = $.templates("#visitorAccessMsgListJs");
	var visitorAccessMsg = $.templates("#visitorAccessMsgJs");
	var visitorAccessRobotMsg = $.templates("#robotWelcomeMsgJs");
	var robotSelect = $.templates("#robotSelectJS");
	var memberAccessMsgList = $.templates("#memberAccessMsgListJs");
	var memberAccessMsg = $.templates("#memberAccessMsgJs");
	//机器人欢迎语json
	var robotWelcomMsg = {};

	$(function(){
		$("#autoConfigForm").render({success:function(result){
			if($.isEmptyObject(result["channel.getAutoConfig"].data)||result["channel.getAutoConfig"].data.CHANNEL_AUTO_CONF==""){
				$("#saveChannelConfig").text("下一步");
				AutoConfig.isFirst = true;
			}else{
				var resp = result["channel.getAutoConfig"].data;
				for(let i in resp){
					if(i.indexOf("VISITOR_ACCESS_ROBOT_MSG")!=-1){
						robotWelcomMsg[i] = resp[i];
					}
				}
				$("#saveChannelConfig").text("保存");
				AutoConfig.isFirst = false;
			}
		}});  
	});
	AutoConfig.update = function() {
		var data = form.getJSONObject("#autoConfigForm");
 		//统一转义2.5.3#20210610-1
		for(var i in data){
			data[i] = encodeURIComponent(data[i])
		}
 		//机器人欢迎语写入
 		for(var i in robotWelcomMsg){
 			data[i] = robotWelcomMsg[i];
 		}
		ajax.remoteCall("${ctxPath}/servlet/channel?action=autoConfig",data,function(result) { 
			if(result.state == 1){
				layer.msg(result.msg,{icon: 1,time:1200},function(){
					if(AutoConfig.isFirst){
						popup.openTab({url:"${ctxPath}/pages/media/channel/channel-key-list.jsp",title:"按键配置管理（"+AutoConfig.channelName+"）",reload:true,data:{entId:AutoConfig.entId,channelId:AutoConfig.channelId,channelName:AutoConfig.channelName}});
						$("#saveChannelConfig").text("保存");
						AutoConfig.isFirst = false;
					}else{
						popup.openTab({url:'${ctxPath}/pages/media/channel/channel-list.jsp',title:'全媒体渠道配置管理',reload:true,data:{entId:AutoConfig.entId}});
					}
			    });
			}else{
				layer.alert(result.msg,{icon: 5});
			}
		});
	}
	
	AutoConfig.updateVisitorAccessMsgList = function(id,content) {
		var list=new Array();
		//2.5.3#20210517-1
		var dataStr = $("#"+id).val();
		if(dataStr!=""){
			var jsonArray = [];
			try{
				jsonArray=JSON.parse(dataStr);
			}catch (e) {
				dataStr = dataStr.replaceAll("'","\"");
				jsonArray=JSON.parse(dataStr);
			}
			for(var key in jsonArray){
				var json ={code:key,content:jsonArray[key]};
				list.push(json);
			}
		}
		var html="";
		var data={data:list,content:content};
		
		html=visitorAccessMsgList.render(data);
		layer.open({
			  type: 1,
			  skin: 'layui-layer-rim', //加上边框
			  area: ['820px', '540px'], //宽高
			  btn: ['确认', '取消'],
			  content: html,
			  yes: function(index) {
					var data = form.getJSONObject("#updateForm");
				  	var list=new Array();
				  	var j={}
				  	if(typeof(data["index"])=="string"){
						j[data["index"]]=data["content"];
				  	}else{
				  		for(var i in data["index"]){
							var key=data["index"][i];
							j[key] = data["content"][i];
						}
				  	}
				  	var text=JSON.stringify(j)
				  	if(text=="{}"){
				  		text="";
				  	}
				  	$("#"+id).val(JSON.stringify(j));
					
					layer.close(index)

				}
			});
	}
	AutoConfig.addRuntime = function(content){
		var newDom =$(AutoConfig.createRuntimeHTML(content));
		$("#runtimeDiv").append(newDom);
		param.renderRuntime(newDom.find('.dateSelect1').get(0),newDom.find('.dateSelect2').get(0),e,v);
	}
	AutoConfig.deleteRuntime = function(ele){
		$(ele).parent().parent().parent().remove();
		$("#runtimeDiv").find('.add').last().show();
	}
	AutoConfig.createRuntimeHTML=function(content){
		return visitorAccessMsg.render({content:content});
	}
	
	//robot welcome msg begin----
	AutoConfig.changeRobotMsg = function(var1){
		let value = $(var1).val();
		$(var1).parent().siblings(".layui-input-inline.contentDiv").children().attr("name","autoReplyConfig.VISITOR_ACCESS_ROBOT_MSG_" + value);
	}
	AutoConfig.addRobotMsg = function(content){
		var newDom =$(AutoConfig.createRobotHTML(content));
		$("#robotMsgDiv").append(newDom);
	}
	AutoConfig.deleteRobotMsg = function(ele){
		$(ele).parent().parent().parent().remove();
		$("#robotMsgDiv").find('.add').last().show();
	}
	AutoConfig.createRobotHTML=function(content){
		return robotSelect.render({content:content});
	}
	AutoConfig.updateVisitorAccessRobotMsg = function(id,content) {
		var list = [];
		if(!$.isEmptyObject(robotWelcomMsg)){
			for(let i in robotWelcomMsg){
				var name = i;
				var obj = {};
				if(isNumber(name.substring(name.length-1))){
					obj.select = name.substring(name.length-1);
					obj.content = robotWelcomMsg[name];
					list.push(obj);
				}
			}
		}
		var data = {data:list,content:content};
		var html = visitorAccessRobotMsg.render(data);
		layer.open({
			type: 1,
		  	skin: 'layui-layer-rim',
		  	area: ['820px', '540px'],
		  	btn: ['确认', '取消'],
		  	content: html,
		  	yes: function(index) {
		  		var flag = false;
		  		$("#robotWelcome select").each(function(){
		  			if($(this).val()==""){
		  				layer.alert("请选择时间段！",{icon: 5});
		  				flag = true;
		  				return false;
		  			}
		  		});
		  		if(flag){
		  			return;
		  		}
				robotWelcomMsg = form.getJSONObject("#robotWelcome");
				layer.close(index);
			}
		});
	}
	//robot welcome msg end---

	//member access msg begin----
	AutoConfig.updateMemberAccessMsgList = function(confKey, content) {
		// 获取渠道KEY
		var channelKey = AutoConfig.getChannelKey();
		if(!channelKey) {
			layer.alert("获取渠道信息失败！", {icon: 5});
			return;
		}

		// 加载会员身份配置数据
		ajax.remoteCall("${ctxPath}/servlet/channelMemberAutoConfig?action=getConfig", {
			channelKey: channelKey,
			confKey: confKey
		}, function(result) {
			if(result.state == 1) {
				var data = {data: result.data.list || [], content: content, confKey: confKey};
				var html = memberAccessMsgList.render(data);

				layer.open({
					type: 1,
					skin: 'layui-layer-rim',
					area: ['820px', '540px'],
					btn: ['确认', '取消'],
					content: html,
					success: function(layero, index) {
						// 渲染下拉选择框
						layero.find("select[data-mars]").each(function() {
							$(this).render();
						});
					},
					yes: function(index) {
						AutoConfig.saveMemberConfig(confKey, index);
					}
				});
			} else {
				layer.alert(result.msg || "加载配置失败", {icon: 5});
			}
		});
	}

	AutoConfig.saveMemberConfig = function(confKey, layerIndex) {
		var channelKey = AutoConfig.getChannelKey();
		var memberIds = [];
		var contents = [];

		$("#memberUpdateForm select[name='memberId']").each(function() {
			memberIds.push($(this).val());
		});
		$("#memberUpdateForm textarea[name='content']").each(function() {
			contents.push($(this).val());
		});

		var configData = {};
		for(var i = 0; i < memberIds.length; i++) {
			if(memberIds[i] && contents[i]) {
				configData[memberIds[i]] = contents[i];
			}
		}

		ajax.remoteCall("${ctxPath}/servlet/export?action=saveMemberAutoConfig", {
			channelKey: channelKey,
			confKey: confKey,
			configData: JSON.stringify(configData)
		}, function(result) {
			if(result.state == 1) {
				layer.msg("保存成功", {icon: 1});
				layer.close(layerIndex);
			} else {
				layer.alert(result.msg || "保存失败", {icon: 5});
			}
		});
	}

	AutoConfig.addMemberRuntime = function(content) {
		var newDom = $(AutoConfig.createMemberRuntimeHTML(content));
		$("#memberRuntimeDiv").append(newDom);
		// 渲染新增的下拉选择框
		newDom.find("select[data-mars]").render();
	}

	AutoConfig.deleteMemberRuntime = function(ele) {
		$(ele).parent().parent().parent().remove();
	}

	AutoConfig.createMemberRuntimeHTML = function(content) {
		return memberAccessMsg.render({content: content});
	}

	AutoConfig.getChannelKey = function() {
		// 通过channelId获取channelKey
		var channelKey = null;
		ajax.remoteCall("${ctxPath}/servlet/channel?action=getChannelKeyByChannelId", {
			channelId: AutoConfig.channelId
		}, function(result) {
			if(result.state == 1 && result.data) {
				channelKey = result.data.CHANNEL_KEY;
			}
		}, false); // 同步调用
		return channelKey;
	}
	//member access msg end---

	function isNumber(val) {
    	var regPos = /^\d+(\.\d+)?$/; //非负浮点数
    	var regNeg = /^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/; //负浮点数
    	if(regPos.test(val) || regNeg.test(val)) {
	        return true;
	    } else {
	        return false;
		}
    }
	
</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>