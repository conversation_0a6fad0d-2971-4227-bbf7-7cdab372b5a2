package com.yunqu.yc.media.center.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.msg.AgentVideoMessage;
import com.yunqu.yc.media.center.util.CacheUtil;
import com.yunqu.yc.media.center.util.CommonUtil;
import com.yunqu.yc.media.center.util.MediaCacheUtil;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.string.StringUtils;

import java.util.*;

/**
 * 坐席信息
 *
 * <AUTHOR>
 */
public class Agent {


    /**
     * 坐席Id
     */
    String agentId;
    /**
     * 坐席名称
     */
    String agentName;
    /**
     * 坐席昵称
     */
    String nickName;

    /**
     * 坐席工号
     */
    String agentPhone;

    /**
     * 企业Id
     */
    String entId;

    /**
     * 请求开始时间
     */
    String loginTime;

    /**
     * 服务开始时间
     */

    String logoutTime;


    /**
     * 坐席状态，Ready 闲 NotReady 忙
     */
    String agentState = "Ready";

    /**
     * 坐席状态时间
     */
    long agentStateTime = System.currentTimeMillis();

    /**
     * Agent状态, 1 在线，0不在线
     */

    int onlineState;

    /**
     * 忙状态
     */
    int busyState;

    /**
     * 最大的服务用户数
     */
    int maxServiceCount = -1;


    /**
     * 当天服务总数
     */
    int totalServiceCount = 0;


    /**
     * 最后的心跳时间
     */
    long heartbeatTime = System.currentTimeMillis();

    /**
     * 最后接收用户信息时间
     */
    long lastUserMsgTime;

    /**
     * 最后agent发送信息
     */
    long lastAgentMsgTime;

    long lastServiceTime;

    String ipaddr;

    int dateId = 0;

    /**
     * 坐席最后空闲时间
     */
    long startReadTime;

    /**
     * 坐席最后置忙时间
     */
    long startNotReadyTime;

    /**
     * sessionId,用户的唯一标志
     */

    Set<String> groupIds = new HashSet<String>();
    /**
     * 保存当前用户服务的用户Id
     */
    Map<String, UserSession> users = new HashMap<String, UserSession>();

    /**
     * 已接入的视频用户
     */
    UserSession videoUser;

    /**
     * 坐席是否繁忙
     *
     * @return
     */
    public synchronized boolean isBusy() {
        return (this.users.size() >= this.getMaxServiceCount());
    }

    public void updateHeartbeatTime() {
        this.heartbeatTime = System.currentTimeMillis();
    }

    /**
     * 更新最后接入服务时间
     */
    public void updateLastServiceTime() {
        this.lastServiceTime = System.currentTimeMillis();
    }

    public String getIpaddr() {
        return ipaddr;
    }

    public void setIpaddr(String ipaddr) {
        this.ipaddr = ipaddr;
    }

    public int getTotalServiceCount() {
        return totalServiceCount;
    }

    public String getAgentPhone() {
        return agentPhone;
    }

    public void setAgentPhone(String agentPhone) {
        this.agentPhone = agentPhone;
    }

    public long getStartReadTime() {
        return startReadTime;
    }

    public void setStartReadTime(long startReadTime) {
        this.startReadTime = startReadTime;
    }


    public long getStartNotReadyTime() {
        return startNotReadyTime;
    }

    public void setStartNotReadyTime(long startNotReadyTime) {
        this.startNotReadyTime = startNotReadyTime;
    }

    /**
     * 保存坐席空闲时间
     * 1.坐席签入置闲开始计时，每个用户接入判断是否繁忙，坐席繁忙结束计时，计算时间。
     * 2.置闲状态下每次关闭一个会话开始计时，坐席繁忙时结束计时，计算时间。
     * 3.坐席置忙或签出，结束计时，计算时间。
     * 4.服务上限增大/减少的时候并且坐席空闲开始计时，坐席繁忙时结束计时，计算时间。
     */
    public synchronized void saveReadTime() {
        long _startReadTime = 0;
        long thid = Thread.currentThread().getId();
        int hashCode = this.hashCode();
        MediaCenterLogger.getDebugLogger().info("<saveReadTime><" + agentId + ">Thread["+thid+"]hashCode<" + hashCode + "> lastReadTime-> "+this.startReadTime+" from-->"+ CommonUtil.getInvokeClassNameAndMethod());
        if (this.online() && this.isReady() && !this.isBusy()) {//在线 且置闲 且非繁忙
            _startReadTime = System.currentTimeMillis();
            MediaCenterLogger.getDebugLogger().info("<saveReadTime><" + agentId + ">Thread["+thid+"]hashCode<" + hashCode + "> >> 开始计时！！！");
        }else{
            MediaCenterLogger.getDebugLogger().info("<saveReadTime><" + agentId + ">Thread["+thid+"]hashCode<" + hashCode + "> >> 结束计时！！！");
        }
        try {
            if (this.startReadTime > 0) {
                //如果已存在的最后空闲时间非当天，则不计算当天空闲时长
                EasyCalendar lastCal = EasyCalendar.newInstance(new Date(this.startReadTime));
                EasyCalendar currentCal = EasyCalendar.newInstance();
                if (lastCal.getDateInt() < currentCal.getDateInt()) {
                    MediaCenterLogger.getDebugLogger().warn("<saveReadTime><" + agentId + ">Thread["+thid+"]hashCode<" + hashCode + "> >> 上一次最后空闲时间日期 与当前日期不是同一天，不计算！！！");
//                    EasyCalendar lastDayCal = EasyCalendar.newInstance(lastCal.getDateString("-") + " 23:59:59", "yyyy-MM-dd HH:mm:ss");
                }else{
                    long time = System.currentTimeMillis() - this.startReadTime;
                    IndexModel.getIndexModel(this.entId, this.agentId).saveReadTime(time);
                    MediaCenterLogger.getDebugLogger().info("<saveReadTime><" + agentId + ">Thread["+thid+"]hashCode<" + hashCode + "> >> 上一次最后空闲时间:" + lastCal.getDateTime("-") + "，本次设置空闲开始时间:" + _startReadTime + ",readTime:" + time);
                }
            }

        } catch (Exception e) {
            MediaCenterLogger.getDebugLogger().error(e.getMessage(),e);
        }finally {
            this.startReadTime = _startReadTime;
        }
    }

    /**
     * 保存服务开始时间，记录首次服务开始时间、最后一次服务开始时间
     */
    public void saveServiceTime() {
        IndexModel.getIndexModel(this.entId, this.agentId).startService(null);
    }

    /**
     * 保存末次服务结束时间
     */
    public void saveLastServiceEndTime() {
        IndexModel.getIndexModel(this.entId, this.agentId).finishService();
    }

    public void saveNotReadyTime(long startNotReadyTime) {
        if (this.startNotReadyTime > 0) {
            long time = System.currentTimeMillis() - this.startNotReadyTime;
            MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <saveNotReadyTime> >> lastNotReadyTime:" + this.startNotReadyTime + ",startReadyTime:" + startNotReadyTime + ",notReadyTime:" + time);
            IndexModel.getIndexModel(this.entId, this.agentId).saveNotReadyTime(time);
        }
        setStartNotReadyTime(startNotReadyTime);
    }

    public JSONObject getAgentMonitorString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("agentId", getAgentId());
        jsonObject.put("agentName", this.getAgentName());
        jsonObject.put("isReady", isReady());
        jsonObject.put("isBusy", isBusy());
        jsonObject.put("sessionCount", getUsers().size());
        jsonObject.put("lastServiceTime", getLastServiceTimeString());
        return jsonObject;
    }

    /**
     * 当前坐席是否有效，当，超过30秒没有收到心跳信息就超时。
     *
     * @return
     */
    public boolean isReady() {
        //if(!this.valid()) return true;
        return ("Ready".equalsIgnoreCase(this.agentState));
    }

    /**
     * 如果180秒内没有收到任何的消息和操作，则自动置忙。
     *
     * @return
     */
    public boolean readyTimeout() {
        //如果不在线，直接返回true;
        long diff = System.currentTimeMillis() - this.heartbeatTime;
        //如果超时大于35秒，则置为超时。
        if (diff > 180 * 1000) {
            return true;
        }
        if (!this.online()) return true;
        return false;
    }

    /**
     * 如果30分钟没有任何消息，则自动登出
     *
     * @return
     */
    public boolean serviceTimeout() {
        // 如果不在线，直接返回true;
        long diff = System.currentTimeMillis() - this.heartbeatTime;
        // 如果超时大于30分钟，则置为超时。
        if (diff > 1800 * 1000) {
            return true;
        }
        if (!this.online()) return true;
        return false;
    }

    public void removeUser(String sessionId) {
        users.remove(sessionId);
        saveReadTime();
        if(!this.isReady() && this.isBusy()) {
            saveNotReadyTime(System.currentTimeMillis());
        } else {
            saveNotReadyTime(0);
        }
        saveLastServiceEndTime();
        //小休时长统计
        //1.坐席置忙并且没有会话开始计时，坐席置闲结束计时，计算时间。
        //2.置忙状态下关闭最后一个会话开始计时，坐席置闲结束计时，计算时间。
        if (!this.isReady() && users.size() == 0) {
            IndexModel.getIndexModel(this.entId, this.agentId).startNotReady("1");
        }

        if (!this.online()) {
            this.setLogoutTime(EasyCalendar.newInstance().getDateTime("-"));
        }
    }

//	public SkillGroup getSkillGroup() {
//		return skillGroup;
//	}


    public void addSkillGroup(String skillGroupId) {
        if (!this.groupIds.contains(skillGroupId)) {
            this.groupIds.add(skillGroupId);
        }
    }

    public void removeSkillGroup(String skillGroupId) {
        this.groupIds.remove(skillGroupId);
    }

    public Set<String> getGroupIds() {
        return groupIds;
    }

    public void setGroupIds(Set<String> groupIds) {
        this.groupIds = groupIds;
    }

    public long getLastServiceTime() {
        return this.lastServiceTime;
    }

    public UserSession getServiceUser(String sessionId) {
        if (StringUtils.isBlank(sessionId)) {
            return null;
        }
        return users.get(sessionId);
    }

    /**
     * 给坐席添加服务用户
     *
     * @param userSesssion
     * @param type         添加类型，1排队接入，2转移接入
     * @return
     */
    public synchronized boolean addUser(UserSession userSesssion, int type) {
        //坐席繁忙，新用户登录不能加进去服务用户里
        if (type == 1 && this.isBusy()) {
            MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] agent[" + this.agentId + "] is busy, addUser(userSesssion)->" + userSesssion);
            return false;
        }
        EasyCalendar cal = EasyCalendar.newInstance();
        int _dateId = cal.getDateInt();
        if (_dateId != this.dateId) {
            this.totalServiceCount = 0;
            this.dateId = _dateId;
            MediaCacheUtil.put("MEDIA_SERVICE_COUNT_" + this.dateId + "_" + this.agentId, 0,24*3600);
        }
        try {
            if (this.totalServiceCount == 0) {
                Object count = MediaCacheUtil.get("MEDIA_SERVICE_COUNT_" + this.dateId + "_" + this.agentId);
                if (count != null && !"".equals(count)) {
                    this.totalServiceCount = Integer.parseInt(count.toString());
                }
            }
        } catch (Exception ex) {
            MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
        }

        saveReadTime();

        if (this.isBusy()) {
            saveNotReadyTime(System.currentTimeMillis());
        } else if (this.isReady()) {
            saveNotReadyTime(0);
        } else {
            saveNotReadyTime(System.currentTimeMillis());
        }
        //记录首次服务开始时间、最后一次服务开始时间
        saveServiceTime();
        this.totalServiceCount++;
        users.put(userSesssion.getSessionId(), userSesssion);
        MediaCacheUtil.put("MEDIA_SERVICE_COUNT_" + this.dateId + "_" + this.agentId, totalServiceCount + "", 24 * 3600);
        return true;
    }


    public void setReady() {
        this.agentState = "Ready";
        this.agentStateTime = System.currentTimeMillis();
        IndexModel.getIndexModel(this.entId, this.agentId).startReady();
        saveReadTime();
        saveNotReadyTime(0);

    }

    public long getReadyLen() {
        long inReadyLen = IndexModel.getIndexModel(this.entId, this.agentId).getInboundReadyTime();
        if (inReadyLen <= 0)
            return 0;
        return inReadyLen / 1000;
    }

    public void setNotReady() {
        this.agentState = "NotReady";
        this.agentStateTime = System.currentTimeMillis();
        //小休时长统计
        //1.坐席置忙并且没有会话开始计时，坐席置闲结束计时，计算时间。
        //2.置忙状态下关闭最后一个会话开始计时，坐席置闲结束计时，计算时间。
        if (users.size() == 0) {
            IndexModel.getIndexModel(this.entId, this.agentId).startNotReady("1");
        }
        saveReadTime();
        saveNotReadyTime(System.currentTimeMillis());
    }

    public long getNotReadyLen() {
        long inNotReadyLen = IndexModel.getIndexModel(this.entId, this.agentId).getInboundNotReadyTime();
        if (inNotReadyLen <= 0)
            return 0;
        return inNotReadyLen / 1000;
    }

    public void setLimit(int limitCount) {
        this.maxServiceCount = limitCount;
    }

    /**
     * 坐席登录
     */
    public void login() {
        if (this.startReadTime > 0) {
            MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] <login><" + agentId + "> >> lastReadTime:" + this.startReadTime);
            MediaCenterLogger.getDebugLogger().info("<login><" + agentId + "> >> lastReadTime:" + this.startReadTime);
        }
        EntContext context = EntContext.getContext(this.entId);
        this.setOnlineState(1);
        this.setNotReady();//默认置忙，ccbar没有发送cmdNotReady
        IndexModel.getIndexModel(this.entId, this.agentId).startLogin();
        this.setLoginTime(EasyCalendar.newInstance().getDateTime("-"));
        //20210406 坐席签入时设置当前日期，并更新缓存钟的当前的服务人数
        EasyCalendar cal = EasyCalendar.newInstance();
        this.dateId = cal.getDateInt();
        MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 坐席[" + agentId + "]签入，所属技能组：" + groupIds);
        for (String groupId : groupIds) {
            SkillGroup skillGroup = context.getSkillGroup(groupId);
            if (skillGroup != null) {
                skillGroup.addOnlineAgent(this);
                skillGroup.addAgent(this);
            }
        }
        this.totalServiceCount = context.getAgentServiceCount(agentId);
        MediaCacheUtil.put("MEDIA_SERVICE_COUNT_" + this.dateId + "_" + this.agentId, totalServiceCount + "", 24 * 3600);
    }

    /**
     * '挂机原因，挂机原因，2  坐席结束服务  9 坐席端异常关闭
     */
    public void logout(int clearCause) {
//		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Agent.logout("+this.agentId+") ->> clearCause:"+clearCause);
        EntContext context = EntContext.getContext(this.entId);
        this.setOnlineState(0);
        this.setLogoutTime(EasyCalendar.newInstance().getDateTime("-"));
        IndexModel.getIndexModel(this.entId, this.agentId).finishLogin();
        saveReadTime();
        saveNotReadyTime(0);

        for (String groupId : groupIds) {
            SkillGroup skillGroup = context.getSkillGroup(groupId);
            if (skillGroup != null) {
                skillGroup.removeOnlineAgent(this);
                skillGroup.removeAgent(this);
            }
        }
        //如果是坐席异常退出的，则不做任何处理，下次坐席登录，还能看到在线用户信息。
        if (clearCause != 9) {
            Iterator<String> iter = users.keySet().iterator();
            while (iter.hasNext()) {
                UserSession user = users.get(iter.next());
                user.logout(clearCause);
            }
            context.removeAgent(agentId);//移除坐席信息
        }

        //坐席签出，挂断正在通话的视频
        try {

            if (videoUser != null) {
                VideoSession videoSession = videoUser.getVideoSession();
                int callType = videoSession.getCallType();//发起者，  1 客户端   2 坐席端
                String videoState = videoSession.getVideoState();//视频会话状态：inviteVideo 邀请中，videoSuccess 视频连接成功，closeVideo 视频关闭
                String command = "closeVideo";
                if (callType == 2) {
                    command = "inviteVideo".equals(videoState) ? "cancelInviteVideo" : command;//坐席正在邀请客户端，坐席登出时发送cancelInviteVideo到客户端
                } else {
                    command = "inviteVideo".equals(videoState) ? "disagreeInviteVideo" : command;//客户端正在邀请坐席，坐席登出时发送disagreeInviteVideo到客户端
                }
                JSONObject jsonObject = new JSONObject();
                jsonObject.put("command", command);
                jsonObject.put("agentId", agentId);
                jsonObject.put("entId", entId);
                jsonObject.put("sessionId", videoUser.getSessionId());
                MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] 坐席[" + agentId + "]签出，挂断正在通话的视频sessionId[" + videoUser.getSessionId() + "]--->" + jsonObject);
                AgentVideoMessage.getInstance(agentId).doMessage(jsonObject);
            }

        } catch (Exception e) {
            MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
        }

    }

    /**
     * 登录通知，在超过1800秒没有做任何动作，则做签出处理。
     */
    public void logoutNotify() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("command", "cmdLogout");
        jsonObject.put("agentId", this.agentId);
        jsonObject.put("entId", this.entId);
        jsonObject.put("clearCause", 5);
        ProducerBroker.sendAgentMessage(agentId, jsonObject.toJSONString());
    }

    /**
     * 置忙通知，在超过180秒没有做任何动作，则做置忙处理。
     */
    public void notReadyNotify() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("command", "cmdNotReady");
        jsonObject.put("agentId", this.agentId);
        jsonObject.put("entId", this.entId);
        ProducerBroker.sendAgentMessage(agentId, jsonObject.toJSONString());
    }

    public String getAgentId() {
        return agentId;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentName() {
        return agentName;
    }

    public void setAgentName(String agentName) {
        this.agentName = agentName;
    }

    public String getNickName() {
        return nickName;
    }

    public void setNickName(String nickName) {
        this.nickName = nickName;
    }

    public String getEntId() {
        return entId;
    }

    public void setEntId(String entId) {
        this.entId = entId;
    }

    public String getLoginTime() {
        return loginTime;
    }

    public void setLoginTime(String loginTime) {
        this.loginTime = loginTime;
    }

    public String getLogoutTime() {
        return logoutTime;
    }

    public void setLogoutTime(String logoutTime) {
        this.logoutTime = logoutTime;
    }


    /**
     * 这里只取状态时间持续时间，返回秒
     *
     * @return
     */
    public long getAgentStateTime() {
        return (System.currentTimeMillis() - this.agentStateTime) / 1000;
    }

    public void setAgentStateTime(long agentStateTime) {
        this.agentStateTime = agentStateTime;
    }

    public int getOnlineState() {
        return onlineState;
    }

    public String getOnelinStateName() {
        if (onlineState == 1) return "在线";
        return "离线";
    }

    public boolean online() {
        return (onlineState == 1);
    }

    public void setOnlineState(int onlineState) {
        this.onlineState = onlineState;
    }

    public int getBusyState() {
        return busyState;
    }

    public void setBusyState(int busyState) {
        this.busyState = busyState;
    }

    public int getMaxServiceCount() {
        //如果没有设置服务上线
        if (this.maxServiceCount < 0) {
            Object object = CacheUtil.get("SERVICE_LIMITCOUNT_" + agentId);
            String cacheMaxServiceCount = "";
            if (object != null) {
                cacheMaxServiceCount = object.toString();
            }
            if (StringUtils.isNotBlank(cacheMaxServiceCount)) {
                try {
                    this.maxServiceCount = Integer.parseInt(cacheMaxServiceCount);
                } catch (Exception ex) {
                    // TODO: handle exception
                }
            }
        }
        if (this.maxServiceCount < 0) {
            this.maxServiceCount = 5;
            CacheUtil.put("SERVICE_LIMITCOUNT_" + this.agentId, String.valueOf(this.maxServiceCount));
        }
        return maxServiceCount;
    }

    public int getCurServiceCount() {
        return this.users.size();
    }

    public void setMaxServiceCount(int maxServiceCount) {
        this.maxServiceCount = maxServiceCount;
        CacheUtil.put("SERVICE_LIMITCOUNT_" + this.agentId, String.valueOf(this.maxServiceCount));

        saveReadTime();

        if (this.isReady() && this.isBusy()) {
            saveNotReadyTime(0);
        } else {
            saveNotReadyTime(System.currentTimeMillis());
        }
    }

    public String getLastServiceTimeString() {
        if (this.lastServiceTime == 0) return "-";
        EasyCalendar easyCalendar = EasyCalendar.newInstance(new Date(this.lastServiceTime));
        return easyCalendar.getTimeString();
    }


    public String getLastHeartbeatTimeString() {
        if (this.heartbeatTime == 0) return "-";
        EasyCalendar easyCalendar = EasyCalendar.newInstance(new Date(this.heartbeatTime));
        return easyCalendar.getTimeString();
    }


    public String getLastUserMsgTimeString() {
        if (this.lastUserMsgTime == 0) return "-";
        EasyCalendar easyCalendar = EasyCalendar.newInstance(new Date(this.lastUserMsgTime));
        return easyCalendar.getTimeString();
    }

    public void updateUserMsgTime() {
        this.lastUserMsgTime = System.currentTimeMillis();
    }

    public String getLastAgentMsgTimeString() {
        if (this.lastAgentMsgTime == 0) return "-";
        EasyCalendar easyCalendar = EasyCalendar.newInstance(new Date(this.lastAgentMsgTime));
        return easyCalendar.getTimeString();
    }

    public void updateAgentMsgTime() {
        this.updateHeartbeatTime();
        this.lastAgentMsgTime = System.currentTimeMillis();
    }

    public Map<String, UserSession> getUsers() {
        return users;
    }

    public void setUsers(Map<String, UserSession> users) {
        this.users = users;
    }

    public UserSession getVideoUser() {
        return videoUser;
    }

    public int getDateId() {
        return dateId;
    }

    public void setDateId(int dateId) {
        this.dateId = dateId;
    }

    public String getAgentState() {
        return agentState;
    }

    public void setAgentState(String agentState) {
        this.agentState = agentState;
    }


    /**
     * 接入视频用户，一个坐席同时只有一个视频用户
     *
     * @param videoUser
     * @return
     */
    //2.0#20191227-1
    public synchronized boolean setVideoUser(UserSession videoUser) {
        if (getVideoUser() != null && videoUser != null) {
            return false;
        }
        this.videoUser = videoUser;
        return true;
    }

    public void removeVideoUser() throws Exception {
        UserSession user = this.getVideoUser();
        if (user == null) {
            return;
        }
        MediaCenterLogger.getLogger().debug("Agent.removeVideoUser() -> agentId[" + this.agentId + "]" + ",ChatSessionId[" + user.getChatSessionId() + "]");
        this.setVideoUser(null);
        user.closeVideo(21);
    }

    //2.0#20200214-1
    public void removeVideoUser(String sessionId) {
        UserSession user = this.getVideoUser();
        if (user == null) {
            return;
        }
        if (!sessionId.equals(user.getSessionId())) {
            return;
        }
        MediaCenterLogger.getLogger().debug("Agent.removeVideoUser() -> agentId[" + this.agentId + "]" + ",ChatSessionId[" + user.getChatSessionId() + "]");
        this.setVideoUser(null);
    }

    /**
     * 检查技能组优先级
     * 当一个坐席在多个技能组上时，需要对比该坐席所属的所有技能组优先排序，如果有优先级大于当前技能组的组并且排队用户不为空则不能将坐席分配到当前技能组。
     *
     * @param skippGroupId
     * @return
     */
    public synchronized boolean checkFirstGroup(String skillGroupId) {
        EntContext context = EntContext.getContext(this.entId);
        SkillGroup cGroup = context.getSkillGroup(skillGroupId);
//		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <检查技能组优先级-"+this.agentId+">当前技能组，skippGroupId："+skillGroupId+",groupName:"+cGroup.getSkillGroupName()+",idxOrder："+cGroup.getIdxOrder());
        boolean isChoose = true;
        for (String gid : this.groupIds) {
            if (gid.equals(skillGroupId)) {
                continue;
            }
            SkillGroup tGroup = context.getSkillGroup(gid);
//			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <检查技能组优先级-"+this.agentId+">坐席所属技能组，skippGroupId："+gid+",groupName:"+tGroup.getSkillGroupName()+",idxOrder："+tGroup.getIdxOrder());
            if (cGroup != null && tGroup != null && cGroup.getIdxOrder() < tGroup.getIdxOrder() && tGroup.getQueueSize("level1") > 0) {
//				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] <检查技能组优先级-"+this.agentId+">该坐席下已存在优先级高于当前技能组["+skillGroupId+"]");
                isChoose = false;
                break;
            }
        }

        return isChoose;
    }

    /**
     * 检查坐席正在服务用户是否超时两小时未发送任何消息
     */
    //2.0#20210428-1
    public void checkUserTime() {
        MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]--->坐席主动签出，检查坐席正在服务用户是否超时两小时未发送任何消息");

        List<JSONObject> dbServiceUser = EntContext.getContext(entId).getAgentServiceUser(agentId);
        Set<UserSession> merUserSet = new HashSet<>(this.getUsers().values());

        if (dbServiceUser == null || dbServiceUser.size() == 0) {
            for (UserSession userSession : merUserSet) {
                try {
                    String sessionId = userSession.getSessionId();
                    if (StringUtils.equals(this.agentId, userSession.getAgentId())) {
                        MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]，sessionId[" + sessionId + "]--->坐席主动签出，数据库中的用户会话已结束，强制调用userSession.logout(3)结束会话 " + userSession);
                        userSession.logout(3);
                    } else {
                        MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]，sessionId[" + sessionId + "]--->坐席主动签出，数据库中的用户会话已结束，该用户不属于当前坐席！！！！！" + userSession);
                        this.getUsers().remove(sessionId);//2.0#20210518-1
                    }
                } catch (Exception e) {
                    MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
                }
            }
            return;
        }

        //遍历内存中的在线用户列表，对比数据库中不存在就强制关闭会话
        for (UserSession userSession : merUserSet) {
            String sessionId = userSession.getSessionId();
            String chatSessionId = userSession.getChatSessionId();
            try {

                if (System.currentTimeMillis() - userSession.getUserTime() > 2 * 3600 * 1000) {
                    if (StringUtils.equals(this.agentId, userSession.getAgentId())) {
                        MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]，sessionId[" + sessionId + "]--->坐席主动签出，检查坐席正在服务用户超时两小时未发送任何消息，强制调用userSession.logout(3)结束会话" + userSession);
                        userSession.logout(3);
                    } else {
                        MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]，sessionId[" + sessionId + "]--->坐席主动签出，该用户不属于当前坐席！！！！！" + userSession);
                        this.getUsers().remove(sessionId);//2.0#20210518-1
                    }
                    continue;
                }
            } catch (Exception e) {
                MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
            }

            boolean issame = false;
            for (JSONObject dbUserObj : dbServiceUser) {
                String dbChatSessionId = dbUserObj.getString("SERIAL_ID");
                if (StringUtils.equals(dbChatSessionId, chatSessionId)) {
                    issame = true;
                    break;
                }
            }

            if (!issame) {
                MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] Agent.checkUserTime() agentId[" + agentId + "]，sessionId[" + sessionId + "]--->坐席主动签出，内存中还存在在线会话而数据库中的用户会话已结束，强制调用userSession.logout(3)结束会话" + userSession);
                userSession.logout(3);
                this.getUsers().remove(sessionId);
            }
        }
    }

    public String toString() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.put("agentId", this.agentId);
        jsonObject.put("agentName", this.agentName);
        jsonObject.put("entId", this.entId);
        jsonObject.put("loginTime", this.loginTime);
        jsonObject.put("isBusy", this.isBusy());
        jsonObject.put("readyTimeout", this.readyTimeout());
        jsonObject.put("online", this.online());
        jsonObject.put("isReady", this.isReady());
        jsonObject.put("agentState", this.agentState);
        jsonObject.put("heartbeatTime", this.getLastHeartbeatTimeString());
        jsonObject.put("onlineState", this.getOnelinStateName());
        jsonObject.put("agentStateTime", this.agentStateTime);
        jsonObject.put("curServiceCount", this.users.size());
        jsonObject.put("maxServiceCount", this.getMaxServiceCount());
        jsonObject.put("skillgroupCount", this.groupIds.size());
        jsonObject.put("skillgroups", this.groupIds);
        //jsonObject.put("skillGroup", this.skillGroup);
        //jsonObject.put("users", this.getUsers());
//		if(this.skillGroup == null) jsonObject.put("skillGroup", new JSONObject());
//		else jsonObject.put("skillGroup", JSONObject.parseObject(skillGroup.toString()));
        return jsonObject.toJSONString();
    }

}
