package com.yunqu.yc.mediagw.servlet;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.mediagw.base.AppBaseServlet;
import com.yunqu.yc.mediagw.base.Constants;
import com.yunqu.yc.mediagw.base.VisitorInfos;
import com.yunqu.yc.mediagw.util.CacheLongTxtUtil;
import com.yunqu.yc.mediagw.util.SyncLockUtil;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.kit.WebKit;
import org.easitline.common.utils.string.StringUtils;

import javax.servlet.annotation.MultipartConfig;
import javax.servlet.annotation.WebServlet;
import javax.servlet.http.HttpServletRequest;
import java.io.PrintWriter;

/**
 * @ClassName: DeveloperDebugServlet
 * @Description: 开发者调试入口
 * <AUTHOR>
 * @Date 2025/04/07
 * @Version 1.0
 */
@WebServlet("/developDebug/*")
@MultipartConfig(maxFileSize = 20 * 1024 * 1024)
public class DeveloperDebugServlet extends AppBaseServlet {
    private static final long serialVersionUID = 1L;
    private static Logger logger = LogEngine.getLogger("yc-mediagw-develop");
    private static final String EXCUTE_TYPES = "clearLock,checkVisitorTimeout,getlongTxtROBOT,setlongTxtROBOT";
    private static long lastRunTime = 0L;
    private static final String dbname = "ycbusi";
    public JSONObject actionForIndex() {
        long thid = Thread.currentThread().getId();
        HttpServletRequest request = getRequest();
        long startTimeMill = System.currentTimeMillis();
        String ip = WebKit.getIP(request);
        EasyResult result = new EasyResult();
        JSONObject param = getRequestQueryParams();
        logger.info("Thread["+thid+"] IP["+ip+"] 执行调试，入参："+param);

        if(lastRunTime > 0 && System.currentTimeMillis() - lastRunTime < 10000) {
            logger.warn("Thread["+thid+"] IP["+ip+"] 执行调试，未开启调试开关，禁止访问！");
            return EasyResult.fail("请求过于频繁，请于10秒后重试！");
        }

        lastRunTime = System.currentTimeMillis();

        if(!Constants.isDevelopSwitch()){
            logger.warn("Thread["+thid+"] IP["+ip+"] 执行调试，未开启调试开关，禁止访问！");
            return EasyResult.fail("禁止访问1！");
        }

        String excuteType = param.getString("excuteType");
        if (StringUtils.isBlank(excuteType)||!EXCUTE_TYPES.contains(excuteType)) {
            logger.warn("Thread["+thid+"] IP["+ip+"] 执行调试，参数[excuteType]为空或不正确，禁止访问！");
            return EasyResult.fail("禁止访问2！");
        }

        try {
            PrintWriter writer = getResponse().getWriter();
            writer.println("调试："+excuteType);
            switch (excuteType){
                case "clearLock":
                    SyncLockUtil.clearLock();
                    break;
                case "checkVisitorTimeout":
                    VisitorInfos.getInstance().checkVisitorTimeout();
                    break;
                //yc-mediagw/developDebug?action=index&excuteType=getlongTxtROBOT&chatId=4d799a0384522b4808380d661043ea99_&cacheKey=cacheLongTxtROBOT_DATA:1713303
                case "getlongTxtROBOT":
                    String chatId = param.getString("chatId");//4d799a0384522b4808380d661043ea99_
                    String cacheKey = param.getString("cacheKey");//cacheLongTxtROBOT_DATA:1713303
                    String cacheVaule = CacheLongTxtUtil.get(dbname, chatId, "ROBOT_DATA", cacheKey);
                    writer.println("cacheKey："+cacheKey);
                    writer.println("cacheVaule：");
                    writer.println(cacheVaule);
                    break;
                //yc-mediagw/developDebug?action=index&excuteType=setlongTxtROBOT&chatId=4d799a0384522b4808380d661043ea99_&cacheKey=cacheLongTxtROBOT_DATA:1713303&addTxt=
                case "setlongTxtROBOT":
                    chatId = param.getString("chatId");//4d799a0384522b4808380d661043ea99_
                    cacheKey = param.getString("cacheKey");//cacheLongTxtROBOT_DATA:1713303
                    String value = CacheLongTxtUtil.get(dbname, chatId, "ROBOT_DATA", cacheKey);
                    EasyRecord easyRecord = new EasyRecord();
                    if(StringUtils.isNotBlank(value)){
                        JSONObject longTxtObj = JSONObject.parseObject(value);
                        longTxtObj.put("debugtxtContent", "风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降风速设置越低可运行的最大频率也相对较低，制冷量和制热量也相对较低。风量越大，噪音越高，制冷时出风温度升高，制热时出风温度下降");
                        longTxtObj.put("addTxt", param.getString("addTxt"));
                        easyRecord.set("ROBOT_DATA",longTxtObj.toJSONString());
                    }
                    CacheLongTxtUtil.set(dbname, easyRecord, chatId, "ROBOT_DATA");
                    writer.println("chatId："+chatId);
                    writer.println("easyRecord：");
                    writer.println(JSONObject.toJSONString(easyRecord));
                    break;
                default:
            }
        } catch (Exception e) {
            logger.error("Thread["+thid+"] IP["+ip+"] 执行调试，执行失败："+e.getMessage());
            logger.error(e.getMessage(),e);
            return EasyResult.fail("执行失败！");
        }
        logger.info("Thread["+thid+"] IP["+ip+"] 执行调试，执行完成！耗时："+(System.currentTimeMillis()-startTimeMill)+" ms");
        return result;
    }


    @Override
    protected String getResId() {
        return null;
    }
}
