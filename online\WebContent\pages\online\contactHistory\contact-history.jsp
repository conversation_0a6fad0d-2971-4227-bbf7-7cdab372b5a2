<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp"%>
<html class="page-agent">
<head>
	<meta charset="utf-8">
	<meta name="renderer" content="webkit|ie-comp|ie-stand">
	<meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1">
	<meta name="viewport" content="width=device-width,initial-scale=1,minimum-scale=1.0,maximum-scale=1.0,user-scalable=no" />

	<title>客服</title>
	<link rel="stylesheet" href="/easitline-static/lib/bootstrap/css/bootstrap.min.css">
	<script src="/easitline-static/js/jquery.min.js"></script>
	<script src="/easitline-static/lib/bootstrap/js/bootstrap.min.js"></script>
	<script type="text/javascript" src="/easitline-static/js/jsrender.min.js"></script>
	<script type="text/javascript" src="/easitline-static/lib/layer/layer.js"></script>
	
	<link rel="stylesheet" href="${ctxPath}/static/css/mediaPortal.css">
	<link rel="stylesheet" href="${ctxPath}/static/css/agentMedia.css">
	<link href="/easitline-static/css/easitline.ui.css?v=20180107" rel="stylesheet">
	<style>
		.timeline-box .timeline-year .timeline-content .timeline-month:before{
			left: 5px;
		}
		.timeline-box .timeline-year .timeline-content .timeline-month:after{
			left: 7px;
		}
		.timeline-box .timeline-year .timeline-content:after{left: 12px;}

		.timeline-box .timeline-year .timeline-content .timeline-month {
		    position: relative;
		    padding: 5px 10px 0 10px;
		}
	.timeline-box .timeline-year .timeline-header{
		color: #666;
		background-color: #f8f8f8;
	}
		.timeline-history{
			display: block;
			padding: 5px 10px;
			text-align: center;
			background-color: #f2f2f2;
			cursor: pointer;
			text-decoration: none;
		}
		.timeline-history:hover{
			text-decoration: none;
		}
		.row-history{
			/*z-index: 2;*/
		    background: #fff;
		    position: relative;
		}
		.timeline-message{
			margin: 5px 10px 5px 20px;
			border:1px solid #ddd;
			border-radius: 2px;
			background: #fefefe;
			color: #333;
		}

		.timeline-message .sender{
			padding: 5px;
			background-color: #c5d3f1;
			border-bottom: 1px solid #ddd;
		}
		.timeline-message.send .sender{
			background-color: #eee;
		}
		.timeline-message .msg{
			padding: 5px;
			
		}
		.timeline-message  img{max-width: 100%}
		.timeline-year{margin-bottom: 10px;}
	</style>
</head>
<body>
	<div id="timeline" class="timeline-box">
		
	</div>
	<div class="row paginate" id="page">
		<div class="_pagination" style="display: inline-block;position: relative;float: left;margin-left: 15px;">
			<input type="hidden" name="pageIndex" class="pageIndexV" value="-1">
			<input type="hidden" class="totalPageV" value="0">
			<input type="hidden" class="totalRowV" value="0">
			<c:choose>
				<c:when test="${empty param.pageSizes}">
					<c:set var="pageSizes" value="5,10,25,50,100"></c:set>
					<c:set var="pageSize" value="10"></c:set>
				</c:when>
				<c:otherwise>
					<c:set var="pageSizes" value="${param.pageSizes}"></c:set>
					<c:set var="pageSize" value="${param.pageSize}"></c:set>
				</c:otherwise>
			</c:choose>
			<select name="pageSize" class="form-control input-sm pageSizeV" style="width: 90px;display: inline-block;height: 28px;padding: 2px 5px">
				<c:forEach items="${fn:split(pageSizes,',')}"  var="val" varStatus="vs">
					<option value="${val}"  <c:if test="${pageSize==val}">selected="selected"</c:if>>${val} 条/页</option>
				</c:forEach>
			</select>&nbsp;
			共 <span class="totalRow"></span>&nbsp;条 ,&nbsp;<span class="totalPage"></span> 页
		</div>
		<ul class="pagination pagination-sm pageNumV"  style="margin: 0px;float: right;"></ul>
	</div>
	<script src="${ctxPath}/static/js/chat/qq-wechat-emotion-parser.min.js"></script>
	
	<script id="chat-his-template" type="text/x-jsrender">
		{{for data}}
		<div id="{{:serialId}}" class="timeline-year">
			<div class="timeline-header">{{:channelName}}-{{:beginTime}}</div>
			<div class="timeline-content">
				<div class="timeline-month">
					<label class="timeline-date" style="width:40px;display:none">{{:beginTime}}</label>
					<ul class="timeline">
						<li>
							<div class="row">
								<div class="col-xs-12 col-sm-6 col-md-6 "><span style="margin-right:2em">来</span>源：<span>{{:channelName}}</span></div>
								<div class="col-xs-12 col-sm-6 col-md-6 ">处理坐席：<span>{{:agentName}}</span></div>
								<div class="col-xs-12 col-sm-6 col-md-6 ">开始时间：{{:beginTime}}</div>
								<div class="col-xs-12 col-sm-6 col-md-6 ">结束时间：{{:entTime}}</div>
								<div class="col-xs-12 col-sm-12 col-md-12 ">服务描述：{{:consultOrderContent}}</div>
								
							</div>
							
						</li>
						
					</ul>
				</div>
				<div class="clearfix clear">
					<a class="timeline-history" data-serialid="{{:serialId}}"  data-servicetime="{{:beginTime}}"  style="float:left;width:50%">聊天记录</a>
					<a href="javascript:;" class="timeline-history" onclick="showTreval('{{:chatSessionId}}')" style="float:left;width:50%;border-left:1px solid #ddd;box-sizing:border-box">轨迹</a>
				</div>
				<div class="row-history"></div>
			</div>
		</div>		
		{{/for}}
		{{if length==0}}
		<p class="text-center">暂无记录</p>
		{{/if}}
	</script>
	<script id="chat-his-message-template" type="text/x-jsrender">
		{{for data}}
		<div class="timeline-message {{if sender && sender != 'user'}}send{{/if}}">
			<div class="sender">
				<span class="pull-right">{{:msgTime}}</span>
				<span class="">{{if sender && sender == 'agent'}}客服{{else sender == 'robot'}}机器人{{else}}用户{{/if}}</span>
			</div>
			<div class="msg">
				{{formatreply:#data}}
			</div>
		</div>
		{{/for}}
		{{if data.length==0}}
		<p class="text-center" style="padding:10px">暂无记录</p>
		{{/if}}
	</script>

	<script>
	/*回复格式化*/
$.views.converters("formatreply", function(data) {
    //console.log('消息:',data);
   
    var contents = '';
    var sender =data.sender;
    if(sender == 'robot'){
		try {
			data.msgContent = JSON.parse(data.msgContent)
		} catch (e) {
		}
	}
    var c = sender =='robot'?data.msgContent.content:data.msgContent;
    switch(data.msgType){
        case 'text':
            var text = sender =='robot'?data.msgContent.content:data.msgContent;
            contents = '<div class="chat-text">'+qqWechatEmotionParser(text)+'</div>';
        break;
        case 'richText':
            var text = sender =='robot'?data.msgContent.content:data.msgContent;
            contents = '<div class="chat-richtext">'+text+'</div>';
        break;
        case 'image':
            var url = sender =='robot'?data.msgContent.url:data.msgContent;
            var title = sender =='robot'?data.msgContent.content:data.msgContent;
            //contents ='<img title="'+title+'" src="'+url+'"/>';
            contents= '<div class="chat-img-show"><img src="'+url+'"></div>';
        break;
        case 'audio':
            var url = sender =='robot'?data.msgContent.url:data.msgContent;
            var title = sender =='robot'?data.msgContent.content:'语音';
            contents = '<div data-url="'+url+'" class="chat-video"><audio title="'+title+'" src="'+url+'" controls ></audio></div>';
            
        break;
         case 'voice':
            var url = sender =='robot'?data.msgContent.url:data.msgContent;
            var title = sender =='robot'?data.msgContent.content:'语音';

            contents = '<div data-url="'+url+'" class="chat-video"><audio title="'+title+'" src="'+url+'" controls ></audio></div>';
        break;
        case 'video':
            var url = sender =='robot'?data.msgContent.url:data.msgContent;
            var title = sender =='robot'?data.msgContent.content:'视频';

            contents = '<video controls title="'+title+'" src="'+url+'" style="max-width:100%;height:200px"/>';
        break;
        case 'news':
            if(data.msgContent.picurl){
                contents = '<a target="_blank" title="'+data.msgContent.content+'" href="'+data.msgContent.url+'"><p>'+data.msgContent.content+'</p><img src="'+data.msgContent.picurl+'" /></a>';
            }else{
                contents = '<a target="_blank" title="'+data.msgContent.content+'" href="'+data.msgContent.url+'"><p>'+data.msgContent.content+'</p></a>';
            }
        break;
        default:
        contents = '<div>'+c+'</div>';

    }
    
    return contents.replace(/[\n\r]/g,'<br>');
});

		$.views.converters("msg", function(msgContent,msgType) {
			if( msgType == 'text'){
				msgContent = qqWechatEmotionParser(msgContent)
				return '<div>'+msgContent+'</div>';
			}else if( msgType == 'image'){
				return '<img src="'+msgContent+'">';
			}else if(msgType == 'video'){
				return '<div data-video="'+msgContent+'" class="chat-video"></div>'
			}
		});
	var sessionid='${param.sessionId}';
	$("input[name='pageIndex']").val(1);
	function dataShow(result){
		if(result.state){
			var tmp = $.templates("#chat-his-template");
			var html = tmp.render(result);
			$("#timeline").html(html);
			var totalPage = result.totalPage;
			var total = result.total;
			$(".totalRowV").val(total)
			$(".totalPageV").val(totalPage)
			$(".totalRow").html(total)
			$(".totalPage").html(totalPage)
			var pageIndex = $("input[name='pageIndex']").val();
			pageIndex = parseInt(pageIndex)
			if(totalPage>0){
				var html = "";
				html += '<li class="next"><a href="javascript:void(0)" onclick="dataPage(0,1)" pagenum="1">首页</a></li>';
				if(pageIndex==1){
					html += '<li class="disabled"><a class="paginate_button previous disabled" style="color: #777;cursor: not-allowed;background-color: #fff;border-color: #ddd;">«</a></li>'
				}else{
					html += '<li class="next"><a href="javascript:void(0)" onclick="dataPage(1,1)"  pagenum="1">«</a></li>';
				}
				if(totalPage<=5) {
					for (var i = 1; i <= totalPage; i++) {
						if (i == pageIndex) {
							html += '<li class="active"><a href="javascript:void(0)" pagenum="' + i + '">' + i + '</a></li>';
						} else {
							html += '<li class="previous"><a href="javascript:void(0)" onclick="dataPage(0,' + i + ')" pagenum="' + i + '">' + i + '</a></li>';
						}
					}
				}else if(pageIndex+4>totalPage){
					for(var i =totalPage-4;i<=totalPage;i++){
						if(i==pageIndex){
							html += '<li class="active"><a href="javascript:void(0)" pagenum="'+i+'">'+i+'</a></li>';
						}else{
							html += '<li class="previous"><a href="javascript:void(0)" onclick="dataPage(0,'+i+')" pagenum="'+i+'">'+i+'</a></li>';
						}
					}
				}else{
					for(var i =pageIndex;i<=pageIndex+4;i++){
						if(i==pageIndex){
							html += '<li class="active"><a href="javascript:void(0)" pagenum="'+i+'">'+i+'</a></li>';
						}else{
							html += '<li class="previous"><a href="javascript:void(0)" onclick="dataPage(0,'+i+')" pagenum="'+i+'">'+i+'</a></li>';
						}
					}
				}
				if(pageIndex==totalPage){
					html += '<li class="disabled"><a class="paginate_button previous disabled" style="color: #777;cursor: not-allowed;background-color: #fff;border-color: #ddd;">»</a></li>'
				}else{
					html += '<li class="next"><a href="javascript:void(0)" onclick="dataPage(1,2)" pagenum="1">»</a></li>';
				}
				html += '<li class="next"><a href="javascript:void(0)" onclick="dataPage(0,'+totalPage+')" pagenum="'+totalPage+'">尾页</a></li>';
				$(".pageNumV").html(html);
			}else{
				$(".pageNumV").html("");
				$("#page").addClass("hidden");
				$("#timeline").html("<div style='padding:5px;text-align:center;'>暂无数据</div>");
			}
		}else{
			$(".pageNumV").html("");
			$("#page").addClass("hidden");
			var msg = result.msg;
			if(!msg){
				msg = "暂无数据"
			}
			$("#timeline").html("<div style='padding:5px;text-align:center;'>"+msg+"</div>");
		}
	}
	$("select[name='pageSize']").change(function(){
		custHistChatList1(sessionid,dataShow);
	})

	function dataPage(type,page){
		if(type==0){
			$("input[name='pageIndex']").val(page);
		}else{
			var pageIndex = $("input[name='pageIndex']").val();
			pageIndex = parseInt(pageIndex)
			if(page==1){
				$("input[name='pageIndex']").val(pageIndex-1);
			}else{
				$("input[name='pageIndex']").val(pageIndex+1);
			}
		}
		custHistChatList1(sessionid,dataShow);
	}
		$(function(){
			
			custHistChatList1(sessionid,dataShow);

			$('body').on("click",".timeline-history",function(){
				var t = $(this);
				var serialId = t.data('serialid');
					var serviceTime = t.data('servicetime');
					getHisList2(serialId,serviceTime)
				/*if(t.hasClass('loaded')){
					t.parents('.timeline-content').find('.row-history').toggle();
				}else{
					var serialId = t.data('serialid');
					var serviceTime = t.data('servicetime');
					getHisList(serialId,serviceTime)
				}*/
			});
			
		});
		// 获得当天历史聊天记录
		function custHistChatList1(sessionId,callback){
			if(!sessionId){return false;}
			var data = {
				pageIndex:$("input[name='pageIndex']").val(),
				pageSize:$("select[name='pageSize']").val(),
				sessionId:sessionId,
				mobile:'${param.mobile}',
				accountType:'${param.accountType}',
				account:'${param.account}',
				channelType:'${param.channelType}',
				chatSessionId:'${param.chatSessionId}'
			};
			
			/* $.ajax({  
		    	type: 'POST',  
		  		url: '/online/servlet/order?action=getResultList',
		  		data: $("#"+fromId).serialize(),
		  		dataType: "json",
		  		success: function(result){ 
			   		if(result.data&&result.data.respData){
			   			console.log(result.data);
			   			console.log(result.data.respData);
//				   		renderPage(result.data,'afterOrderTpl','contentDivId');
				   		renderPage(result.data,'afterOrderTpl','contentDivId');
			   		}else{
				   		renderFooter('contentDivId');
			        }
		        }  
		  	}); */ 
			$.ajax({
				dataType:'json',
				//url: CallControl.getContextPath() + '/yc-ccbar/servlet/mediaEvent?action=custHistChatList',
				url:'/online/servlet/contact?action=custHistChatList',
				cache:false,
	            contentType : "application/x-www-form-urlencoded; charset=UTF-8",
	            data:data,
				timeout : 15000,	//5秒超时
				success:function(result){
					if ($.isFunction(callback)) { callback(result); }
					if(result.data.code == 'succ'){
						
					}else{
						
					}
					//custHistChatList
				},
				error:function( XMLHttpRequest, textStatus,	errorThrown ){}
			});
		}

		function getHisList(serialId,serviceTime){
			parent.ChatControl.loadHistMessage(serialId,'',function(result){
				
				if(result.state){
					var tmp = $.templates("#chat-his-message-template");
					var html = tmp.render(result);
					$("#"+serialId).find('.row-history').html(html)
					$("#"+serialId).find('.timeline-history').addClass('loaded')
				}
			});
		}
		function getHisList2(serialId,serviceTime){
			parent.ChatControl.loadHistMessage(serialId,'',function(result){
				
				if(result.state){
					var tmp = $.templates("#chat-his-message-template");
					var html = tmp.render(result);
					html = '<div class="row-history">'+html+'</div>';
					openHistory(html);
				}
			});
		}

		

		function openHistory(content){
			layer.open({
		        content:content,
		        area:['100%','100%'],
		        title:'聊天记录',
		        offset:'rb',
		        btn: [],
		        skin:'chat-history',
		        shade: 0
		        
		    });
		}

		function showTreval (chatSessionId,size){
			layer.open({type:2,title:'用户轨迹',content:'/online/pages/online/contactHistory/online-travl.jsp?chatSessionId='+chatSessionId,area:size||['100%','100%']})
		}
	</script>
</body>
</html>