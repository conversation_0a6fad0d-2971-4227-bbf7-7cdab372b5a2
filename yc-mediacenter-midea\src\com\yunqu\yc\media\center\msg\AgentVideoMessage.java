package com.yunqu.yc.media.center.msg;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.Agent;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.model.UserSession;
import com.yunqu.yc.media.center.model.VideoSession;
import com.yunqu.yc.media.center.mqclient.ProducerBroker;
import com.yunqu.yc.media.center.util.CacheUtil;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;
import org.easitline.common.utils.string.StringUtils;

import java.util.HashMap;
import java.util.Map;

/**
 * 坐席发送的视频事件消息处理
 * <AUTHOR>
 *
 */
public class AgentVideoMessage {

	private static Map<String, AgentVideoMessage> agentVideoMaps = new HashMap<String, AgentVideoMessage>();
	
	String agentId;
	
	public AgentVideoMessage(String agentId) {
		this.agentId = agentId;
	}

	public synchronized static AgentVideoMessage getInstance(String agentId) {
		AgentVideoMessage agentVideoMessage = agentVideoMaps.get(agentId);
		if(agentVideoMessage!=null) {
			return agentVideoMessage;
		}
		synchronized (agentId.intern()){
			agentVideoMessage = new AgentVideoMessage(agentId);
			agentVideoMaps.put(agentId,agentVideoMessage);
			return agentVideoMessage;
		}
	}
	
	public synchronized void doMessage(JSONObject jsonObject) throws Exception {
		String command = jsonObject.getString("command");
		String agentId 		= jsonObject.getString("agentId");
		String entId 		= jsonObject.getString("entId");
		String sessionId 	= jsonObject.getString("sessionId");
		
		EntContext entContext = EntContext.getContext(entId);
		
		Agent agent = entContext.getAgentInfo(agentId);
		
		UserSession user = agent.getServiceUser(sessionId);
		
		if(user==null) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> agent service user is null!");
			user = agent.getVideoUser();
		}
		
		if(user==null) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> UserSession is null!");
			return;
		}
		
		MessageModel messageModel = user.getMessageModel().newModel();
		messageModel.setMsgType("event");
		messageModel.setEvent(command);
		messageModel.setAgentId(agentId);
		
		JSONObject channelConfig = user.getChannel().getChannelConfig();
		String videoApiType = channelConfig.getString("VIDEO_API_TYPE");//yq-webrtc 云趣自研，tx-trtc 腾讯云TRTC
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] videoApiType["+videoApiType+"] << "+jsonObject.toJSONString());
		
		if("tx-trtc".equals(videoApiType)) {
			this.txtrtc(messageModel, agent, user, jsonObject, command);
		}else {
			this.yqwebrtc(messageModel, agent, user, jsonObject, command);
		}
		
	}
	
	/**
	 *  使用云趣视频SDK，美居app
	 * @param messageModel
	 * @param agent
	 * @param serviceUser
	 * @param command
	 * @throws Exception
	 */
	public void yqwebrtc(MessageModel messageModel,Agent agent,UserSession user,JSONObject jsonObject,String command) throws Exception {
		String respCommandKey = "media_"+command+"_"+agentId;
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		String agentId = agent.getAgentId();
		String sessionId = user.getSessionId();
		String defMsgType = "videoCall";//为了兼容美居app
		//坐席发送视频邀请
		if("inviteVideo".equalsIgnoreCase(command)) {
			//坐席繁忙，该坐席有未结束的视频通话
			UserSession videoUser = agent.getVideoUser();
			if(videoUser!=null) {
				if(StringUtils.equals(sessionId, videoUser.getSessionId())) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 视频邀请失败：重复邀请！");
					CacheUtil.put(respCommandKey, "002");
					return;
				}
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 坐席繁忙，该坐席有未结束的视频通话！");
				CacheUtil.put(respCommandKey, "002");
				return;
			}
			
			//锁定坐席，坐席被邀请期间不接受其他邀请
			boolean setVideoUser = agent.setVideoUser(user);
			if(!setVideoUser) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 视频邀请失败：当前坐席正在视频通话中！");
				return;
			}
			
			boolean inviteFalg = user.inviteVideo(2);
			if(!inviteFalg) {
				//视频邀请失败：当前用户正在视频通话中
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 视频邀请失败：当前用户正在视频通话中！");
				agent.removeVideoUser(sessionId);
				return;
			}
			messageModel.setMsgContent("来自客服的视频邀请");
			CacheUtil.put(respCommandKey, "000");
			messageModel.setMsgType(defMsgType);
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
		//取消邀请
		}else if("cancelInviteVideo".equalsIgnoreCase(command)) {
			JSONObject clearMsg = user.cancelInviteVideo(24);
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setMsgType(defMsgType);
			messageModel.setMsgContent(clearMsg.getString("userText"));
			String msg = messageModel.toString();
			ProducerBroker.sendUserMessage(sessionId,msg);
			//系统回复语直接入库
			UserSession.getInstance(sessionId).saveMessage(JSONObject.parseObject(msg),4);

			messageModel.setMsgType("event");
			messageModel.setEvent("System");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			agentVideoMaps.remove(agentId);
		//坐席同意用户的视频邀请
		}else if("agreeInviteVideo".equalsIgnoreCase(command)){
			VideoSession videoSession = user.getVideoSession();
			if(videoSession==null||!videoSession.isInvite()) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 坐席取消邀请，当前视频会话非邀请状态！");
				return;
			}
			messageModel.setMsgContent("坐席同意视频邀请");
			messageModel.setMsgType(defMsgType);
//			user.startVideo();
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
		}else if("disagreeInviteVideo".equalsIgnoreCase(command)){
//			{"clearCause":11,"userText":"通话时长 00:05:00","agentText":"通话时长 00:05:00","time":300,"callType":1}
			JSONObject clearMsg = user.cancelInviteVideo(22);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setMsgContent(clearMsg.getString("userText"));
			messageModel.setMsgType(defMsgType);
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString());
			agentVideoMaps.remove(agentId);
		}else if("videoFail".equalsIgnoreCase(command)){
			JSONObject clearMsg = user.closeVideo(31);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setMsgContent(clearMsg.getString("userText"));
			messageModel.setMsgType(defMsgType);
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString());
			agentVideoMaps.remove(agentId);
		}else if("videoSuccess".equalsIgnoreCase(command)) {
			messageModel.setMsgContent("视频连接成功");
			user.startVideo();
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
		}else if("closeVideo".equalsIgnoreCase(command)) {
			VideoSession videoSession = user.getVideoSession();
			if(videoSession==null) {
				return;
			}
			
			if(StringUtils.equals(agentId, videoSession.getThirdAgentId())) {
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] 三方坐席退出会议！");
				videoSession.closeThirdAgent();
				return;
			}
			boolean videoChat = videoSession.isVideoChat();
			JSONObject clearMsg = user.closeVideo(21);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
			if(callType==1) {
				//通知坐席
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			}else {
				//坐席发起的视频聊天，需要先发送一条event=closeVideo,msgContent=""，再发送一条event=System,msgContent="通话时长 00:35"
				messageModel.setMsgContent("");
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
				
				messageModel.setEvent("System");
				messageModel.setMsgContent(clearMsg.getString("agentText"));
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			}
			
			//发送视频满意度
			if(videoChat) {
	//			/yc-mediagw/pages/appraise/appraise.jsp
				JSONObject videoConfig = entContext.getVideoConfig();
				String videoSatisfyUrl = videoConfig.getString("VIDEO_SATISFY_URL");
				if(StringUtils.isNotBlank(videoSatisfyUrl)) {
					String channelKey = user.getChannel().getChannelKey();
					if(videoSatisfyUrl.indexOf("?")>=0) {
						videoSatisfyUrl = videoSatisfyUrl+"&serialId="+user.getChatSessionId()+"&agentPhone="+agent.getAgentPhone()+"&channelKey="+channelKey+"&sessionId="+sessionId;
					}else {
						videoSatisfyUrl = videoSatisfyUrl+"?serialId="+user.getChatSessionId()+"&agentPhone="+agent.getAgentPhone()+"&channelKey="+channelKey+"&sessionId="+sessionId;
					}
					
					messageModel.setVideoSatisfyUrl(videoSatisfyUrl);
				}
			}
			//通知用户
			messageModel.setMsgContent(clearMsg.getString("userText"));
			messageModel.setMsgType(defMsgType);
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			messageModel.setVideoSatisfyUrl("");
			agentVideoMaps.remove(agentId);
		}else if("timeoutVideo".equalsIgnoreCase(command)) {
			JSONObject clearMsg = user.cancelInviteVideo(23);
			int callType = clearMsg.getIntValue("callType");
			messageModel.setSender(callType==1?"user":"agent");//用于标记视频发起者
			messageModel.setSerialId(clearMsg.getString("chatId"));
//			messageModel.setEvent("System");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			messageModel.setEvent("timeoutVideo");
			messageModel.setMsgType(defMsgType);
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString());
			agentVideoMaps.remove(agentId);
			return ;
		//视频会议创建成功后，坐席端需要发送“startVideo”，需要携带视频会议confId,recordId,chatSessionId，用于更新视频通话记录
		}else if("startVideo".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] 收到视频会议创建成功事件"+jsonObject.toJSONString());
			user.startVideo();
			int callType = user.getVideoSession().getCallType();
			user.getVideoSession().startMeeting(jsonObject.getString("recordId"), jsonObject.getString("confId"));
			//20210409自动发送一条客服消息
			//20210427只有用户端发起的视频才需要推送客服消息
//			尊敬的XX会员（匹配正确的会员等级，如果不是会员就写尊敬的用户），我是您的专属视频客服XXXX（XX是工号），很高兴为您服务！
			if(callType==1) {
				messageModel.setSerialId(RandomKit.uniqueStr());
				String videoStartMsg = "尊敬的#levelName#，我是您的专属视频客服#agentName#，很高兴为您服务！";
				String levelName = user.getUserInfo().getString("levelName");
				levelName = StringUtils.isNotBlank(levelName)?levelName:"客户";
				videoStartMsg = videoStartMsg.replace("#levelName#", levelName).replace("#agentName#", agent.getAgentPhone());
				messageModel.setSender("agent");//用于标记视频发起者
				messageModel.setEvent("System");
				messageModel.setMsgType("event");
				messageModel.setMsgContent(videoStartMsg);
				ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
				
				messageModel.setSender("agent");//用于标记视频发起者
				messageModel.setEvent("agent");
				messageModel.setMsgType("text");
				ProducerBroker.sendUserMessage(sessionId, messageModel.toString());
				//2.0#20210517-1
				//插入一条消息记录
				EasyCalendar cal = EasyCalendar.newInstance();
				EasyRecord record = new EasyRecord(entContext.getTableName("CC_MEDIA_CHAT_RECORD"),"CHAT_ID");
				record.setPrimaryValues(messageModel.getSerialId());
				record.set("DATE_ID",cal.getDateInt());
				record.set("ENT_ID",messageModel.getEntId());
				record.set("AGENT_ID",agent.getAgentId());
				record.set("CHAT_SESSION_ID",user.getChatSessionId());  //如果是机器人的信息，则直接保存为当前的sessionId
				record.set("CUST_SESSION_ID",sessionId);
				record.set("MSG_TIME",cal.getDateTime("-"));
				record.set("MSG_TIMESTAMP",cal.getTimeInMillis());
				record.set("MSG_TYPE","text");
				record.set("MSG_CONTENT",messageModel.getMsgContent());
				record.set("SENDER",2);//发送者类型 ，1 客户 2 坐席 3 机器人 4 系统
				QueryFactory.getWriteQuery(messageModel.getEntId()).save(record);
			}
			agentVideoMaps.remove(agentId);
			
		//开始标记：startVideoTag
		}else if("startVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] 收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().startTag(jsonObject);
		//结束标记：stopVideoTag
		}else if("stopVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] 收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().stopTag(jsonObject);
		//删除标记：dropVideoTag
		}else if("dropVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] 收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().dropTag(jsonObject);
		}
	}
	
	
	
	/**
	 * 使用腾讯云视频SDK，美的服务号视频小程序，只能是坐席发起视频邀请
	 * @param messageModel
	 * @param agent
	 * @param serviceUser
	 * @param command
	 * @throws Exception
	 */
	public void txtrtc(MessageModel messageModel,Agent agent,UserSession user,JSONObject jsonObject,String command) throws Exception {
		String respCommandKey = "media_"+command+"_"+agentId;
		EntContext entContext = EntContext.getContext(messageModel.getEntId());
		JSONObject channelConfig = user.getChannel().getChannelConfig();
		String agentId = agent.getAgentId();
		String sessionId = user.getSessionId();
		messageModel.setMsgType("text");
		//坐席发送视频邀请
		if("inviteVideo".equalsIgnoreCase(command)) {
			//坐席繁忙，该坐席有未结束的视频通话
			UserSession videoUser = agent.getVideoUser();
			if(videoUser!=null) {
				if(StringUtils.equals(sessionId, videoUser.getSessionId())) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserVideoMessage("+command+") error  -> 视频邀请失败：重复邀请！");
					CacheUtil.put(respCommandKey, "002");
					return;
				}
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") error  -> 坐席繁忙，该坐席有未结束的视频通话！");
				CacheUtil.put(respCommandKey, "002");
				return;
			}

			//TODO 判断用户端的版本号app_version
			//...

			//锁定坐席，坐席被邀请期间不接受其他邀请
			boolean setVideoUser = agent.setVideoUser(user);
			if(!setVideoUser) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") error  -> 视频邀请失败：当前坐席正在视频通话中！");
				return;
			}
			
			boolean inviteFalg = user.inviteVideo(2);
			if(!inviteFalg) {
				//视频邀请失败：当前用户正在视频通话中
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") error  -> 视频邀请失败：当前用户正在视频通话中！");
				agent.removeVideoUser(sessionId);
				return;
			}
			messageModel.setMsgContent("来自客服的视频邀请");
			CacheUtil.put(respCommandKey, "000");
			//20210412 用户信息中增加区分视频sdk类型
			String miniprogramPage = channelConfig.getString("MINIPROGRAM_PAGE");//小程序页面地址
			String miniprogramUrl = channelConfig.getString("MINIPROGRAM_URL");//小程序地址
			String miniprogramAppId = channelConfig.getString("MINIPROGRAM_APPID");//小程序appId
			String thumbUrl = channelConfig.getString("MINIPROGRAM_THUMB_URL");//小程序缩略图地址

			//拼接小程序页面地址
			String roomId = jsonObject.getString("roomId");//视频会议房间号
			String miniPagePath = jsonObject.getString("miniPagePath");//视频会议房间页面地址
			if(StringUtils.isNotBlank(miniPagePath)) miniprogramPage = miniPagePath;
			if(miniprogramPage.indexOf("?")>-1) {
				miniprogramPage += "&roomId="+roomId+"&agentPhone="+agent.getAgentPhone()+"&openId="+user.getSessionId()+"&chatSessionId="+user.getChatSessionId();
			}else {
				miniprogramPage += "?roomId="+roomId+"&agentPhone="+agent.getAgentPhone()+"&openId="+user.getSessionId()+"&chatSessionId="+user.getChatSessionId();
			}
			VideoSession videoSession = user.getVideoSession();
			JSONObject msgContentObj = new JSONObject();
			msgContentObj.put("toUser", user.getSessionId());
			msgContentObj.put("title", "客服邀请您进行视频通话...");
			msgContentObj.put("msgType", "miniprogrampage");
			msgContentObj.put("miniProgramPagePath", miniprogramPage);
			msgContentObj.put("miniprogramUrl", miniprogramUrl);
			msgContentObj.put("miniProgramAppId", miniprogramAppId);
			msgContentObj.put("thumbUrl", thumbUrl);
			msgContentObj.put("chatId", videoSession.getChatId());
			messageModel.setMsgType("miniprogrampage");
			messageModel.setMsgContent(msgContentObj.toJSONString());
			messageModel.setEvent("agent");

			//保存小程序卡片消息
			JSONObject msgObj = new JSONObject();
			msgObj.put("clearCause", 0);
			msgObj.put("userText", "客服邀请您进行视频通话...");
			msgObj.put("agentText", "视频通话");
			msgObj.put("time", 0);
			msgObj.put("chatId", videoSession.getChatId());
			msgObj.put("callType", videoSession.getCallType());
			msgObj.put("miniProgram", msgContentObj);
			videoSession.setChatMsg(msgObj.toJSONString(),false);
			//send msg to user
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			
			//20210416 当坐席端推送视频会议链接时，附带推送一条中止挂断微信消息。
			//该微信推送消息文本内容中含有【点此挂断】按钮，用于用户点击【点此挂断】按钮来拒绝坐席端发起的视频请求，而无需用户直接点击进入视频会议室内点击挂断操作
//				CC系统自动生成文本消息，例如：
			//2.0#20210428-1
			Thread.sleep(1000);
			String msgContent = "如当前不便接通，可<a href=\"weixin://bizmsgmenu?msgmenucontent=【点此挂断】&msgmenuid=999900\">【点此挂断】</a>；1分钟内未接通，邀请将自动关闭";
			messageModel.setMsgContent(msgContent);
			messageModel.setEvent("agent");
			messageModel.setMsgType("text");
			String touserMsg = messageModel.toString(RandomKit.randomStr());
			ProducerBroker.sendUserMessage(sessionId, touserMsg);
			user.saveMessage(JSONObject.parseObject(touserMsg),4);
			return;
		//取消邀请
		}else if("cancelInviteVideo".equalsIgnoreCase(command)) {
			JSONObject clearMsg = user.cancelInviteVideo(24);
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setMsgContent(clearMsg.getString("userText"));
			String msg = messageModel.toString(RandomKit.randomStr());
			ProducerBroker.sendUserMessage(sessionId,msg);
			//系统回复语直接入库
			UserSession.getInstance(sessionId).saveMessage(JSONObject.parseObject(msg),4);

			messageModel.setMsgType("event");
			messageModel.setEvent("System");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			agentVideoMaps.remove(agentId);
		}else if("closeVideo".equalsIgnoreCase(command)) {
			VideoSession videoSession = user.getVideoSession();
			if(videoSession==null) {
				return;
			}
			JSONObject clearMsg = user.closeVideo(21);
			//通知坐席，坐席发起的视频聊天，需要先发送一条event=closeVideo,msgContent=""，再发送一条event=System,msgContent="通话时长 00:35"
			messageModel.setSerialId(clearMsg.getString("chatId"));
			messageModel.setSender("agent");//用于标记视频发起者
			messageModel.setMsgType("event");
			messageModel.setMsgContent("");
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			messageModel.setEvent("System");
			messageModel.setMsgContent(clearMsg.getString("agentText"));
			ProducerBroker.sendAgentMessage(agentId, messageModel.toAgentString());
			
			//通知用户
			messageModel.setMsgType("text");
			messageModel.setMsgContent(clearMsg.getString("userText"));
			ProducerBroker.sendUserMessage(sessionId, messageModel.toString(RandomKit.randomStr()));
			messageModel.setVideoSatisfyUrl("");
			agentVideoMaps.remove(agentId);
		//视频会议创建成功后，坐席端需要发送“startVideo”，需要携带视频会议confId,recordId,chatSessionId，用于更新视频通话记录
		}else if("startVideo".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage<"+command+"> 收到视频会议创建成功事件"+jsonObject.toJSONString());
			user.startVideo();
			user.getVideoSession().startMeeting(jsonObject.getString("recordId"), jsonObject.getString("confId"));
			
		//开始标记：startVideoTag
		}else if("startVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage<"+command+"> 收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().startTag(jsonObject);
		//结束标记：stopVideoTag
		}else if("stopVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage<"+command+"> 收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().stopTag(jsonObject);
		//删除标记：dropVideoTag
		}else if("dropVideoTag".equalsIgnoreCase(command)) {
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage<"+command+"> 收收到视频打标记事件："+jsonObject.toJSONString());
			user.getVideoSession().dropTag(jsonObject);
		}
	}
	
	
	/**
	 * 邀请三方坐席
	 * @param jsonObject
	 */
	public static void inviteAgent(JSONObject jsonObject) {
		String command = jsonObject.getString("command");
		String agentId 			= jsonObject.getString("agentId");
		String targetAgentId 	= jsonObject.getString("targetAgentId");//目标坐席id
		String entId 			= jsonObject.getString("entId");
		String sessionId 		= jsonObject.getString("sessionId");
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] << "+jsonObject.toJSONString());
		
		EntContext entContext = EntContext.getContext(entId);
		String defMsgType = "event";
		
		String respCommandKey = "media_"+command+"_"+agentId;
		
		//坐席发送视频邀请
		if("inviteAgent".equalsIgnoreCase(command)) {
			
			Agent agent = entContext.getAgentInfo(agentId);
			UserSession user = agent.getServiceUser(sessionId);
			MessageModel messageModel = user.getMessageModel().newModel();
			
			if(StringUtils.isBlank(targetAgentId)) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 邀请三方失败，未选择目标坐席！");
				CacheUtil.put(respCommandKey, "001");
				return;
			}
			
			Agent targetAgent = entContext.getAgentInfo(targetAgentId);
			if(targetAgent==null||!targetAgent.online()) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 邀请三方失败，目标坐席不在线！");
				CacheUtil.put(respCommandKey, "002");
				return;
			}
			
			if(targetAgent.getVideoUser()!=null) {
				CacheUtil.put(respCommandKey, "003");
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 邀请三方失败，目标坐席繁忙！");
				return;
			}
			//锁定坐席，坐席被邀请期间不接受其他邀请
			boolean setVideoUser = targetAgent.setVideoUser(user);
			if(!setVideoUser) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] AgentVideoMessage("+command+") ["+agentId+"] ["+sessionId+"] error  -> 视频邀请失败：当前坐席正在视频通话中！");
				return;
			}
			
			user.getVideoSession().inviteThirdAgent(targetAgentId);
			
			messageModel.setEvent(command);
			messageModel.setMsgType(defMsgType);
			messageModel.setAgentId(targetAgentId);
			messageModel.setMsgContent("来自坐席["+agent.getAgentName()+"]的视频邀请");
			CacheUtil.put(respCommandKey, "000");
			JSONObject parseObject = JSONObject.parseObject(messageModel.toAgentString());
			parseObject.getJSONObject("data").put("fromAgentId", agentId);
			
			ProducerBroker.sendAgentMessage(targetAgentId, parseObject.toJSONString());
		//被邀请坐席，同意视频邀请
		}else if("agreeInviteAgent".equalsIgnoreCase(command)){
			String fromAgentId 	= jsonObject.getString("fromAgentId");//来源坐席id
			Agent fromAgent = entContext.getAgentInfo(fromAgentId);
			Agent targetAgent = entContext.getAgentInfo(agentId);
			UserSession user = fromAgent.getServiceUser(sessionId);
			MessageModel messageModel = user.getMessageModel().newModel();
			messageModel.setMsgType(defMsgType);
			messageModel.setEvent(command);
			messageModel.setAgentId(fromAgentId);
			JSONObject parseObject = JSONObject.parseObject(messageModel.toAgentString());
			JSONObject dataObject = parseObject.getJSONObject("data");
			dataObject.put("targetAgentId", agentId);
			JSONObject _userInfo = dataObject.getJSONObject("userInfo");
			_userInfo.put("userData", jsonObject.getJSONObject("userData"));
			dataObject.put("userInfo", _userInfo);
			
			user.getVideoSession().agreeInviteThirdAgent();
			dataObject.put("msgContent", "坐席["+agentId+"]同意视频邀请");
			//发送来源坐席
			ProducerBroker.sendAgentMessage(fromAgentId, parseObject.toJSONString());
		}else if("disagreeInviteAgent".equalsIgnoreCase(command)){
			String fromAgentId 	= jsonObject.getString("fromAgentId");//来源坐席id
			Agent targetAgent = entContext.getAgentInfo(agentId);
			targetAgent.removeVideoUser(sessionId);
			Agent fromAgent = entContext.getAgentInfo(fromAgentId);
			UserSession user = fromAgent.getServiceUser(sessionId);
			user.getVideoSession().disagreeInviteThirdAgent();
			
			MessageModel messageModel = user.getMessageModel().newModel();
			messageModel.setMsgType(defMsgType);
			messageModel.setEvent(command);
			messageModel.setAgentId(fromAgentId);
			messageModel.setMsgContent("坐席["+agentId+"]拒绝视频邀请");
			JSONObject parseObject = JSONObject.parseObject(messageModel.toAgentString());
			parseObject.getJSONObject("data").put("targetAgentId", agentId);
			ProducerBroker.sendAgentMessage(fromAgentId, parseObject.toJSONString());
		}else if("timeoutInviteAgent".equalsIgnoreCase(command)) {
			String fromAgentId 	= jsonObject.getString("fromAgentId");//来源坐席id
			Agent fromAgent = entContext.getAgentInfo(fromAgentId);
			Agent targetAgent = entContext.getAgentInfo(agentId);
			targetAgent.removeVideoUser(sessionId);
			UserSession user = fromAgent.getServiceUser(sessionId);
			user.getVideoSession().disagreeInviteThirdAgent();
			MessageModel messageModel = user.getMessageModel().newModel();
			messageModel.setMsgType(defMsgType);
			messageModel.setEvent(command);
			messageModel.setAgentId(fromAgentId);
			messageModel.setMsgContent("坐席未接听");
			JSONObject parseObject = JSONObject.parseObject(messageModel.toAgentString());
			parseObject.getJSONObject("data").put("targetAgentId", agentId);
			ProducerBroker.sendAgentMessage(fromAgentId, parseObject.toJSONString());
		//取消邀请
		}else if("cancelInviteAgent".equalsIgnoreCase(command)) {
			String fromAgentId 	= jsonObject.getString("fromAgentId");//来源坐席id
			Agent fromAgent = entContext.getAgentInfo(fromAgentId);
			Agent targetAgent = entContext.getAgentInfo(agentId);
			targetAgent.removeVideoUser(sessionId);
			UserSession user = fromAgent.getServiceUser(sessionId);
			user.getVideoSession().cancelInviteThirdAgent();
			MessageModel messageModel = user.getMessageModel().newModel();
			messageModel.setMsgType(defMsgType);
			messageModel.setEvent(command);
			messageModel.setAgentId(fromAgentId);
			messageModel.setMsgContent("坐席已取消");
			JSONObject parseObject = JSONObject.parseObject(messageModel.toAgentString());
			parseObject.getJSONObject("data").put("targetAgentId", agentId);
			parseObject.getJSONObject("data").put("fromAgentId", fromAgentId);
			ProducerBroker.sendAgentMessage(agentId, parseObject.toJSONString());
			agentVideoMaps.remove(agentId);
		}
	}
	
}
