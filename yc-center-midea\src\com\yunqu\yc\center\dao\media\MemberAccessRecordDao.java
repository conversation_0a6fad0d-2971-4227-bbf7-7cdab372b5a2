package com.yunqu.yc.center.dao.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 进线接入记录DAO
 * <AUTHOR>
 */
@WebObject(name="memberAccessRecord")
public class MemberAccessRecordDao extends AppDaoContext {

    /**
     * 进线接入记录列表
     */
    @WebControl(name="list", type=Types.LIST)
    public JSONObject list(){
        String entId = param.getString("entId");
        String configId = param.getString("configId");
        String memberName = param.getString("memberName");
        String startTime = param.getString("startTime");
        String endTime = param.getString("endTime");
        String sessionId = param.getString("sessionId");
        String channelKey = param.getString("channelKey");

        EasySQL sql = this.getEasySQL("select t1.*,t2.CHANNEL_NAME");
        sql.append(" from YCBUSI.CC_MEDIA_MEMBER_RECORD t1");
        sql.append(" left join YCBUSI.CC_CHANNEL t2 on t1.CHANNEL_KEY=t2.CHANNEL_KEY and t2.ENT_ID=?", entId);
        sql.append(" where 1=1");
        
        sql.append(configId, " and t1.CONFIG_ID = ?");
        sql.append(sessionId, " and t1.SESSION_ID = ?");
        sql.append(channelKey, " and t1.CHANNEL_KEY = ?");
        
        if(StringUtils.isNotBlank(memberName)) {
            sql.append(" and (t1.MEMBER_NAME like ? or t1.LEVEL_NAMES like ?)", 
                "%" + memberName + "%", "%" + memberName + "%");
        }
        
        if(StringUtils.isNotBlank(startTime)) {
            sql.append(" and t1.CREATE_TIME >= ?", startTime);
        }
        
        if(StringUtils.isNotBlank(endTime)) {
            sql.append(" and t1.CREATE_TIME <= ?", endTime);
        }
        
        sql.append(" order by t1.CREATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }

    /**
     * 会员身份策略字典
     */
    @WebControl(name="memberStrategyDict", type=Types.DICT)
    public JSONObject memberStrategyDict(){
        String entId = param.getString("entId");
        return getDictByQuery(
            "select CONFIG_ID, MEMBER_NAME from YCUSER.CC_MEDIA_MEMBER_CONFIG order by RANK_INDEX desc",
            new Object[]{}
        );
    }

    /**
     * 渠道字典
     */
    @WebControl(name="channelDict", type=Types.DICT)
    public JSONObject channelDict(){
        String entId = param.getString("entId");
        return getDictByQuery(
            "select CHANNEL_KEY, CHANNEL_NAME from YCBUSI.CC_CHANNEL where ENT_ID=? and CHANNEL_STATE=0 order by CHANNEL_NAME",
            new Object[]{entId}
        );
    }

}
