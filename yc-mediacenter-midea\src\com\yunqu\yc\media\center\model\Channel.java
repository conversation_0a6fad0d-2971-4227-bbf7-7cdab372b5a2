package com.yunqu.yc.media.center.model;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.base.QueryFactory;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.db.impl.JSONMapperImpl;

import java.sql.SQLException;
import java.util.*;

/**
 * 渠道信息表,保存渠道配置信息
 * <AUTHOR>
 *
 */

public class Channel {
	
	/**
	 * 渠道Id
	 */
	String channelId;
	/**
	 * 企业Id
	 */
	String entId;
	/**
	 * 渠道名称
	 */
	String channelName;
	
	
	String channelKey;
	
	/**
	 * 渠道类型
	 */
	String channelType;
	/**
	 * 渠道配置，包括：配置欢迎语
	 */
	String channelConfig;
	
	/**
	 * 状态， 0 正常  1 停用
	 */
	String channelState  =  "0";
	
	AutoConfig autoConfig;
	
	JSONObject styleConfig;
	
	private Map<String,ChannelKey>  channelKeys  = new HashMap<String,ChannelKey>();
	
	public String getChannelState() {
		return channelState;
	}

	public void setChannelState(String channelState) {
		this.channelState = channelState;
	}

	public ChannelKey  getChannelKeyByCode(String keyCode){
		//MediaCenterLogger.getLogger().debug("channelKeys["+channelKeys.size()+"],input keyCode["+keyCode+"]->"+channelKeys);
		if(StringUtils.isBlank(keyCode)) return null;
		Iterator<String> iter = channelKeys.keySet().iterator();
		while(iter.hasNext()){
			ChannelKey  key = channelKeys.get(iter.next());
			//MediaCenterLogger.getLogger().debug("ChannelKey["+key.getKeyCode()+"]->"+key);
			if(keyCode.equalsIgnoreCase(key.getKeyCode())){
				return key;
			}
		}
		return null;
	}
	public AutoConfig getAutoConfig() {
		return autoConfig;
	}

	public Map<String, ChannelKey> getChannelKeys() {
		return channelKeys;
	}

	public void setChannelKeys(Map<String, ChannelKey> channelKeys) {
		this.channelKeys = channelKeys;
	}

	public String getChannelKey() {
		return channelKey;
	}

	public void setChannelKey(String channelKey) {
		this.channelKey = channelKey;
	}

	public String getChannelType() {
		return channelType;
	}

	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public ChannelKey getChannelKey(String channelKey){
		return channelKeys.get(channelKey);
	}
	
	public void addChannelKey(String keyCode,ChannelKey channelKey){
		channelKeys.put(keyCode, channelKey);
	}
	
	public String getChannelId() {
		return channelId;
	}
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	public String getChannelName() {
		return channelName;
	}
	
	public JSONObject getStyleConfig() {
		return styleConfig;
	}

	public void setStyleConfig(JSONObject styleConfig) {
		if(styleConfig==null) {
			styleConfig = new JSONObject();
		}
		this.styleConfig = styleConfig;
	}

	public JSONObject getChannelConfig() {
		return JSONObject.parseObject(channelConfig);
	}
	public void setChannelConfig(String channelConfig) {
		this.channelConfig = channelConfig;
	}
	
	public void setChannelAutoConfig(String channelAutoConfig) {
		
		JSONObject jsonObject = JSONObject.parseObject(channelAutoConfig); 
		autoConfig = new AutoConfig();
		if(jsonObject == null) return ;
		autoConfig.setAccessMsg(jsonObject.getString("VISITOR_ACCESS_MSG"));
		autoConfig.setAgentOfflineMsg(jsonObject.getString("AGENT_OFFLINE_MSG"));
		autoConfig.setAgentTimeout(jsonObject.getString("AGENT_TIMEOUT"));
		autoConfig.setAgentTimeoutMsg(jsonObject.getString("AGENT_TIMEOUT_MSG"));
		autoConfig.setAutoClose(jsonObject.getString("AUTO_CLOSESESSION_FLAG"));
		autoConfig.setAutoCloseMsg(jsonObject.getString("AUTO_CLOSESESSION_MSG"));
		autoConfig.setAutoReplay(jsonObject.getString("AUTO_REPLY_FLAG"));
		autoConfig.setBlacklistMsg(jsonObject.getString("BLACKLIST_MSG"));
		autoConfig.setCancelQueueMsg(jsonObject.getString("VISITOR_CANCELQUE_MSG"));
		autoConfig.setCloseTime(jsonObject.getString("AUTO_CLOSESESSION_TIMEOUT"));
		autoConfig.setGoodbyeMsg(jsonObject.getString("GOODBYE_MSG"));
		autoConfig.setNotWorkMsg(jsonObject.getString("SYSTEM_NOTINSERVICE_MSG"));
		autoConfig.setQueueFullMsg(jsonObject.getString("QUEUE_FULL_MSG"));
		autoConfig.setQueueMsg(jsonObject.getString("VISITOR_QUEUE_MSG"));
		autoConfig.setQueueTimeout(jsonObject.getString("VISITOR_QUEUE_TIMEOUT"));
		autoConfig.setQueueTimeoutMsg(jsonObject.getString("QUEUE_TIMEOUT_MSG"));
		autoConfig.setUserTimeout(jsonObject.getString("VISITOR_TIMEOUT"));
		autoConfig.setUserTimeoutMsg(jsonObject.getString("VISITOR_TIMEOUT_MSG"));
		autoConfig.setWelcomeMsg(jsonObject.getString("WELCOME_MSG"));
		autoConfig.setQueueNotifyTime(jsonObject.getIntValue("QUEUE_NOTIFY_TIME"));
		autoConfig.setQueueMsgList(jsonObject.getString("VISITOR_QUEUE_MSG_LIST"));
		autoConfig.setAccessMsgList(jsonObject.getString("VISITOR_ACCESS_MSG_LIST"));
		autoConfig.setInAgentAfterMsg(jsonObject.getString("IN_AGENT_AFTER_MSG"));
		autoConfig.setInQueueAfterMsg(jsonObject.getString("IN_QUEUE_MSG"));
	}
	
	
	/**
	 * 初始化标志
	 * @param init
	 */
	public void reload(){
		
		EntContext entContext = EntContext.getContext(this.entId);
		
		String sql = "select * from CC_CHANNEL where ENT_ID = ?  and  CHANNEL_ID = ? ";
		try {
			List<JSONObject> rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId,this.channelId },new JSONMapperImpl());
			if(rows == null) {
				this.setChannelState("1"); 
				return;
			}
			for(JSONObject row:rows){
				this.setChannelId(row.getString("CHANNEL_ID"));  //这里送给平台的是坐席工号，例如：8001
				this.setChannelName(row.getString("CHANNEL_NAME"));
				this.setChannelType(row.getString("CHANNEL_TYPE"));
				this.setChannelKey(row.getString("CHANNEL_KEY"));
				this.setChannelConfig(row.getString("CHANNEL_CONF"));
				this.setChannelAutoConfig(row.getString("CHANNEL_AUTO_CONF"));
				this.setChannelState(row.getString("CHANNEL_STATE"));
				this.setEntId(row.getString("ENT_ID"));
				this.setStyleConfig(row.getJSONObject("STYLE_CONF"));
			} 	
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		//this.channelKeys.clear();
		
		Set<String> keyIds = new HashSet<String>();
		
		sql = "select * from CC_CHANNEL_KEY where ENT_ID = ?  and CHANNEL_ID = ?";
		try {
			List<JSONObject> rows = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId ,this.channelId},new JSONMapperImpl());
			MediaCenterLogger.getLogger().debug("正在重新加载渠道按键，渠道key:"+this.channelKey+",按键数："+rows.size()+",key："+rows);
			for(JSONObject row:rows){
				String keyId = row.getString("KEY_ID");
				keyIds.add(keyId);
				ChannelKey channelKey = this.channelKeys.get(keyId);
				if(channelKey == null){
					channelKey = new ChannelKey();
					channelKey.setChannelId(row.getString("CHANNEL_ID"));  //这里送给平台的是坐席工号，例如：8001
					channelKey.setKeyCode(row.getString("KEY_CODE"));
					channelKey.setKeyName(row.getString("KEY_NAME"));
					channelKey.setKeyType(row.getString("KEY_TYPE"));
					channelKey.setKeyContent(row.getString("KEY_CONTENT"));
					channelKey.setEntId(entId);
					channelKey.setChannelKeyId(row.getString("KEY_ID"));
					this.addChannelKey(keyId, channelKey);
				}else{
					channelKey.setChannelId(row.getString("CHANNEL_ID"));  //这里送给平台的是坐席工号，例如：8001
					channelKey.setKeyCode(row.getString("KEY_CODE"));
					channelKey.setKeyName(row.getString("KEY_NAME"));
					channelKey.setKeyType(row.getString("KEY_TYPE"));
					channelKey.setKeyContent(row.getString("KEY_CONTENT"));
					channelKey.setEntId(this.entId);
					channelKey.setChannelKeyId(row.getString("KEY_ID"));
				}
			} 	
			Set<String> _keyIds = new HashSet<String>();
			for(String keyId:this.channelKeys.keySet()){
				if(!keyIds.contains(keyId)){
					_keyIds.add(keyId);
				}
			}
			for(String keyId :_keyIds){
				this.channelKeys.remove(keyId);
			}
			MediaCenterLogger.getLogger().debug("加载渠道按键完成，渠道key:"+this.channelKey+",按键数："+this.channelKeys.size()+",key："+this.channelKeys);
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
//		try {
//			
//			sql = "select KEY_ID,SKILL_GROUP_ID from  "+entContext.getTableName( "CC_SKILL_GROUP_CHANNEL")+" where  ENT_ID = ?  and  CHANNEL_ID = ?";
//			rows =  QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId ,this.channelId});
//			for(EasyRow row:rows){
//				String keyId = row.getColumnValue("KEY_ID");
//				String groupId = row.getColumnValue("SKILL_GROUP_ID");
//				ChannelKey channelKey = this.getChannelKey(keyId);
//				if(channelKey == null) continue;
//				channelKey.setSkillGroupId(groupId);
//			} 	
//		} catch (SQLException ex) {
//		}
		
		//20201126 查询每个按键关联的技能组列表，按技能组优先级（PRIORITY）排序
	    try {
	    	sql = "select KEY_ID,SKILL_GROUP_ID from  "+entContext.getTableName( "CC_SKILL_GROUP_CHANNEL")+" where ENT_ID = ? and CHANNEL_ID = ? and KEY_ID = ? order by PRIORITY desc";
	    	for (ChannelKey channelKey : this.channelKeys.values()) {
	    		String keyId = channelKey.getChannelKeyId();
	    		List<JSONObject> skillIds = QueryFactory.getReadQuery().queryForList(sql, new Object[] { entId ,this.channelId,keyId} ,new JSONMapperImpl());
	    		List<SkillGroup> skillGroupList = new ArrayList<SkillGroup>();
	    		
	    		for (JSONObject obj : skillIds) {
	    			String skillGroupId = obj.getString("SKILL_GROUP_ID");
	    			skillGroupList.add(entContext.getSkillGroup(skillGroupId));
	    		}
	    		channelKey.setQueueSkillGroups(skillGroupList);
	    		//设置优先级别最低的技能组为普通技能组
	    		if(skillGroupList.size()>0) {
	    			String groupId = skillIds.get(skillIds.size()-1).getString("SKILL_GROUP_ID");
	    			channelKey.setSkillGroupId(groupId);
	    		}
			}
		} catch (SQLException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	public String toJSONString(){
		JSONObject  jsonObject = new JSONObject();
		jsonObject.put("channelId", this.getChannelId());
		jsonObject.put("channelKey", this.getChannelKey());
		jsonObject.put("channelName", this.getChannelName());
		jsonObject.put("channelType", this.getChannelType());
		jsonObject.put("channelKeys", channelKeys);
		return jsonObject.toJSONString();
	}
	
	public String toString(){
		JSONObject  jsonObject = new JSONObject();
		jsonObject.put("channelId", this.getChannelId());
		jsonObject.put("channelKey", this.getChannelKey());
		jsonObject.put("channelName", this.getChannelName());
		jsonObject.put("channelType", this.getChannelType());
		return jsonObject.toJSONString();
	}
	
}
