package com.yunqu.yc.media.center.mqclient;

import com.yunqu.yc.media.center.listener.GolbalThreadState;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.activemq.BrokerMessageListener;
import org.easitline.common.core.context.AppContext;

import javax.jms.Message;
import javax.jms.MessageConsumer;
import javax.jms.ObjectMessage;
import java.util.concurrent.BlockingQueue;
import java.util.concurrent.LinkedBlockingQueue;
import java.util.concurrent.TimeUnit;


public class ConsumerBroker implements Runnable {
	
	private Broker broker = null;
	
	private BlockingQueue<Message> messageQueue = new LinkedBlockingQueue<Message>(20000);
	
	private BrokerMessageListener brokerMessageListener;
	
	private String brokerName ;
	
	private ThreadGroup threadGroup;
	
	private GolbalThreadState threadsState;
	
	private int timer = 0;
	
	private MessageConsumer consumer = null;
	
	public ConsumerBroker(String brokerName,BrokerMessageListener brokerMessageListener,GolbalThreadState threadsState){
		this.brokerName = brokerName;
		this.brokerMessageListener = brokerMessageListener;
		this.threadsState = threadsState;
	}
	
	@Override
	public void run() {
		//启动的时候等待系统启动完成
		try {
			Thread.sleep(5*1000);
		} catch (InterruptedException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		this.initBroker();
		
		try {
			Thread.sleep(5*1000);
		} catch (InterruptedException ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		this.initThread();
		
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] threadsState.isRunning() << "+ threadsState.isRunning());
		
		while(threadsState.isRunning()){
			
			try {
				if(consumer == null) {
					MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] ConsumerBroker run error  , cause:  consumer init fail!" );
					Thread.sleep(3*1000);
					this.initBroker();
					continue;
				}
				//设定消息保护。
				if(messageQueue.size()>=20000){
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] ConsumerBroker.recevie("+this.brokerName+")  messageQueue.size()>=10000!" );
					continue;
				}
				
				Message message = consumer.receive(5*1000);
				
				if(message == null){
					timer++;
					if(timer>=30){
						timer = 0;
						MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] ConsumerBroker.recevie("+this.brokerName+") <<  no message ..." );
					}
					continue;
				}
				boolean offer = messageQueue.offer(message,1,TimeUnit.SECONDS);
				if(!offer) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] ConsumerBroker.recevie("+this.brokerName+")  messageQueue.offer() return false" );
					continue;
				}
			} catch (Exception ex) {
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
				MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
				//如果接收消息出现异常，则进行重连
				try {
					Thread.sleep(3*1000);
				} catch (InterruptedException e) {
				}
				this.closeBroker();
				this.initBroker();
			}
		}
		
		this.closeBroker();
	}
	

	public  void closeBroker(){
		
		try {
			if(this.consumer != null) {
				this.consumer.close();
			}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		
		try {
			this.broker.close();
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
		this.consumer = null;
		this.broker = null;
	}
	
	private void initBroker(){
		//如果已经退出状态，则不做任何的处理。
		if(!threadsState.isRunning()) return;
		try {
			if(broker==null){
				String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
				MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] ConsumerBroker.initBroker() -> url:"+addr+",brokerName:"+this.brokerName);
				broker =  BrokerFactory.getConsumerQueueBroker(addr,this.brokerName,"","");
			}
		    consumer = broker.getContext().getConsumer();
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] ConsumerBroker.addConsumer() error,cause:"+ex.getMessage(),ex);
			closeBroker();
		}
		
	}
	
	public boolean isClose(){
		return this.broker == null;
	}
	
	/**
	 * 启动消息处理线程
	 */
	private void initThread(){
		threadGroup = new ThreadGroup(this.brokerName);
		Thread  client = new Thread(this.threadGroup,new MessageListenerThread());
		client.start();
	}
	
	/**
	 * 消息处理线程
	 * <AUTHOR>
	 *
	 */
	private  class MessageListenerThread implements Runnable{
		@Override
		public void run() {
			while(threadsState.isRunning()){
				try {
					//从队列中获取通知的数据
					
//					Message message = messageQueue.take();//堵塞
					Message message = messageQueue.poll(1000, TimeUnit.MILLISECONDS);//线程非堵塞，1秒之后无对象返回null;
					if(message==null) {
						continue;
					}
//					MediaCenterLogger.getLogger().debug("ConsumerBroker.recevie(text) << "+ message);
					ObjectMessage objMessage = (ObjectMessage) message;
					brokerMessageListener.onMessage(objMessage);
				} catch (Exception ex) {
					MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
					//防止死循环
					try {
						Thread.sleep(3000);
					} catch (InterruptedException ex1) {
						MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex1.getMessage());
			MediaCenterLogger.getLogger().error(ex1.getMessage(),ex1);
					}
				}
			}
		}
	}
}


