package com.yunqu.yc.media.center.log;

import com.yunqu.yc.media.center.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;
import org.easitline.common.utils.calendar.EasyCalendar;

/**
 * 网关日志，需要根据具体的网关实现进行修改。
 * <AUTHOR>
 *
 */
public class MediaCenterLogger {
   public static Logger  getLogger(){
	   EasyCalendar cal  = EasyCalendar.newInstance();
	   String hours = cal.getTimeString().substring(0, 2);
	   return LogEngine.getLogger("yc-mediacenter_"+hours);
   }

  public static Logger  getLogger(String prefix){
	   EasyCalendar cal  = EasyCalendar.newInstance();
	   String hours = cal.getTimeString().substring(0, 2);
	   return LogEngine.getLogger("yc-mediacenter_"+hours+"_"+prefix);
   }
    public static Logger  getDebugLogger(){
        return LogEngine.getLogger("yc-mediacenter_debug");
    }
   public static Logger  getTimesLogger(){
	   return LogEngine.getLogger("yc-mediacenter-times");
   }

    public static Logger  getCacheDataLogger(){
        return LogEngine.getLogger(Constants.APP_NAME+"-cacheData");
    }
}
