package com.yunqu.yc.media.center.model;

import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.annotation.JSONField;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.util.CacheUtil;
import org.apache.commons.lang3.StringUtils;

public class MessageModel {
	
	/**
	 * 命令Id
	 */
	private String command;
	/**
	 * 企业Id
	 */
	private String entId;
	/**
	 * 坐席Id
	 */
	private String agentId;
	/**
	 * 坐席name
	 */
	private String agentName;
	/**
	 * 坐席nickName
	 */
	private String agentNickName;
	/**
	 * 坐席工号
	 */
	private String agentPhone;
	
	/**
	 * 用户的sessionId
	 */
	private String sessionId;
	
	/**
	 * 本次请求的JSONObject
	 */
	@JSONField(serialize = false)
	private JSONObject messageJson;
	
	private  String  channelId;
	
	private  String  channelKey;
	
	private  String  msgContent;
	
	@JSONField(serialize = false)
	private JSONObject userInfo;
	
	private String msgType;
	
	private String channelType;
	
	private String callbackService;
	
	private String serialId;
	
	private String requestTime;

	/**
	 * selectKey 选择导航语
	 * System
	 * queue
	 * close
	 * Closed 通知坐席会话关闭
	 * Removed
	 * start
	 * Connected  发送会话接入事件到坐席端
	 * closeVideo
	 * timeoutVideo
	 * agent
	 * getAgent
	 * end
	 * automatic
	 * disagreeInviteVideo  当作坐席拒绝处理
	 * inviteVideo
	 * videoCalling  视频连接中
	 * cancelInviteVideo
	 * closeVideo
	 * agreeInviteVideo  用户同意视频邀请
	 */

	private String event;
	
	private String channelName;
	
	private String brokerName;
	
	/**
	 * 时间原因， connected时间， 1 用户登录  2 转移登录
	 */
	private String eventCause;
	
	/**
	 * 结束原因
	 */
	private String clearCause;
	
	
	private String chatSessionId;
	
	/**
	 * 当前排队号
	 */
	private String queueNo;
	
	
	/**
	 * 发送者 
	 */
	private String sender = "system";
	/**
	 * 视频满意度地址
	 */
	private String videoSatisfyUrl = "";

	/**
	 * 视频唯一标识，每次创建呼叫等待页时生成唯一的
	 */
	private String videoMsgId;

	public String getVideoMsgId() {
		return videoMsgId;
	}

	public void setVideoMsgId(String videoMsgId) {
		this.videoMsgId = videoMsgId;
	}

	public String getEventCause() {
		return eventCause;
	}

	public void setEventCause(String eventCause) {
		this.eventCause = eventCause;
	}

	public String getChatSessionId() {
		return chatSessionId;
	}

	public void setChatSessionId(String chatSessionId) {
		this.chatSessionId = chatSessionId;
	}

	public String getClearCause() {
		return clearCause;
	}

	public void setClearCause(String clearCause) {
		this.clearCause = clearCause;
	}

	public String getChannelName() {
		return channelName;
	}

	public void setChannelName(String channelName) {
		this.channelName = channelName;
	}

	public String getEvent() {
		return event;
	}

	public void setEvent(String event) {
		this.event = event;
	}
	
	public String getBrokerName() {
		return brokerName;
	}

	public void setBrokerName(String brokerName) {
		this.brokerName = brokerName;
	}

	/**
	 * 用户接入时间
	 */
	private long  sessionTime = System.currentTimeMillis();
	
	public String getNikeName(){
		String nikeName = this.getUserInfo().getString("nickName");
		if(StringUtils.isBlank(nikeName)) return "访客";
		return nikeName;
	}
	
	public String getSerialId() {
		return serialId;
	}
	public void setSerialId(String serialId) {
		this.serialId = serialId;
	}
	public String getMsgContent() {
		return msgContent;
	}
	public void setMsgContent(String msgContent) {
		this.msgContent = msgContent;
	}
	public JSONObject getUserInfo() {
		if(this.userInfo == null) return new JSONObject();
		return userInfo;
	}
	public void setUserInfo(JSONObject userInfo) {
		this.userInfo = userInfo;
	}
	public String getChannelId() {
		return channelId;
	}
	public void setChannelId(String channelId) {
		this.channelId = channelId;
	}
	public String getChannelKey() {
		return channelKey;
	}
	public void setChannelKey(String channelKey) {
		this.channelKey = channelKey;
	}
	public String getCommand() {
		return command;
	}
	public void setCommand(String command) {
		this.command = command;
	}
	public String getEntId() {
		return entId;
	}
	public void setEntId(String entId) {
		this.entId = entId;
	}
	public String getAgentId() {
		return agentId;
	}
	public void setAgentId(String agentId) {
		this.agentId = agentId;
	}
	
	public String getAgentName() {
		return agentName;
	}

	public void setAgentName(String agentName) {
		this.agentName = agentName;
	}

	
	public String getAgentNickName() {
		return agentNickName;
	}

	public void setAgentNickName(String agentNickName) {
		this.agentNickName = agentNickName;
	}

	public String getSessionId() {
		return sessionId;
	}
	public void setSessionId(String sessionId) {
		this.sessionId = sessionId;
	}
	public JSONObject getMessageJson() {
		return messageJson;
	}
	public void setMessageJson(JSONObject messageJson) {
		this.messageJson = messageJson;
	}
	public String getMsgType() {
		return msgType;
	}
	public void setMsgType(String msgType) {
		this.msgType = msgType;
	}
	public String getChannelType() {
		return channelType;
	}
	public void setChannelType(String channelType) {
		this.channelType = channelType;
	}
	public String getCallbackService() {
		return callbackService;
	}
	public void setCallbackService(String callbackService) {
		this.callbackService = callbackService;
	}
	
	public long getSessionTime() {
		return sessionTime;
	}
	public void setSessionTime(long sessionTime) {
		this.sessionTime = sessionTime;
	}
	
	public int hashCode(){
		return this.sessionId.hashCode();
	}
	
	public String getRequestTime() {
		return requestTime;
	}
	public void setRequestTime(String requestTime) {
		this.requestTime = requestTime;
	}
	public String getQueueNo() {
		return queueNo;
	}

	public void setQueueNo(String queueNo) {
		this.queueNo = queueNo;
	}

	public String getSender() {
		return sender;
	}

	public void setSender(String sender) {
		this.sender = sender;
	}
	
	public String getVideoSatisfyUrl() {
		return videoSatisfyUrl;
	}

	public void setVideoSatisfyUrl(String videoSatisfyUrl) {
		this.videoSatisfyUrl = videoSatisfyUrl;
	}

	public String getAgentPhone() {
		return agentPhone;
	}

	public void setAgentPhone(String agentPhone) {
		this.agentPhone = agentPhone;
	}

	public String toString(String serialId) {
		this.setSerialId(serialId);
		return this.toString();
	}

	/**
	 * 替换msgContent中的用户会员等级名称
	 * @Description :
	 * <AUTHOR>
	 * @Datetime 2021/12/6 19:56
	 * @return: java.lang.String
	 */
	public String getFllMsgContent(){
		//替换msgContent中的用户会员等级名称，来源于onlinegw 的服务MEDIA-WELCOME-SERVICE
		String brandName = CacheUtil.get("levelNameContent_"+sessionId);
		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] getFllMsgContent()-->cachename: levelNameContent_"+sessionId+"=="+brandName);
		if(StringUtils.isNotBlank(brandName)){
			msgContent = msgContent.replaceAll("#levelName#", brandName);
			msgContent = msgContent.replaceAll("#levelName_md_2#", brandName);
		}else{
			msgContent = msgContent.replaceAll("#levelName#", "客户");
			msgContent = msgContent.replaceAll("#levelName_md_2#", "客户");
		}
		return msgContent;
	}
	
	public String toString(){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("serialId", this.getSerialId());
		jsonObject.put("channelKey", this.getChannelKey());
		jsonObject.put("entId", this.getEntId());
		jsonObject.put("agentId", this.getAgentId());
		jsonObject.put("agentName", this.getAgentName());
		jsonObject.put("timestamp", System.currentTimeMillis());
		jsonObject.put("sign", "");
		jsonObject.put("callbackUrl", this.getCallbackService());
		JSONObject data = new JSONObject();
		data.put("sessionId", this.getSessionId());
		data.put("chatSessionId", this.getChatSessionId());
		data.put("msgType", this.getMsgType());
		data.put("msgContent", this.getFllMsgContent());
		data.put("userInfo", this.getUserInfo());
		data.put("mediaEvent", this.getEvent());
		data.put("clearCause", this.getClearCause());
		data.put("channelId", this.getChannelId());
		data.put("channelKey", this.getChannelKey());
		data.put("channelName", this.getChannelName());
		data.put("channelType", this.getChannelType());
		data.put("queueNo", this.getQueueNo());
		data.put("sender", this.getSender());
		data.put("videoSatisfyUrl", this.getVideoSatisfyUrl());
		data.put("agentPhone", this.getAgentPhone());
		data.put("agentId", this.getAgentId());
		data.put("agentName", this.getAgentName());
		data.put("agentNickName", this.getAgentNickName());
		
		jsonObject.put("data", data);
		return jsonObject.toJSONString();
	}
	
	public String toAgentString(String serialId) {
		this.setSerialId(serialId);
		return this.toAgentString();
	}
	
	public String toAgentString(){
		JSONObject jsonObject = new JSONObject();
		jsonObject.put("serialId", this.getSerialId());
		jsonObject.put("channelKey", this.getChannelKey());
		jsonObject.put("entId", this.getEntId());
		jsonObject.put("agentId", this.getAgentId());
		jsonObject.put("timestamp", System.currentTimeMillis());
		jsonObject.put("sign", "");
		jsonObject.put("callbackUrl", this.getCallbackService());
		JSONObject data = new JSONObject();
		data.put("sessionId", this.getSessionId());
		data.put("chatSessionId", this.getChatSessionId());
		data.put("sender", this.getSender());
		data.put("msgType", this.getMsgType());
		data.put("msgContent", this.getFllMsgContent());
		data.put("userInfo", this.getUserInfo());
		data.put("event", this.getEvent());
		data.put("eventCause", this.getEventCause());
		data.put("clearCause", this.getClearCause());
		data.put("channelId", this.getChannelId());
		data.put("channelKey", this.getChannelKey());
		data.put("channelName", this.getChannelName());
		data.put("channelType", this.getChannelType());
		
		jsonObject.put("data", data);
		return jsonObject.toJSONString();
	}
	
	/**
	 * 生成新的对象
	 * @return
	 */
	public MessageModel newModel() {
		String jsonString = JSONObject.toJSONString(this);
		MessageModel messageModel = JSONObject.toJavaObject(JSONObject.parseObject(jsonString), MessageModel.class);
		messageModel.setUserInfo(this.getUserInfo());
		messageModel.setMessageJson(this.getMessageJson());
		return messageModel;
	}
	
	
}
