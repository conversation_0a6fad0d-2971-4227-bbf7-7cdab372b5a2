package com.yunqu.yc.center.util;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.QueryFactory;
import com.yunqu.yc.center.log.YcCenterLogger;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyDate;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;
import java.util.*;

/**
 * 会员身份策略配置工具类
 * <AUTHOR>
 */
public class MemberConfigUtil {

    /**
     * 默认会员身份字典
     */
    private static Map<String, String> defMemberDictMap = new LinkedHashMap();

    static {
        defMemberDictMap.put("levelCode_1", "vip1");
        defMemberDictMap.put("levelCode_2", "colmo黑金");
        defMemberDictMap.put("levelCode_3", "colmo黄金");
        defMemberDictMap.put("levelCode_4", "colmo钻石");
        defMemberDictMap.put("levelCode_5", "colmo铂金");
        defMemberDictMap.put("levelCode_6", "colmo白银");
        defMemberDictMap.put("levelCode_7", "东芝钻石");
        defMemberDictMap.put("levelCode_8", "东芝铂金");
        defMemberDictMap.put("levelCode_9", "东芝黄金");
        defMemberDictMap.put("levelCode_10", "东芝白银");
        defMemberDictMap.put("levelCode_11", "东芝普通会员");
        defMemberDictMap.put("levelCode_12", "黑钻卡美粉");
        defMemberDictMap.put("levelCode_13", "白金卡美粉");
        defMemberDictMap.put("levelCode_14", "金卡美粉");
        defMemberDictMap.put("levelCode_15", "银卡美粉");
        defMemberDictMap.put("levelCode_16", "蓝卡美粉");
        defMemberDictMap.put("levelCode_17", "vip2");
    }
    /**
     * 校验结果类
     */
    public static class ValidationResult {
        private boolean valid = true;
        private List<String> errors = new ArrayList<>();

        public void addError(String error) {
            this.valid = false;
            this.errors.add(error);
        }

        public boolean isValid() {
            return valid;
        }

        public List<String> getErrors() {
            return errors;
        }

        public String getErrorMessage() {
            return String.join("；", errors);
        }
    }

    /**
     * 校验会员身份策略配置
     * @param memberStrategy 会员身份策略数据
     * @param isUpdate 是否为更新操作
     * @return 校验结果
     */
    public static ValidationResult validateMemberStrategy(JSONObject memberStrategy, boolean isUpdate) {
        ValidationResult result = new ValidationResult();

        try {
            String memberId = memberStrategy.getString("MEMBER_ID");
            String memberName = memberStrategy.getString("MEMBER_NAME");
            Integer rankIndex = memberStrategy.getInteger("RANK_INDEX");
            String configId = memberStrategy.getString("CONFIG_ID");

            // 基本字段校验
            if(StringUtils.isBlank(memberId)) {
                result.addError("会员身份不能为空");
            }

            if(StringUtils.isBlank(memberName)) {
                result.addError("会员名称不能为空");
            }

            if(rankIndex == null || rankIndex < 1 || rankIndex > 20) {
                result.addError("身份等级必须在1-20之间");
            }

            // 会员身份唯一性校验
            if(StringUtils.isNotBlank(memberId)) {
                if(checkMemberIdExists(memberId, isUpdate ? configId : null)) {
                    result.addError("该会员身份已存在");
                }
            }

            // 身份等级唯一性校验
            if(rankIndex != null) {
                if(checkRankIndexExists(rankIndex, isUpdate ? configId : null)) {
                    result.addError("该身份等级已存在");
                }
            }

            // 渠道配置校验
            String channelKeys = memberStrategy.getString("CHANNEL_KEYS");
            if(StringUtils.isNotBlank(channelKeys)) {
                ValidationResult channelResult = validateChannelStrategies(channelKeys, rankIndex, isUpdate ? configId : null);
                if(!channelResult.isValid()) {
                    result.errors.addAll(channelResult.getErrors());
                    result.valid = false;
                }
            }

        } catch (SQLException e) {
            YcCenterLogger.getLogger().error("校验会员身份策略失败", e);
            result.addError("校验失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 校验渠道策略配置
     * @param channelStrategy 渠道策略数据
     * @param isUpdate 是否为更新操作
     * @return 校验结果
     */
    public static ValidationResult validateChannelStrategy(JSONObject channelStrategy, boolean isUpdate) {
        ValidationResult result = new ValidationResult();

        try {
            String channelKey = channelStrategy.getString("CHANNEL_KEY");
            String configId = channelStrategy.getString("CONFIG_ID");
            Integer rankIndex = channelStrategy.getInteger("RANK_INDEX");

            // 基本字段校验
            if(StringUtils.isBlank(channelKey)) {
                result.addError("渠道不能为空");
            }

            if(StringUtils.isBlank(configId)) {
                result.addError("配置ID不能为空");
            }

            if(rankIndex == null || rankIndex < 1 || rankIndex > 20) {
                result.addError("身份等级必须在1-20之间");
            }

            // 渠道身份等级冲突校验
            if(StringUtils.isNotBlank(channelKey) && rankIndex != null) {
                if(checkChannelRankIndexConflict(channelKey, rankIndex, isUpdate ? configId : null)) {
                    result.addError("该渠道的身份等级冲突");
                }
            }

        } catch (SQLException e) {
            YcCenterLogger.getLogger().error("校验渠道策略失败", e);
            result.addError("校验失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 批量校验渠道策略配置
     * @param channelKeys 渠道KEY列表（逗号分隔）
     * @param rankIndex 身份等级
     * @param ignoreConfigId 排除的配置ID
     * @return 校验结果
     */
    public static ValidationResult validateChannelStrategies(String channelKeys, Integer rankIndex, String ignoreConfigId) {
        ValidationResult result = new ValidationResult();

        if(StringUtils.isBlank(channelKeys) || rankIndex == null) {
            return result;
        }

        try {
            String[] channels = channelKeys.split(",");
            Map<String, String> channelNameMap = getChannelNameMap();

            for(String channelKey : channels) {
                if(StringUtils.isNotBlank(channelKey)) {
                    if(checkChannelRankIndexConflict(channelKey, rankIndex, ignoreConfigId)) {
                        String channelName = channelNameMap.get(channelKey);
                        result.addError("渠道【" + (channelName != null ? channelName : channelKey) + "】的身份等级冲突");
                    }
                }
            }

        } catch (SQLException e) {
            YcCenterLogger.getLogger().error("批量校验渠道策略失败", e);
            result.addError("校验失败：" + e.getMessage());
        }

        return result;
    }

    /**
     * 检查会员身份是否已存在
     */
    public static boolean checkMemberIdExists(String memberId, String ignoreConfigId) throws SQLException {
        EasyQuery query = QueryFactory.getQuery();
        String sql = "select count(1) from YCUSER.CC_MEDIA_MEMBER_CONFIG where MEMBER_ID = ?";
        Object[] params;

        if(StringUtils.isNotBlank(ignoreConfigId)) {
            sql += " and CONFIG_ID <> ?";
            params = new Object[]{memberId, ignoreConfigId};
        } else {
            params = new Object[]{memberId};
        }

        return query.queryForInt(sql, params) > 0;
    }

    /**
     * 检查身份等级是否已存在
     * @param rankIndex 身份等级
     * @param ignoreConfigId  忽略的策略id，一般为当前策略修改时传递
     * @return
     * @throws SQLException
     */
    public static boolean checkRankIndexExists(Integer rankIndex, String ignoreConfigId) throws SQLException {
        if(rankIndex == null) {
            return false;
        }

        String sql = "select count(1) from YCUSER.CC_MEDIA_MEMBER_CONFIG where RANK_INDEX = ?";
        Object[] params;

        if(StringUtils.isNotBlank(ignoreConfigId)) {
            sql += " and CONFIG_ID <> ?";
            params = new Object[]{rankIndex, ignoreConfigId};
        } else {
            params = new Object[]{rankIndex};
        }

        return QueryFactory.getQuery().queryForExist(sql, params);
    }

    /**
     * 检查渠道身份等级冲突（检查渠道关联该身份策略的“身份等级”是否在其他身份策略上已存在）
     * @param channelKey 渠道key
     * @param rankIndex 会员身份策略的身份等级
     * @param ignoreConfigId 忽略的策略id
     * @return
     * @throws SQLException
     */
    public static boolean checkChannelRankIndexConflict(String channelKey, Integer rankIndex, String ignoreConfigId) throws SQLException {
        String sql = "select count(1) from YCUSER.CC_MEDIA_MEMBER_CHANNEL where CHANNEL_KEY = ? and RANK_INDEX = ?";
        Object[] params;

        if(StringUtils.isNotBlank(ignoreConfigId)) {
            sql += " and CONFIG_ID <> ?";
            params = new Object[]{channelKey, rankIndex, ignoreConfigId};
        } else {
            params = new Object[]{channelKey, rankIndex};
        }

        return QueryFactory.getQuery().queryForExist(sql, params);
    }

    /**
     * 获取渠道名称映射
     */
    public static Map<String, String> getChannelNameMap() throws SQLException {
        Map<String, String> channelNameMap = new HashMap<>();
        EasyQuery query = QueryFactory.getQuery();

        List<JSONObject> channels = query.queryForList(
            "select CHANNEL_KEY, CHANNEL_NAME from YCUSER.CC_CHANNEL where CHANNEL_STATE='Y'",
            new Object[]{}, new JSONMapperImpl()
        );

        for(JSONObject channel : channels) {
            channelNameMap.put(channel.getString("CHANNEL_KEY"), channel.getString("CHANNEL_NAME"));
        }

        return channelNameMap;
    }

    /**
     * 获取预设会员身份字典
     * @return
     */
    public static Map<String, String> getMemberDictMap(){
        //TODO 查询数据获取
        return defMemberDictMap;
    }

    /**
     * 创建渠道策略配置
     * @param configId 身份策略id
     * @param channelKey 渠道key
     * @param channelName 渠道name
     * @param channelStrategy 身份策略对象
     * @throws SQLException
     */
    public static void createChannelStrategy(String configId, String channelKey,String channelName, JSONObject channelStrategy) throws SQLException {
        EasyRecord channelRecord = new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CHANNEL", "CHANNEL_KEY", "CONFIG_ID");
        channelRecord.setPrimaryValues(channelKey, configId);
        channelRecord.set("RANK_INDEX", channelStrategy.getInteger("RANK_INDEX"));
        channelRecord.set("IS_TO_AGENT", channelStrategy.getString("IS_TO_AGENT"));
        channelRecord.set("TO_AGENT_GROUP_ID", channelStrategy.getString("TO_AGENT_GROUP_ID"));
        channelRecord.set("CHANNEL_NAME", channelName);
        channelRecord.set("CREATE_TIME", EasyDate.getCurrentDateString());
        channelRecord.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        QueryFactory.getQuery().save(channelRecord);
    }

    /**
     * 更新渠道策略配置
     * @param configId 身份策略id
     * @param channelKey 渠道key
     * @param channelStrategy 身份策略对象
     */
    public static void updateChannelStrategy(String configId, String channelKey, JSONObject channelStrategy) throws SQLException {
        EasyRecord channelRecord = new EasyRecord("YCUSER.CC_MEDIA_MEMBER_CHANNEL", "CHANNEL_KEY", "CONFIG_ID");
        channelRecord.setPrimaryValues(channelKey, configId);
        channelRecord.set("RANK_INDEX", channelStrategy.getInteger("RANK_INDEX"));
        channelRecord.set("IS_TO_AGENT", channelStrategy.getString("IS_TO_AGENT"));
        channelRecord.set("TO_AGENT_GROUP_ID", channelStrategy.getString("TO_AGENT_GROUP_ID"));
        channelRecord.set("UPDATE_TIME", EasyDate.getCurrentDateString());
        QueryFactory.getQuery().update(channelRecord);
    }

    /**
     * 重新更新身份策略中的关联渠道
     * @param configId 身份策略id
     * @throws SQLException
     */
    public static void reloadMemberChannelList(String configId) throws SQLException {
        EasyQuery query = QueryFactory.getQuery();
        // 新增/更新完成后，回写上级配置中的 CHANNEL_KEYS、CHANNEL_NAMES
        List<JSONObject> remainChannels = query.queryForList(
                "select mc.CHANNEL_KEY, ch.CHANNEL_NAME " +
                        "from YCUSER.CC_MEDIA_MEMBER_CHANNEL mc " +
                        "left join YCUSER.CC_CHANNEL ch on mc.CHANNEL_KEY = ch.CHANNEL_KEY " +
                        "where mc.CONFIG_ID = ? order by ch.CHANNEL_NAME",
                new Object[]{configId}, new JSONMapperImpl()
        );
        StringBuilder keysBuf = new StringBuilder();
        StringBuilder namesBuf = new StringBuilder();
        if(remainChannels != null){
            for(JSONObject rc : remainChannels){
                String k = rc.getString("CHANNEL_KEY");
                String n = rc.getString("CHANNEL_NAME");
                if(StringUtils.isNotBlank(k)){
                    if(keysBuf.length() > 0){ keysBuf.append(","); }
                    keysBuf.append(k);
                    if(namesBuf.length() > 0){ namesBuf.append(","); }
                    namesBuf.append(StringUtils.isNotBlank(n) ? n : k);
                }
            }
        }
        query.executeUpdate(
                "update YCUSER.CC_MEDIA_MEMBER_CONFIG set CHANNEL_KEYS = ?, CHANNEL_NAMES = ?, UPDATE_TIME = ? where CONFIG_ID = ?",
                keysBuf.toString(), namesBuf.toString(), EasyDate.getCurrentDateString(), configId
        );

    }
}
