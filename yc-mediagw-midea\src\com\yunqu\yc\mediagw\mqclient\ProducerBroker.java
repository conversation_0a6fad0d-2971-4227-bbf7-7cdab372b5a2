package com.yunqu.yc.mediagw.mqclient;

import java.util.HashMap;
import java.util.Map;

import javax.jms.JMSException;

import com.yunqu.yc.mediagw.log.EventLogger;
import com.yunqu.yc.mediagw.util.MediaCacheUtil;
import com.yunqu.yc.mediagw.util.CacheUtil;
import org.easitline.common.core.activemq.Broker;
import org.easitline.common.core.activemq.BrokerFactory;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.utils.string.StringUtils;

import com.yunqu.yc.mediagw.base.MediaConstants;
import com.yunqu.yc.mediagw.log.MediagwLogger;


/**
 * 
 * <AUTHOR>
 *
 */
public class ProducerBroker {
	
	private static  Broker  mediaCenterBroker;
	private static  Broker  userBroker;
	private static  Map<String,Broker> ccbarBrokers = new HashMap<String,Broker>();
	
	
	
	//media-broker-marsNodeId
	
	/**
	 * 发送消息到yc-mediacenter
	 * {
		    "agentId":"3003@1000",
		    "data":{
		        "userInfo":Object{...},
		        "msgType":"text",
		        "bizType":"",
		        "msgContent":"88",
		        "channelKey":"gh_58f8a50fddbb",
		        "msgTime":"2021-09-01 15:18:07",
		        "channelName":"美美测试",
		        "channelType":"2",
		        "sessionId":"oFtQywF4mbSblrbfW6BcDupa5vTQ",
		        "chatSessionId":"83695196267926721555877",
		        "channelId":"84664589459219995533711"
		    },
		    "channelKey":"gh_58f8a50fddbb",
		    "entId":"1000",
		    "serialId":"83695193120326716288110",
		    "sign":"29FD49D1569A140CCE4E054367E662B9",
		    "callbackUrl":"WEBCHAT-CALLBACK-SERVICE",
		    "event":"end",
		    "key":"123456",
		    "command":"bye",
		    "timestamp":"1630480687967"
		}
	 * @param msg
	 */
	public static void sendMediaCenterMessage(String msg) {
		long thid = Thread.currentThread().getId();
		String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
		
		if (mediaCenterBroker == null) {
			mediaCenterBroker = BrokerFactory.getProducerQueueBroker(addr, MediaConstants.MEDIACENTER_BROKER, "", "");
			CacheUtil.put("ActiveMQ_ADDR",addr,24*3600);
		}

		EventLogger.getLogger().info(" >> sendMediaCenterMessage("+MediaConstants.MEDIACENTER_BROKER+")-> " + msg);
		try {
			mediaCenterBroker.sendMessage(msg);
		} catch(JMSException e) {
			mediaCenterBroker.close();
			MediagwLogger.getLogger().error("Thread["+thid+"]>> sendMediaCenterMessage("+MediaConstants.MEDIACENTER_BROKER+") error,need again new broker,cause:"+e.getMessage());
			mediaCenterBroker = BrokerFactory.getProducerQueueBroker(addr, MediaConstants.MEDIACENTER_BROKER, "", "");
			try {
				mediaCenterBroker.sendMessage(msg);
			} catch (JMSException e1) {
				MediagwLogger.getLogger().error(e1.getMessage(),e1);
				mediaCenterBroker.close();
			}
		} catch (Exception ex) {
			MediagwLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
	/**
	 * 获得用户所对应的MARS。
	 * @param msg
	 * @return
	 */
	public static void sendUserMessage(String msg){
		long thid = Thread.currentThread().getId();

		String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
		if (userBroker == null) {
			userBroker = BrokerFactory.getProducerQueueBroker(addr, MediaConstants.getUserBrokerName(), "", "");
		}
		
		MediagwLogger.getLogger().info("Thread["+thid+"]>> sendUserMessage("+MediaConstants.getUserBrokerName()+")-> " + msg);
		
		try {
			userBroker.sendMessage(msg);
		} catch(JMSException e) {
			userBroker.close();
			MediagwLogger.getLogger().error("Thread["+thid+"]>> sendMediagwMessage("+MediaConstants.getUserBrokerName()+") error,the broker is close,need again new broker,cause:"+e.getMessage());
			userBroker = BrokerFactory.getProducerQueueBroker(addr, MediaConstants.MEDIACENTER_BROKER, "", "");
			try {
				userBroker.sendMessage(msg);
			} catch (JMSException e1) {
				userBroker.close();
				MediagwLogger.getLogger().error(e1.getMessage(),e1);
			}
		} catch (Exception ex) {
			MediagwLogger.getLogger().error(ex.getMessage(),ex);
		}
		
	}
	
	/**
	 * 获得agent队列
	 * @param agentId
	 * @param msg
	 * @return
	 */
	public static void sendAgentMessage(String agentId,String msg){
		long thid = Thread.currentThread().getId();

		if(StringUtils.isBlank(agentId)){
			MediagwLogger.getLogger().error("Thread["+thid+"]>> sendAgentMessage error,cause: agentId is null,msg :"+msg,null);
			return ;
		} 
			//获得当前坐席所在的mars上的MQ
		String brokerName = MediaCacheUtil.get(MediaConstants.BROKER_AGENT_NAME+agentId);
//		MediagwLogger.getLogger().info(MediaConstants.BROKER_AGENT_NAME+agentId +"="+brokerName);

		if(StringUtils.isBlank(brokerName)){
			MediagwLogger.getLogger().warn("Thread["+thid+"]>> sendAgentMessage(agentId:"+agentId+",msg:"+msg+") error ,cause:"+MediaConstants.BROKER_AGENT_NAME+agentId+" brokerName is null!");
			return ;
		}
		
//		MediagwLogger.getLogger().info("Thread["+thid+"]>> sendAgentMessage("+brokerName+")->"+msg);
		Broker broker = ccbarBrokers.get(brokerName);
		
		String addr = AppContext.getContext("yc-api").getProperty("ActiveMQ_ADDR", "tcp://127.0.0.1:61616");
		if(broker==null){
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
		}
		
		try {
			broker.sendMessage(msg);
			ccbarBrokers.put(brokerName, broker);
		} catch(JMSException e) {
			broker.close();
			MediagwLogger.getLogger().error(" >> sendAgentMessage("+agentId+","+brokerName+") error,the broker is close,need again new broker,cause:"+e.getMessage());
			broker =  BrokerFactory.getProducerQueueBroker(addr,brokerName,"","");
			
			try {
				broker.sendMessage(msg);
				ccbarBrokers.put(brokerName, broker);
			} catch (JMSException e1) {
				broker.close();
				MediagwLogger.getLogger().error(e1.getMessage(),e1);
			}
		} catch (Exception ex) {
			MediagwLogger.getLogger().error(ex.getMessage(),ex);
		}
	}
	
}
