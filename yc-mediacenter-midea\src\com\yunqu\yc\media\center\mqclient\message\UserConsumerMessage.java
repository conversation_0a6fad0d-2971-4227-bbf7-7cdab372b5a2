package com.yunqu.yc.media.center.mqclient.message;


import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.model.MessageModel;
import com.yunqu.yc.media.center.msg.Message;
import com.yunqu.yc.media.center.msg.impl.*;
import org.easitline.common.core.activemq.BrokerMessageListener;
import org.easitline.common.core.activemq.MessageException;
import org.easitline.common.utils.string.StringUtils;

import javax.jms.ObjectMessage;

/**
 * 处理全媒体网关消息
 * 
 * {
    "agentId":"3003@1000",
    "data":{
        "userInfo":Object{...},
        "msgType":"text",
        "bizType":"",
        "msgContent":"88",
        "channelKey":"gh_58f8a50fddbb",
        "msgTime":"2021-09-01 15:18:07",
        "channelName":"美美测试",
        "channelType":"2",
        "sessionId":"oFtQywF4mbSblrbfW6BcDupa5vTQ",
        "chatSessionId":"83695196267926721555877",
        "channelId":"84664589459219995533711"
    },
    "channelKey":"gh_58f8a50fddbb",
    "entId":"1000",
    "serialId":"83695193120326716288110",
    "sign":"29FD49D1569A140CCE4E054367E662B9",
    "callbackUrl":"WEBCHAT-CALLBACK-SERVICE",
    "event":"end",
    "key":"123456",
    "command":"bye",
    "timestamp":"1630480687967"
}
 *
 */
public class UserConsumerMessage implements BrokerMessageListener {
	

	@Override
	public void onMessage(Object messageObj) throws MessageException {
		
		//获得消息内容
		JSONObject jsonObject = this.getJson(messageObj);
		if(jsonObject == null){
			MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage.onMessage() error , messageObj is null ->"+messageObj);
			return ;
		}
		
		JSONObject dataObject = jsonObject.getJSONObject("data");
		
		try {
			String command = jsonObject.getString("command");
			String entId = jsonObject.getString("entId");
			String channelKey = jsonObject.getString("channelKey");
			String callbackUrl = jsonObject.getString("callbackUrl");
			String serialId = jsonObject.getString("serialId");
			String agentId = jsonObject.getString("agentId");
			
			String sessionId = dataObject.getString("sessionId");
			String channelId = dataObject.getString("channelId");
			String channelType = dataObject.getString("channelType");
			String chatSessionId = dataObject.getString("chatSessionId");
			String msgType = dataObject.getString("msgType");
			String msgContent = dataObject.getString("msgContent");
			JSONObject userInfo = dataObject.getJSONObject("userInfo");
			//视频唯一标识，每次创建呼叫等待页时生成唯一的
			String videoMsgId = dataObject.getString("videoMsgId");

			if(StringUtils.isBlank(command)){
				MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage.onMessage() error , command is null ->"+jsonObject.toJSONString());
				return ;
			}
			Message  message = getMessage(command);
			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage<"+command+","+sessionId+"> << "+jsonObject);
			
			if(StringUtils.isBlank(channelId)) {
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage.onMessage() error ,cause:channelId is null ->"+jsonObject.toJSONString());
				return ;
			}
			
			if(StringUtils.isBlank(entId)) {
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage.onMessage() error ,cause:entId is null ->"+jsonObject.toJSONString());
				return ;
			}
			
			if(StringUtils.isBlank(chatSessionId)) {
				MediaCenterLogger.getLogger().warn("Thread["+Thread.currentThread().getId()+"] UserConsumerMessage.onMessage() error ,cause:chatSessionId is null ->"+jsonObject.toJSONString());
				return ;
			}
			
			//全媒体网关消息结构
			MessageModel messageModel = new MessageModel();
			messageModel.setSerialId(serialId);
			if(StringUtils.isNotBlank(agentId)) messageModel.setAgentId(agentId);
			messageModel.setCommand(command);
			messageModel.setEntId(entId);
			messageModel.setSessionId(sessionId);
			messageModel.setChannelId(channelId);
			messageModel.setChannelKey(channelKey);
			messageModel.setMsgType(msgType);
			messageModel.setCallbackService(callbackUrl);
			messageModel.setChannelType(channelType);
			messageModel.setMsgContent(msgContent);
			messageModel.setUserInfo(userInfo);
			messageModel.setChatSessionId(chatSessionId);
			messageModel.setVideoMsgId(videoMsgId);
			message.doMessage(messageModel);
			
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] ConsumerMessage.onMessage() error,cause:"+ex.getMessage(),ex);
		}
		
	}
	
	private JSONObject getJson(Object messageObj){
		ObjectMessage msg = null;
		if(messageObj instanceof ObjectMessage){
			msg = (ObjectMessage)messageObj;
		}
		if(msg == null) return null;
		
		String jsonString =  null;
		try {
			jsonString = (String)msg.getObject();
		} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}
		if(StringUtils.isBlank(jsonString)){
			return null;
		}
		return JSONObject.parseObject(jsonString);
		
	}
	
	private Message getMessage(String command){
		if("welcome".equals(command)) return new WelcomeMessage();
		if("bye".equals(command)) return new ByeMessage();
		if("selectKey".equals(command)) return new SelectKeyMessage();
		if(command.toLowerCase().indexOf("video")>-1) {
			return new UserVideoMessage();
		}
		
		return new GetAgentMessage();
	}
	
}
