package com.yunqu.yc.agent.servlet;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.agent.base.AgentInfos;
import com.yunqu.yc.agent.base.Constants;
import com.yunqu.yc.agent.base.QueryFactory;
import com.yunqu.yc.agent.chat.ChatContext;
import com.yunqu.yc.agent.chat.model.MessageModel;
import com.yunqu.yc.agent.chat.model.SessionModel;
import com.yunqu.yc.agent.chat.vo.SessionRowMapper;
import com.yunqu.yc.agent.enums.MsgSenderEnum;
import com.yunqu.yc.agent.genesys.util.RespResultEnum;
import com.yunqu.yc.agent.log.CcbarLogger;
import com.yunqu.yc.agent.log.GenesysLogger;
import com.yunqu.yc.agent.log.MediaLogger;
import com.yunqu.yc.agent.model.AgentModel;
import com.yunqu.yc.agent.mqclient.MediaProducerBroker;
import com.yunqu.yc.agent.msg.EventFactory;
import com.yunqu.yc.agent.msg.MediaAgentStateEnum;
import com.yunqu.yc.agent.msg.MediaRespCmdEnum;
import com.yunqu.yc.agent.msg.impl.v1.EventHandlerV1;
import com.yunqu.yc.agent.server.WebSocket;
import com.yunqu.yc.agent.util.MediaCacheUtil;
import com.yunqu.yc.sso.impl.YCUserPrincipal;
import org.apache.commons.lang3.StringUtils;
import org.easitline.common.core.cache.CacheManager;
import org.easitline.common.core.web.EasyResult;
import org.easitline.common.db.EasyQuery;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasyRow;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.calendar.EasyCalendar;
import org.easitline.common.utils.kit.RandomKit;

import javax.servlet.ServletException;
import javax.servlet.annotation.WebServlet;
import java.io.IOException;
import java.sql.SQLException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

@WebServlet({"/servlet/mediaEvent"})
public class MediaEventServlet
  extends EventBaseServlet
{
  private static final long serialVersionUID = 2151657556079304871L;
  private static final String RESULT_SUCC = "succ";
  private static final String RESULT_FAIL = "fail";
  EventHandlerV1 eventMessage = (EventHandlerV1)EventFactory.getEventMessage("v1");

  @Deprecated
  public EasyResult actionForSendMessage()
    throws ServletException, IOException
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    CcbarLogger.getLogger()
      .info("MediaEventServlet.actionForMessage(" + getYCUserPrincipal().getLoginAcct() + ")->:" + cmdJsonString);
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String sessionId = cmdJson.getString("sessionId");
    String msgContent = cmdJson.getString("msgContent");
    CcbarLogger.getLogger().info("actionForMessage.cmdJson->" + cmdJson);
    CcbarLogger.getLogger().info("actionForMessage.msgContent->" + msgContent);
    String chatSessionId = (String)CacheManager.getMemcache().get("MEDIA_USER_SERIAL_" + sessionId);
    chatSessionId = StringUtils.stripToEmpty(chatSessionId);
    MsgSenderEnum sendEnum = MsgSenderEnum.getEnumByKey(cmdJson.getString("sender"),MsgSenderEnum.坐席);

    String agentId = getYCUserPrincipal().getLoginAcct();
    MessageModel messageModel = new MessageModel();
    messageModel.setSerialId(RandomKit.randomStr());
    messageModel.setAgentId(agentId);
    messageModel.setEntId(getEntId());
    messageModel.setSessionId(sessionId);
    messageModel.setChatSessionId(chatSessionId);
    messageModel.setMsgType("text");
    messageModel.setSender(sendEnum.getKey());
    messageModel.setSenderCode(sendEnum.getCode());
    messageModel.setMsgContent(msgContent);
    try
    {
      MediaProducerBroker.sendUserMessage(sessionId, messageModel.toString());
      
      JSONObject notifyJson = new JSONObject();
      notifyJson.put("entId", getEntId());
      notifyJson.put("agentId", agentId);
      notifyJson.put("sessionId", sessionId);
      notifyJson.put("command", "cmdMsgNotify");
      notifyJson.put("sender", sendEnum.getKey());
      MediaProducerBroker.sendMediaCenterMessage(notifyJson.toJSONString());
      ChatContext.getMessage(getEntId()).save(messageModel);
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error("mediaEvent->Send msgcontent error,cause:" + ex.getMessage(), ex);
    }
    return EasyResult.ok();
  }
  
  public EasyResult actionForLimit()
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String count = cmdJson.getString("count");
    String entId = cmdJson.getString("entId");
    if (StringUtils.isBlank(entId)) {
      entId = getEntId();
    }
    String agentId = cmdJson.getString("agentId");
    if (StringUtils.isBlank(agentId)) {
      agentId = getAgentId();
    }
    String updateAgentAcc =cmdJson.get("updateAgentAcc")==null?agentId:String.valueOf(cmdJson.get("updateAgentAcc")) ;
    updateCount(count, entId, agentId,updateAgentAcc);
    return EasyResult.ok();
  }
  
  public void updateCount(String count, String entId, String agentId,String updateAgentAcc)
  {
    JSONObject notifyJson = new JSONObject();
    notifyJson.put("entId", entId);
    notifyJson.put("agentId", agentId);
    notifyJson.put("count", count);
    notifyJson.put("command", "cmdLimit");
    MediaProducerBroker.sendMediaCenterMessage(notifyJson.toJSONString());
    
    try
    {
      SimpleDateFormat sdf = new SimpleDateFormat();
      sdf.applyPattern("yyyy-MM-dd HH:mm:ss");
      Date date = new Date();
      EasyRecord record = new EasyRecord(getTableName("CC_MEDIA_AGENT_STATE_RECORD"), new String[] { "RECORD_ID" });
      record.setPrimaryValues(new Object[] { RandomKit.randomStr() });
      record.set("AGENT_ACCT", agentId);
      record.set("AGENT_PHONE", agentId);
      record.set("STATE", count);
      record.set("RECORD_TIME", sdf.format(date));
      record.set("UPDATE_AGENT_ACC", updateAgentAcc);
      record.set("ENT_ID", entId);
      getQuery().save(record);
    }
    catch (SQLException e)
    {
      e.printStackTrace();
      MediaLogger.getLogger().error("服务上线日志入库失败->actionForLimit:" + e.getMessage());
    }
  }
  @Deprecated
  public EasyResult actionForEvent()
    throws Exception
  {
	  setResHeader();
    if (getYCUserPrincipal() == null) {
      return getSessionTimeoutResult();
    }
    EasyResult result = EasyResult.ok();
    
    String cmdJson = getRequest().getParameter("cmdJson");
    MediaLogger.getLogger().info("<MediaEvent>[" + getAgentId() + "][" + getRequest().getRemoteAddr() + "] << " + cmdJson);
    
    JSONObject cmdObject = JSONObject.parseObject(cmdJson);
    if (cmdObject == null)
    {
      result.setSuccess("fail", "处理失败，原因：cmdJson数据请求格式错误！");
      return result;
    }
    String command = cmdObject.getString("messageId");
    String resp = "";
    try
    {
      if ("cmdNotReady".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdNotReady"));
        resp = getCommandResult(getAgentId(), "cmdNotReady");
        if ("000".equals(resp))
        {
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaAgentStateEnum.BUSY.getReq(getEntId(), getAgentId()).toJSONString());
        	WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.BUSY.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          if (StringUtils.isBlank(resp)) {
            return EasyResult.error(500, "置忙失败,消息响应超时");
          }
          return EasyResult.error(500, "置忙失败[" + result + "]");
        }
      }
      if ("cmdReady".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdReady"));
        resp = getCommandResult(getAgentId(), "cmdReady");
        if ("000".equals(resp))
        {
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaAgentStateEnum.IDLE.getReq(getEntId(), getAgentId()).toJSONString());
        	WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.IDLE.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          if (StringUtils.isBlank(resp)) {
            return EasyResult.error(500, "置闲失败,消息响应超时");
          }
          return EasyResult.error(500, "置闲失败[" + result + "]");
        }
        return result;
      }
      if ("cmdLogout".equalsIgnoreCase(command))
      {
        MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), "cmdLogout"));
        resp = getCommandResult(getAgentId(), "cmdLogout");
        if ("001".equals(resp)) {
          return EasyResult.error(500, "当前还有待处理用户，请处理完成再签出！");
        }
        if ("000".equals(resp))
        {
          MediaCacheUtil.delete(Constants.BROKER_AGENT_NAME + getAgentId());
          
//          CcbarSocket.sendMessageToAgent("Event", getAgentId(), MediaRespCmdEnum.Logout.getReq(getEntId(), getAgentId(), RespResultEnum.SUCCESS).toJSONString());
//          CcbarSocket.sendMessageToAgent("State", getAgentId(), MediaAgentStateEnum.LOGOFF.getReq(getEntId(), getAgentId()).toJSONString());
          WebSocket.sendMessageToAgent(getAgentId(), MediaRespCmdEnum.Logout.getReq(getEntId(), getAgentId(), RespResultEnum.SUCCESS).toJSONString());
          WebSocket.sendMessageToAgent(getAgentId(), MediaAgentStateEnum.LOGOFF.getReq(getEntId(), getAgentId()).toJSONString());
        }
        else
        {
          return EasyResult.error(500, "签出失败！");
        }
        return result;
      }
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error("run actionForEvent() error,cause:" + ex.getMessage(), ex);
      

      MediaLogger.getLogger().info("<Resp>[" + getAgentId() + "][" + command + ":" + resp + "] >>" + result);
    }
    return result;
  }
  
  public EasyResult actionForAgentList()
  {
	  setResHeader();
    if (getYCUserPrincipal() == null) {
      return getSessionTimeoutResult();
    }
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    
    String sessionId = cmdJson.getString("sessionId");

    JSONObject data = new JSONObject();
    EasyQuery query = getQuery();
    try{
      //查询当前会话信息
      String sql = "select CHANNEL_ID from " + getTableName("CC_MEDIA_RECORD") + "  where SERVER_STATE = 2 and SESSION_ID = ?";
      String channelId = query.queryForString(sql, new Object[]{sessionId});
      //查询渠道按键关联的技能组列表
      sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME from ycbusi.CC_SKILL_GROUP where SKILL_GROUP_ID IN(select distinct SKILL_GROUP_ID  from ycbusi.CC_SKILL_GROUP_CHANNEL where CHANNEL_ID = ?)  order by IDX_ORDER";
      List<JSONObject> groups1 = query.queryForList(sql, new Object[] { channelId }, new JSONMapperImpl());

      //查询渠道配置的转移技能组列表
      sql = "select SKILL_GROUP_ID,SKILL_GROUP_NAME from ycbusi.CC_SKILL_GROUP where SKILL_GROUP_ID IN(select distinct SKILL_GROUP_ID  from CC_CHANNEL_TRAN_GROUP where CHANNEL_ID = ?)  order by IDX_ORDER";
      List<JSONObject> groups2 = query.queryForList(sql, new Object[] { channelId }, new JSONMapperImpl());

      //合并技能组列表
      groups2.addAll(groups1);
      ArrayList<JSONObject> newGroups = new ArrayList<>();
      String groupIdStr = "";
      for (JSONObject group : groups2){
        String skillGroupId = group.getString("SKILL_GROUP_ID");
        String key = "mediacenter_monitor_group_" + skillGroupId;
        String groupInfo = this.cache.get(key);

        if(groupIdStr.contains(skillGroupId)){
          continue;
        }
        groupIdStr += skillGroupId;

        if (!StringUtils.isBlank(groupInfo))
        {
          JSONObject groupJson = JSONObject.parseObject(groupInfo);
          JSONArray agents = groupJson.getJSONArray("onlineAgents");
          if (agents == null)
          {
            agents = new JSONArray();
            MediaLogger.getLogger().warn(" 获取全媒体转接坐席列表, 缓存里该技能组下没有空闲坐席，技能组缓存key:" + key);
          }
          JSONArray _agents = new JSONArray();
          for (int i = 0; i < agents.size(); i++)
          {
            JSONObject agent = agents.getJSONObject(i);
            if ((agent.getBooleanValue("isReady")) && 
              (!getAgentId().equals(agent.get("agentId"))))
            {
              JSONObject _agent = new JSONObject();
              _agent.put("agentId", agent.get("agentId"));
              _agent.put("agentName", agent.get("agentName"));
              _agent.put("agentState", agent.get("agentState"));
              _agent.put("serviceLimitCount", agent.get("maxServiceCount"));
              _agent.put("curServiceCount", agent.get("curServiceCount"));
              _agents.add(_agent);
            }
          }
          group.put("agents", _agents);
          group.put("agentCount",_agents.size());
          newGroups.add(group);
        }
      }
      data.put("groups", newGroups);
      return getDataResult("AgentList", "succ", "succ", data);
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error("查询坐席列表失败，原因：" + ex.getMessage(), ex);
      return getDataResult("AgentList", "fail", "查询坐席列表失败，原因：" + ex.getMessage(), null);
    }
  }
  
  public EasyResult actionForServiceInfo()
  {
	  setResHeader();
    EasyCalendar cal = EasyCalendar.newInstance();
    JSONObject data = new JSONObject();
    String serviceLimitCount = (String)this.cache.get("SERVICE_LIMITCOUNT_" + getAgentId());
    String maxServiceLimitCount = "50";
    String minServiceLimitCount = "5";
    try {
      YCUserPrincipal userPrincipal = this.getYCUserPrincipal();
      if (userPrincipal == null) {
        return EasyResult.fail("当前用户的session已失效，请重新登录系统");
      }
      String sql = "select MAX_SERVER ,MIN_SERVER  from " + getTableName("CC_BUSI_USER") + " where USER_ID = ? and BUSI_ORDER_ID = ? and ENT_ID = ?";
      EasyRow queryForRow = getQuery().queryForRow(sql, new Object[] { getYCUserPrincipal().getUserId(), getBusiOrderId(), getEntId() });
      if (queryForRow != null)
      {
        maxServiceLimitCount = queryForRow.getColumnValue("MAX_SERVER");
        minServiceLimitCount = queryForRow.getColumnValue("MIN_SERVER");
      }
      if (StringUtils.isBlank(maxServiceLimitCount)) {
        maxServiceLimitCount = "50";
      }
      if (StringUtils.isBlank(minServiceLimitCount)) {
        minServiceLimitCount = "5";
      }

      //缓存中不存在当前服务上限，设置默认服务上限为5
      if (StringUtils.isBlank(serviceLimitCount)) {
        serviceLimitCount = "5";
        MediaLogger.getLogger().warn("MediaEventServlet.actionForServiceInfo() 坐席["+getAgentId()+"] 缓存中不存在当前服务上限，设置默认服务上限为5并更新至缓存中！");
        updateCount(minServiceLimitCount, getEntId(), getAgentId(),"admin@Mars");
        serviceLimitCount = minServiceLimitCount;
      }else{
        if (Integer.parseInt(serviceLimitCount) < Integer.parseInt(minServiceLimitCount))
        {
          MediaLogger.getLogger().warn("MediaEventServlet.actionForServiceInfo() 坐席["+getAgentId()+"] 当前缓存中的服务上限（"+serviceLimitCount+"）< 最小服务上限（"+minServiceLimitCount+"）强制更新”坐席当前服务上限为"+minServiceLimitCount+"至缓存中！");
          updateCount(minServiceLimitCount, getEntId(), getAgentId(),"admin@Mars");
          serviceLimitCount = minServiceLimitCount;
        }
      }

    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error(ex.getMessage(), ex);
    }
    data.put("serviceLimitCount", serviceLimitCount);
    data.put("maxServiceLimitCount", maxServiceLimitCount);
    data.put("minServiceLimitCount", minServiceLimitCount);
    EasyResult result = EasyResult.ok();
    result.setData(data);
    return result;
  }
  
  private String getAgentStateInfo(String entId, String agentId, String command)
  {
    JSONObject jsonObject = new JSONObject();
    jsonObject.put("entId", entId);
    jsonObject.put("agentId", agentId);
    jsonObject.put("command", command);
    
    return jsonObject.toJSONString();
  }
  
  /**
   * 全媒体-加载会话聊天记录
   * @Description :加载会话聊天记录
   * <AUTHOR>
   * @Datetime 2021/10/19 15:54
   * @return: org.easitline.common.core.web.EasyResult
   */
  public EasyResult actionForLoadMessage()
  {
    try{
	  setResHeader();
      String cmdJsonString = getRequest().getParameter("cmdJson");
      JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
      String sessionId = cmdJson.getString("sessionId");
      String msgTime = cmdJson.getString("msgTime");
      List<MessageModel> list = ChatContext.getMessage(getEntId()).load(sessionId, msgTime);
      EasyResult result = EasyResult.ok();
      result.setData(list);
      return result;
    }catch (Exception e){
      MediaLogger.getLogger().error(e.getMessage(),e);
      return EasyResult.fail();
    }
  }
  
  /**
   * 全媒体-加载历史会话聊天记录
   * @Description :加载历史会话聊天记录，用于“接触历史”中查看聊天记录
   * <AUTHOR>
   * @Datetime 2021/10/19 15:57
   * @return: org.easitline.common.core.web.EasyResult
   */
  public EasyResult actionForLoadHistMessage()
    throws Exception
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String chatSessionId = cmdJson.getString("serialId");
    String msgTime = cmdJson.getString("msgTime");
    List<MessageModel> list = ChatContext.getMessage(getEntId()).loadHis(chatSessionId, msgTime);
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForLoadSupportGroups()
    throws Exception
  {
	  setResHeader();
    String sql = "select t1.ent_id,t1.channel_key,t2.key_code,t2.key_name from cc_channel t1, cc_channel_key t2  where t1.channel_id = t2.channel_id  and t1.ent_id = ? and t1.channel_key = '000000' order by t2.key_code";
    

    List<JSONObject> list = new ArrayList();
    try
    {
      list = getQuery().queryForList(sql, new Object[] { getEntId() }, new JSONMapperImpl());
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error("MediaEventServlet.actionForLoadSupportGroups() error,cause:" + ex.getMessage(), ex);
      list = new ArrayList();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForFindSessionMessage()
    throws Exception
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    MediaLogger.getLogger().info("cmdJsonString->" + cmdJsonString);
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String sessionId = cmdJson.getString("sessionId");
    String startTime = cmdJson.getString("startTime");
    String endTime = cmdJson.getString("endTime");
    String key = cmdJson.getString("key");
    List<MessageModel> list = ChatContext.getMessage(getEntId()).findSessionMessage(sessionId, key, startTime, endTime);
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForFindCustMessage()
    throws Exception
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String sessionId = cmdJson.getString("sessionId");
    String key = cmdJson.getString("key");
    String startTime = cmdJson.getString("startTime");
    String endTime = cmdJson.getString("endTime");
    List<MessageModel> list = ChatContext.getMessage(getEntId()).findCustMessage(sessionId, key, startTime, endTime);
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForFindAllMessage()
    throws Exception
  {
	  setResHeader();
    if (getYCUserPrincipal() == null) {
      return getDataResult("FindAllMessage", "fail", "发送失败，原因:session已失效，请重新登录系统", null);
    }
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String key = cmdJson.getString("key");
    String startTime = cmdJson.getString("startTime");
    String endTime = cmdJson.getString("endTime");
    List<MessageModel> list = ChatContext.getMessage(getEntId()).findAllMessage(key, startTime, endTime);
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }

  /**
   * 全媒体-查询在线用户列表
   * @Description :查询坐席正在服务的在线用户列表
   * <AUTHOR>
   * @Datetime 2021/10/19 15:48
   * @return: org.easitline.common.core.web.EasyResult
   */
  public EasyResult actionForChatList()
    throws ServletException, IOException
  {
	  setResHeader();
//	  String sql = "select  t1.*,(SELECT COUNT(1) FROM " + Constants.getYwdbSchema() + ".C_OL_CONSULT_ORDER t2 where t2.SESSION_ID=t1.SERIAL_ID) ZX_ORDER_NUM from " + getTableName("CC_MEDIA_RECORD") + " t1 where t1.AGENT_ID = ?  and t1.ENT_ID = ? and t1.SERVER_STATE = 2 order by  t1.BEGIN_TIME desc ";
    String sql = "select  t1.* from " + getTableName("CC_MEDIA_RECORD") + " t1 where t1.AGENT_ID = ?  and t1.ENT_ID = ? and t1.BEGIN_TIME >= ? and t1.SERVER_STATE = 2 order by  t1.BEGIN_TIME desc ";
    List<SessionModel> list = new ArrayList<SessionModel>();
    try
    {
    	String orderNumSql = "SELECT COUNT(1) FROM " + Constants.getYwdbSchema() + ".C_OL_CONSULT_ORDER t2 where t2.SESSION_ID = ?";
    	EasyQuery query = getQuery();
    	EasyCalendar cal = EasyCalendar.newInstance();
    	cal.add(EasyCalendar.DAY, -1);
    	list = query.queryForList(sql, new Object[] { getAgentId(), getEntId(),cal.getDateTime("-") }, 1, 50, new SessionRowMapper());
    	
    	for (SessionModel sessionModel : list) {
    		int zxOrderNum = query.queryForInt(orderNumSql, new Object[] {sessionModel.getSerialId()});

            // 设置客户标签值
            MediaLogger.getLogger().info("获取客户标签缓存，key：" + sessionModel.getSessionId() + this.getYCUserPrincipal().getLoginAcct());
            sessionModel.setLabelValue(this.cache.get(sessionModel.getSessionId() + this.getUserPrincipal().getLoginAcct()));
    		sessionModel.setZxOrderNum(zxOrderNum);
    	}
    }catch (Exception ex){
      MediaLogger.getLogger().error("MediaEventServlet.actionForHisChatList() error,cause:" + ex.getMessage(), ex);
      list = new ArrayList<SessionModel>();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  /**
   * 全媒体-查询用户历史会话列表
   * @Description :查询用户历史会话列表，和接口：/online/servlet/contact?action=custHistChatList一样，用于“接触历史”展示，未使用
   * <AUTHOR>
   * @Datetime 2021/10/19 15:47
   * @return: org.easitline.common.core.web.EasyResult
   */
  public EasyResult actionForCustHistChatList()
    throws ServletException, IOException
  {
	  setResHeader();
    EasyCalendar cal = EasyCalendar.newInstance();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String sessionId = cmdJson.getString("sessionId");
    String sql = "select  * from " + getTableName("CC_MEDIA_RECORD") + " where  SESSION_ID = ?  and ENT_ID = ? and SERVER_STATE = 3 order by  BEGIN_TIME desc ";
    List<SessionModel> list = new ArrayList();
    try
    {
      list = getQuery().queryForList(sql, new Object[] { sessionId, getEntId() }, 1, 50, new SessionRowMapper());

      list.forEach(sessionModel -> {
        // 设置客户标签值
        sessionModel.setLabelValue(this.cache.get(sessionModel.getSessionId() + this.getUserPrincipal().getLoginAcct()));
      });
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error(ex.getMessage(), ex);
      list = new ArrayList();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForTodayHisChatList()
    throws ServletException, IOException
  {
	  setResHeader();
    EasyCalendar cal = EasyCalendar.newInstance();
    
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String content = cmdJson.getString("content");
    
    EasySQL sql = new EasySQL();
    sql.append(" SELECT t1.*,(SELECT COUNT(1) FROM " + Constants.getYwdbSchema() + ".C_OL_CONSULT_ORDER t2 where t2.SESSION_ID=t1.SERIAL_ID) ZX_ORDER_NUM ");
    sql.append(" FROM " + getTableName("CC_MEDIA_RECORD") + " t1 WHERE 1=1 ");
    sql.append(Integer.valueOf(cal.getDateInt()), " and t1.DATE_ID = ? ");
    sql.append(getAgentId(), " and t1.AGENT_ID = ? ");
    sql.append(getEntId(), " and t1.ENT_ID = ? ");
    sql.append("3", " and t1.SERVER_STATE = ? ");
    if (StringUtils.isNotBlank(content))
    {
      sql.appendLike(content, " and (t1.CUST_CODE like ?  ");
      sql.appendLike(content, " OR t1.CUST_NAME like ?  ");
      sql.appendLike(content, " OR EXISTS(SELECT 1 FROM " + getTableName("CC_MEDIA_CHAT_RECORD") + " T3 WHERE T3.CHAT_SESSION_ID=T1.SERIAL_ID AND T3.MSG_CONTENT LIKE ?))  ");
    }
    sql.append(" order by  t1.BEGIN_TIME desc ");
    

    List<SessionModel> list = new ArrayList();
    try
    {
      list = getQuery().queryForList(sql.getSQL(), sql.getParams(), 1, 50, new SessionRowMapper());
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error(ex.getMessage(), ex);
      list = new ArrayList();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }

  @Deprecated
  public EasyResult actionForLogin() throws Exception
  {
	  setResHeader();
    String cmdJsonString = getRequest().getParameter("cmdJson");
    
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    if (cmdJson == null) {
      return getDataResult("SDKLogin", "fail", "签入失败，原因:cmdJson is null!", null);
    }
    String agentId = cmdJson.getString("agentId");
    
    String entId = cmdJson.getString("entId");
    
    String token = cmdJson.getString("token");
    
    String productId = cmdJson.getString("productId");
    
    String loginKey = cmdJson.getString("loginKey");
    if (StringUtils.isBlank(entId)) {
      return getDataResult("AgentLogin", "fail", "签入失败，原因:企业ID为空", null);
    }
    if (StringUtils.isBlank(agentId)) {
      return getDataResult("AgentLogin", "fail", "签入失败，原因:坐席工号为空", null);
    }
    if (StringUtils.isBlank(token)) {
      return getDataResult("AgentLogin", "fail", "签入失败，原因:登录token为空", null);
    }
    if (StringUtils.isBlank(productId)) {
      return getDataResult("AgentLogin", "fail", "签入失败，原因:productId为空", null);
    }
    if (StringUtils.isBlank(loginKey)) {
      return getDataResult("AgentLogin", "fail", "签入失败，原因:loginKey为空", null);
    }
    MediaLogger.getLogger().info("servlet.sessionId->" + getRequest().getSession().getId());
    
    String _sessionId = (String)this.cache.get("CCBAR_LOGIN_AGENT_" + getAgentId());
    if ((StringUtils.isNotBlank(_sessionId)) && 
      (!getRequest().getSession().getId().equals(_sessionId))) {
      return getDataResult("Login", "fail", "签入失败[403]，原因:坐席工号已签入", null);
    }
    String sql = "select count(1)  from CC_BUSI_ORDER  where BUSI_ID = ? and  ENT_ID = ?";
    try
    {
      if (!QueryFactory.getQuery(null).queryForExist(sql, new Object[] { productId, entId }))
      {
        CcbarLogger.getLogger().warn("<CCBAR-SDKLogin> Login error,cause: busiId[" + productId + 
          "] order not found!,loginInfo->" + cmdJsonString);
        return getDataResult("SDKLogin", "fail", "签入失败，产品订购信息[" + productId + "]不存在!", null);
      }
    }
    catch (Exception ex)
    {
      CcbarLogger.getLogger().error(ex, ex);
      return EasyResult.error(500, "系统处理请求失败，原因:" + ex.getMessage());
    }
    sql = "select count(1) from  CC_USER  where USER_ACCT = ? ";
    try
    {
      if (!QueryFactory.getQuery(null).queryForExist(sql, new Object[] { agentId }))
      {
        CcbarLogger.getLogger().warn("<CCBAR-SDKLogin> Login error,cause: Agent[" + agentId + 
          "] not exist!,loginInfo->" + cmdJsonString);
        return getDataResult("SDKLogin", "fail", "签入失败[404]，原因：坐席工号[" + agentId + "]不存在!", null);
      }
    }
    catch (Exception ex)
    {
      CcbarLogger.getLogger().error("<CCBAR-SDKLogin> " + agentId + "登录失败[500]，原因：" + ex.getMessage(), ex);
      return EasyResult.error(500, "系统处理请求失败，原因:" + ex.getMessage());
    }
    YCUserPrincipal principal = getYCUserPrincipal();
    if (principal != null)
    {
      String _agentId = getAgentId();
      if (!agentId.equals(_agentId)) {
        try
        {
          CcbarLogger.getLogger().warn("<CCBAR-SDKLogin> 当前签入账号["+agentId+"]与登录信息账号["+_agentId+"]不一致，执行退出-->"+JSONObject.toJSONString(principal));
          getRequest().logout();
          Thread.sleep(2000L);
        }
        catch (Exception ex)
        {
          CcbarLogger.getLogger().error(ex, ex);
        }
      }
    }
    principal = getYCUserPrincipal();
    if (principal == null) {
      try
      {
        getRequest().login(agentId, token);
      }
      catch (Exception ex)
      {
        CcbarLogger.getLogger().error("<CCBAR-SDKLogin> " + agentId + " SSO登录失败[403]，原因：" + ex.getMessage(),ex);
        return getDataResult("SDKLogin", "fail", "签入失败[SSO:403]，原因：坐席工号或密码错误", null);
      }
    }
    principal = getYCUserPrincipal();
    if (principal == null)
    {
      CcbarLogger.getLogger().warn("<" + agentId + ">执行SSO登录后，获取principal对象为空，登录失败,登录信息->" + cmdJsonString);
      return getDataResult("CcbarLogin", "fail", "签入失败[SSO:404]，原因：坐席工号或密码错误", null);
    }
    principal.setBusiId(productId);
    
    this.eventMessage.getAgentEvent().clear(getAgentId());
    GenesysLogger.getLogger().info("[clearAgentEvent]->" + getAgentId());
    
    JSONObject data = new JSONObject();
    EasyResult result = null;
    String command = "cmdLogin";
    String resp = "";
    try
    {
      MediaCacheUtil.put(Constants.BROKER_AGENT_NAME + getAgentId(), Constants.getAgentBrokerName(), 3600*24);
      MediaProducerBroker.sendMediaCenterMessage(getAgentStateInfo(getEntId(), getAgentId(), command));
      resp = getCommandResult(getAgentId(), command);
      if ("000".equals(resp))
      {
        String agentState = MediaAgentStateEnum.BUSY.getReq(getEntId(), getAgentId()).toJSONString();
        String respLogin = MediaRespCmdEnum.Login.getReq(getEntId(), getAgentId(), RespResultEnum.SUCCESS).toJSONString();
        

//        CcbarSocket.sendMessageToAgent("State", getAgentId(), respLogin);
//        CcbarSocket.sendMessageToAgent("Event", getAgentId(), agentState);
        WebSocket.sendMessageToAgent( getAgentId(), respLogin);
        WebSocket.sendMessageToAgent( getAgentId(), agentState);
        
        result = getDataResult("Login", "succ", "succ", data);
      }
      else if (StringUtils.isBlank(resp))
      {
        result = getDataResult("Login", "fail", "签入全媒体坐席失败,原因：消息响应超时", data);
      }
      else
      {
        result = getDataResult("Login", "fail", "签入全媒体坐席失败[" + result + "]", data);
      }
    }
    catch (Exception ex)
    {
      result = getDataResult("Login", "fail", "请求失败，原因：" + ex.getMessage(), data);
      MediaLogger.getLogger().error("actionForLogin() error,cause:" + ex.getMessage(), ex);
    }
    MediaLogger.getLogger().info("<Resp>[" + getAgentId() + "][" + command + ":" + resp + "] >>" + result);
    return result;
  }
  
  public EasyResult actionForTodayHisChatListByHisContent()
    throws ServletException, IOException
  {
	  setResHeader();
    EasyCalendar cal = EasyCalendar.newInstance();
    String sql = " select Distinct session_id from  ycbusi.cc_media_record where serial_id in ( select Distinct chat_session_id sessionid from  " + 
      getTableName("CC_MEDIA_CHAT_RECORD") + " where chat_session_id in " + 
      
      "(\tselect session_id from (select session_id from " + getTableName("CC_MEDIA_RECORD") + " where AGENT_ID = ?  and ENT_ID = ?  and DATE_ID = ?  order by  BEGIN_TIME desc) where ROWNUM<=100)" + 
      " and MSG_CONTENT  LIKE '%?%' )";
    List<SessionModel> list = new ArrayList();
    try
    {
      list = getQuery().queryForList(sql, new Object[] { getAgentId(), getEntId(), Integer.valueOf(cal.getDateInt()), getRequest().getParameter("conetnt") }, 1, 100, new SessionRowMapper());
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error(ex.getMessage(), ex);
      list = new ArrayList();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  /**
   * 全媒体-查询历史会话列表
   * @Description :查询历史会话列表，用于历史联系人展示
   * <AUTHOR>
   * @Datetime 2021/10/19 15:52
   * @return: org.easitline.common.core.web.EasyResult
   */
  public EasyResult actionForHisChatList()
    throws ServletException, IOException
  {
	  setResHeader();
    String sql = "select  t1.* from " + getTableName("CC_MEDIA_RECORD") + " t1 where t1.AGENT_ID = ?  and t1.ENT_ID = ? and t1.BEGIN_TIME >= ? and t1.SERVER_STATE = 3 order by  t1.BEGIN_TIME desc ";
    List<SessionModel> list = new ArrayList<SessionModel>();
    try
    {
    	String orderNumSql = "SELECT COUNT(1) FROM " + Constants.getYwdbSchema() + ".C_OL_CONSULT_ORDER t2 where t2.SESSION_ID = ?";
    	EasyQuery query = getQuery();
    	
    	EasyCalendar cal = EasyCalendar.newInstance();
    	cal.add(EasyCalendar.DAY, -7);
    	list = query.queryForList(sql, new Object[] { getAgentId(), getEntId(),cal.getDateTime("-") }, 1, 50, new SessionRowMapper());
    	
    	for (SessionModel sessionModel : list) {
    		int zxOrderNum = query.queryForInt(orderNumSql, new Object[] {sessionModel.getSerialId()});
    		sessionModel.setZxOrderNum(zxOrderNum);
            // 设置客户标签值
            sessionModel.setLabelValue(this.cache.get(sessionModel.getSessionId() + this.getUserPrincipal().getLoginAcct()));
    	}
    }catch (Exception ex){
      MediaLogger.getLogger().error(ex.getMessage(), ex);
      list = new ArrayList<SessionModel>();
    }
    
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  public EasyResult actionForHisChatListByHisContent()
    throws ServletException, IOException
  {
	  setResHeader();
    EasyCalendar cal = EasyCalendar.newInstance();
    
    String cmdJsonString = getRequest().getParameter("cmdJson");
    JSONObject cmdJson = JSONObject.parseObject(cmdJsonString);
    String content = cmdJson.getString("content");
    
    EasySQL sql = new EasySQL();
    sql.append(getAgentId(), " select t1.*,(SELECT COUNT(1) FROM " + Constants.getYwdbSchema() + ".C_OL_CONSULT_ORDER t2 where t2.SESSION_ID=t1.SERIAL_ID) ZX_ORDER_NUM from " + getTableName("CC_MEDIA_RECORD") + " t1 where t1.AGENT_ID = ?  ");
    sql.append(getEntId(), " and t1.ENT_ID = ?  and t1.SERVER_STATE = 3 ");
    if (StringUtils.isNotBlank(content))
    {
      sql.appendLike(content, " and (t1.CUST_CODE like ?  ");
      sql.appendLike(content, " OR t1.CUST_NAME like ?  ");
      sql.appendLike(content, " OR EXISTS(SELECT 1 FROM " + getTableName("CC_MEDIA_CHAT_RECORD") + " T3 WHERE T3.CUST_SESSION_ID = T1.SESSION_ID AND T3.MSG_CONTENT LIKE ?))  ");
    }
    sql.append(" order by  t1.BEGIN_TIME desc ");
    List<SessionModel> list = new ArrayList();
    try
    {
      list = getQuery().queryForList(sql.getSQL(), sql.getParams(), 1, 50, new SessionRowMapper());
    }
    catch (Exception ex)
    {
      MediaLogger.getLogger().error(ex.getMessage(), ex);
      list = new ArrayList();
    }
    EasyResult result = EasyResult.ok();
    result.setData(list);
    return result;
  }
  
  protected String getResId()
  {
    return null;
  }

  /**
   * 获取坐席所属渠道列表
   * @return EasyResult；
   */
  public EasyResult actionForGetMyChannels() {
    EasyResult result = new EasyResult();
    try {
      String agentId =  getRequest().getParameter("agentId");//坐席账号，如：8001@1000
      agentId = StringUtils.isBlank(agentId)?this.getYCUserPrincipal().getLoginAcct():agentId;
      AgentModel agentInfo = AgentInfos.getAgentInfo(agentId);

      List<EasyRow> channels = agentInfo.getChannels();
      List<JSONObject> channelList = new ArrayList<>();
      for (EasyRow easyRow : channels) {
        JSONObject cObj = new JSONObject();
        cObj.put("CHANNEL_ID", easyRow.getColumnValue("CHANNEL_ID"));
        cObj.put("CHANNEL_KEY", easyRow.getColumnValue("CHANNEL_KEY"));
        cObj.put("CHANNEL_NAME", easyRow.getColumnValue("CHANNEL_NAME"));
        cObj.put("CHANNEL_STATE", easyRow.getColumnValue("CHANNEL_STATE"));
        cObj.put("CHANNEL_TYPE", easyRow.getColumnValue("CHANNEL_TYPE"));
        channelList.add(cObj);
      }
      CcbarLogger.getLogger().info("channelList :"+channelList);
      result.setData(channelList);
    } catch (Exception e) {
      MediaLogger.getLogger().error(e.getMessage(), e);
      result.addFail("获取坐席所属渠道列表失败");
    }
    return result;
  }


  /**
   * 获取坐席所属技能组列表
   * @return EasyResult；
   */
  public EasyResult actionForGetMySkillQueue() {
    EasyResult result = new EasyResult();
    try {
      CcbarLogger.getLogger().info("SELECT t1.SKILL_GROUP_ID  FROM ycbusi.cc_skill_group_user t1 where  t1.USER_ID =  ?  and t1.BUSI_ORDER_ID = ? ,"+this.getRemoteUser()+","+this.getBusiOrderId());
      String sql = "SELECT t1.SKILL_GROUP_ID  FROM ycbusi.cc_skill_group_user t1 where  t1.USER_ID =  ?  and t1.BUSI_ORDER_ID = ? ";

      List<JSONObject> row = this.getQuery().queryForList(sql, new Object[]{this.getRemoteUser(),this.getBusiOrderId()},new JSONMapperImpl());
      result.setData(row);
    } catch (Exception e) {
      MediaLogger.getLogger().error(e.getMessage(), e);
      result.addFail("获取坐席所属渠道列表失败");
    }
    return result;
  }
}
