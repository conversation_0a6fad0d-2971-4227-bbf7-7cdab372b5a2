<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title>全媒体渠道配置管理</title>
	<style type="text/css">
		.dropdown-icon>li>.addon{top:42%;}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
       <form action="" method="post" name="searchForm" class="form-inline" id="searchForm" data-toggle="render">
             	<div class="ibox">
             		<div class="ibox-title clearfix">
						 <div class="form-group">
						      <input type="hidden" id="entId" name="entId" value="${param.entId}">
	             		      <h5> 全媒体渠道配置管理</h5>
             		          <div class="input-group input-group-sm">
								      <span class="input-group-addon">渠道名称</span>	
									  <input type="text" name="channelName" class="form-control input-sm" style="width:100px">
							   </div>
							 <div class="input-group input-group-sm">
								 <span class="input-group-addon">渠道标识</span>
								 <input type="text" name="channelKey" class="form-control input-sm" style="width:100px">
							 </div>
							   <div class="input-group input-group-sm">
								      <span class="input-group-addon">渠道类型</span>	
                                      <select class="form-control input-sm" id="channelType" name="channelType">
			                      			<option value="">请选择</option>
			                      			<option value="1">网页</option>
			                      			<option value="2">微信</option>
			                      			<option value="3">微博</option>
			                      			<option value="4">语音</option>
			                      			<option value="5">APP</option>
			                      			<option value="6">视频</option>
			                      	  </select>
			                   </div>
			                   <div class="input-group input-group-sm">
								      <span class="input-group-addon">渠道状态</span>	
                                      <select class="form-control input-sm" id="channelState" name="channelState">
			                      			<option value="">请选择</option>
			                      			<option value="0">正常</option>
			                      			<option value="1">停用</option>
			                      	  </select>
			                   </div>
							   <div class="input-group input-group-sm">
										<button type="button" class="btn btn-sm btn-default" onclick="Channel.searchData()"><span class="glyphicon glyphicon-search"></span> 搜索</button>
								</div>
							   <div class="input-group input-group-sm pull-right btn-group">
								   <div class="btn-group">
									   <button type="button" class="btn btn-success btn-outline btn-sm mr-10 dropdown-toggle"
											   data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
										   更多配置 <span class="caret"></span>
									   </button>
									   <ul class="dropdown-menu">
										   <li><a href="javascript:void(0);" onclick="Channel.satisfExt()">满意度细项管理</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.spRoleGroupMgr()">角色分组管理</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.spRoleMgr()">访客角色管理</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.spCustMgr()">H5特殊访客管理</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.whitelistMgr()">H5输入联想白名单</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.artificialMgr()">人机辅助渠道管理</a></li>
										   <li><a href="javascript:void(0);" onclick="Channel.memberStrategy()">会员身份策略管理</a></li>

									   </ul>
								   </div>
							       <button type="button" class="btn btn-sm btn-success" onclick="Channel.addData()">+新增渠道</button>
							       <button type="button" class="btn btn-sm btn-primary" onclick="Channel.updateTime()"><i class="glyphicon glyphicon-cog"></i>修改时间</button>
							   </div>

						  </div>
             	    </div>  
	              	<div class="ibox-content">
		           	     <table class="table table-auto table-bordered table-hover table-condensed" data-auto-fill="10" id="tableHead" data-mars="channel.list">
                             <thead>
	                         	 <tr>
									<th class="text-c"><label class="checkbox checkbox-info"><input
										type="checkbox" name="checkAll" value=""><span></span></label></th>    								<th>编号</th>
    								<th>渠道名称</th>
    								<th>渠道key</th>
    								<th>渠道类型</th>
    								<th>渠道状态</th>
								    <th>发布时间</th>
								    <th>操作</th>
		   						 </tr>
                             </thead>
                             <tbody id="dataList">
                             </tbody>
                        	 <script id="list-template" type="text/x-jsrender">
								   {{for list}}
										<tr>
											<td class="text-c"><label class="checkbox checkbox-info"><input type="checkbox"  data-id="{{:CHANNEL_ID}}" ><span></span></label></td>
											<td>{{:#index+1}}</td>
                                    		<td>{{:CHANNEL_NAME}}</td>
                                    		<td>{{:CHANNEL_KEY}}</td>
                                    		<td>{{getText:CHANNEL_TYPE '#channelType'}}</td>
                                    		<td>{{getText:CHANNEL_STATE '#channelState'}}</td>
											<td>{{:CREATE_TIME}}</td>
											<td  class="text-l hover">
													<div class="btn-group btn-group-xs dropdown ml-10">
													<button type="button" class="btn btn-default btn-xs dropdown-toggle" data-toggle="dropdown">
														 操作 <span class="caret"></span>
													</button>
													<ul class="dropdown-menu dropdown-icon" role="menu">
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-edit"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.editData('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_TYPE}}')">编辑</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-wrench"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.configRobot('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">机器人配置</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-header"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.configStyle('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">H5客户端配置</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-hand-right"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.selectGroup('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">转移技能组</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-hand-right"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.viewMemberStrategies('{{:CHANNEL_KEY}}','{{:CHANNEL_NAME}}')">高价值用户接待策略</a>
														</li>
													{{if CHANNEL_TYPE != 4 }}
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-list-alt"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.autoConfig('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">自动回复语配置</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-pencil"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.configKey('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">按键配置</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-heart-empty"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.satisfConfig('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_KEY}}')">满意度指标</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-list-alt"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.noticeMgr('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_KEY}}')">公告管理</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-th"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.selfHelpMgr('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_KEY}}')">自助服务专区管理</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-tag"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.channelLabel('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_KEY}}')">标签管理</a>
														</li>
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-pencil"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.userTodoMgr('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}','{{:CHANNEL_KEY}}')">事项待办管理</a>
														</li>
													{{/if}}
														<li>
															<div class="addon">
																<i class="glyphicon glyphicon-trash"></i>
															</div>
															<a  href="javascript:void(0)" onclick="Channel.delOrder('{{:CHANNEL_ID}}','{{:CHANNEL_NAME}}')">删除</a>
														</li>
													</ul>
												</div>
											</td>
									    </tr>
								   {{/for}}					         
							 </script>
		                 </table>
	                     <div class="row paginate">
	                     		<jsp:include page="/pages/common/pagination_more.jsp"/>
	                     </div> 
	              	</div> 
                </div>
        </form>
        <script id="changeWork" type="text/x-jsrender">
		<form id="updatesForm"    method="post"  autocomplete="off" >
						工作时间1
						<input type="text" class="form-control input-sm work-time" id="startDate1" value="08:30" name="BEGIN_WORK_TIME1"  data-mars-top="true" style="height:30px;width:135px;display:inline " >
		           	 		-
		           		 <input type="text" class="form-control input-sm work-time" id="endDate1" value="12:00" name="END_WORK_TIME1"  data-mars-top="true" style="height:30px;width:135px;display:inline" >
						<br>
						<br>
						工作时间2
						<input type="text" class="form-control input-sm work-time" id="startDate2" value="13:30" name="BEGIN_WORK_TIME2"   style="height:30px;width:135px;display:inline " >
		                  	-
		          	    <input type="text" class="form-control input-sm work-time" id="endDate2" value="18:00" name="END_WORK_TIME2"   style="height:30px;width:135px;display:inline " >
				</form>
					

  </script>
</EasyTag:override>

<EasyTag:override name="script">

	<script type="text/javascript">
		jQuery.namespace("Channel");
		$(function() {
			$("input[name='checkAll']").click(function() {
				var ifChecked = $(this).prop("checked");
				$("#dataList input:checkbox").prop("checked", ifChecked);
			})
		})
		Channel.entId = '${param.entId}';
		
		Channel.searchData=function(){
			$("#searchForm").searchData();
		}
		Channel.addData=function(){
		    popup.layerShow({type:1,title:'新增全媒体渠道',offset:'20px',area:['1000px','650px']},"${ctxPath}/pages/media/channel/channel-edit.jsp",{entId:Channel.entId});
		}
		Channel.editData=function(channelId,channelName,channelType){
		    popup.layerShow({type:1,title:'修改全媒体渠道('+channelName+')',offset:'20px',area:['1000px','650px']},"${ctxPath}/pages/media/channel/channel-edit.jsp",{entId:Channel.entId,channelId:channelId,channelType:channelType});
		}
		Channel.delOrder = function(channelId,channelName){
			layer.confirm('当前全媒体渠道将要被删除，是否继续？',{icon: 3, title:'删除提示',offset:'20px'},  function(index){
				layer.close(index);
		    	var data = {channelId:channelId,entId:Channel.entId};
		  		ajax.remoteCall("${ctxPath}/servlet/channel?action=delete", data, function(result) {
		  			if(result.state == 1){
					    layer.msg(result.msg,{icon: 1,time:1200},function(){
					    	Channel.searchData();
					    });
					}else{
						layer.alert(result.msg,{icon: 5});
					}
	  			});
			});
		}
		Channel.detailChannel = function(channelId){
		}
		Channel.autoConfig = function(channelId,channelName){
			popup.openTab("${ctxPath}/pages/media/channel/channel-auto-config-form.jsp","自动回复语配置（"+channelName+"）",{entId:Channel.entId,channelId:channelId,channelName:channelName});
		}
		Channel.configStyle = function(channelId,channelName){
			popup.layerShow({
				type: 2,
				title: "H5客户端配置(" + channelName + ")",
				offset: '20px',
				area: ['900px', '600px']
			}, "${ctxPath}/pages/media/channel/channel-h5-style-edit.jsp", {
				channelId:channelId,
				channelName:channelName,
				entId:Channel.entId
			});
		}
		Channel.configRobot = function(channelId,channelName){
			popup.layerShow({
				type: 2,
				title: "机器人配置(" + channelName + ")",
				offset: '20px',
				area: ['650px', '380px']
			}, "${ctxPath}/pages/media/channel/channel-robot-config.jsp", {channelId:channelId,channelName:channelName});
		}
		Channel.configKey = function(channelId,channelName){
			popup.openTab("${ctxPath}/pages/media/channel/channel-key-list.jsp","按键配置管理（"+channelName+"）",{channelId:channelId,channelName:channelName,entId:Channel.entId});
		}
		
		Channel.manageSW = function(){
			popup.openTab("${ctxPath}/pages/media/channel/channel-sw-list.jsp","渠道敏感词管理",{entId:Channel.entId});
		}
		Channel.satisfConfig = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/channel/channel-satisf-list.jsp","满意度指标配置（"+channelName+"）",{entId:Channel.entId,channelId:channelId,channelName:channelName,channelKey:channelKey});
		}

		var changeWorkEmplates = $.templates("#changeWork");
		Channel.updateTime=function(){
			var ids = $("#dataList").find("input[type='checkbox']:checked");
			if(ids.length<1){
				alert('请选择需要更新的渠道！');
				return;
			}
			var arr=new Array();
			for(var i=0;i<ids.length;i++){
				arr.push($(ids[i]).attr("data-id"));
			}
			var tablehtml = changeWorkEmplates.render();
			layer.open({
				title: '工作时间修改',
				area: ['420px', '200px'], //宽高
				btn: ['确认', '取消'],
				content: tablehtml,
				yes: function(index) {
					var data = form.getJSONObject("#updatesForm");
					data.ids = arr;
					data.entId=$("#entId").val();
					ajax.remoteCall("${ctxPath}/servlet/channel?action=updates", data, function(result) {
						if (result.state == 1) {
							layer.msg(result.msg, {
								icon: 1,
								time: 1200
							}, function() {
								clean();
							});
						} else {
							layer.alert(result.msg, {
								icon: 5
								,title:'信息',btn:['确定']
							});
						}
					});
				}
			})
			
				
		}

		Channel.noticeMgr = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/notice/notice-list.jsp","公告管理",{entId:Channel.entId,channelId:channelId});
		}
		Channel.satisfExt = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/channelConfig/satisf-ext-list.jsp","满意度细项管理",{entId:Channel.entId,channelId:channelId});
		}

		Channel.spRoleMgr = function(){
			popup.openTab("${ctxPath}/pages/media/h5SpRole/spRole-list.jsp","访客角色管理",{entId:Channel.entId});
		}
		Channel.spCustMgr = function(){
			popup.openTab("${ctxPath}/pages/media/h5SpCust/spCust-list.jsp","H5特殊访客管理",{entId:Channel.entId});
		}
		Channel.whitelistMgr = function(){
			popup.openTab("${ctxPath}/pages/media/h5Whitelist/whitelist-list.jsp","H5智能联想白名单管理",{entId:Channel.entId});
		}
		Channel.selfHelpMgr = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/selfHelp/self-help-list.jsp","自助服务专区管理",{entId:Channel.entId,channelId:channelId});
		}
		Channel.userTodoMgr = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/todo/user-todo-list.jsp","事项待办管理",{entId:Channel.entId,channelId:channelId});
		}
		Channel.channelLabel = function(channelId,channelName,channelKey){
			popup.openTab("${ctxPath}/pages/media/channelConfig/label-add.jsp","标签管理",{channelId:channelId});
		}
		Channel.spRoleGroupMgr = function(){
			popup.openTab("${ctxPath}/pages/media/h5SpRole/spRole-group-list.jsp","角色分组管理",{entId:Channel.entId});
		}
		Channel.artificialMgr = function(){
			popup.openTab("${ctxPath}/pages/media/artificial/artificial-list.jsp","人机辅助渠道管理",{entId:Channel.entId});
		}

		Channel.selectGroup=function(channelId,channelName){
			popup.layerShow({type:1,title:'选择转移技能组（'+channelName+'）',offset:'20px',area:['1000px','650px'],shadeClose:false},"${ctxPath}/pages/media/channel/channel-select-group.jsp",{channelId:channelId,channelName:channelName,entId:Channel.entId});
		}

		//高价值用户接待策略
		Channel.memberStrategy = function(){
			popup.openTab("${ctxPath}/pages/media/member/member-strategy-list.jsp","高价值用户接待策略管理",{entId:Channel.entId});
		}

		Channel.viewMemberStrategies=function(channelKey, channelName){
			popup.layerShow({type:1,title:'渠道身份策略 - ' + channelName,offset:'20px',area:['900px','600px']},"${ctxPath}/pages/media/member/channel-member-strategies.jsp",{entId:Channel.entId,channelKey:channelKey, channelName:channelName});
		}

	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp" %>