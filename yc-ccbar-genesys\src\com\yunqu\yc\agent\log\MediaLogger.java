package com.yunqu.yc.agent.log;

import com.yunqu.yc.agent.base.Constants;
import org.apache.log4j.Logger;
import org.easitline.common.core.log.LogEngine;



/**
 * 网关日志，需要根据具体的网关实现进行修改。
 * <AUTHOR>
 *
 */
public class MediaLogger {

   
   public static Logger  getLogger(){
	   return LogEngine.getLogger("yc-ccbar-media");
   }
   public static Logger  getAssistantLogger(){
       return LogEngine.getLogger("yc-ccbar-assistant");
   }
    public static Logger getCacheDataLogger(){
        return LogEngine.getLogger(Constants.APP_NAME+"-cacheData");
    }
    public static Logger getLongTxtLogger(){
        return LogEngine.getLogger(Constants.APP_NAME+"-longtxt");
    }
}
