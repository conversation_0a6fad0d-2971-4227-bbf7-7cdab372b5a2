package com.yunqu.yc.center.dao.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.utils.string.StringUtils;

/**
 * 渠道策略管理DAO
 * <AUTHOR>
 */
@WebObject(name="memberChannelStrategy")
public class MemberChannelStrategyDao extends AppDaoContext {

    /**
     * 渠道关联的会员列表（某个渠道关联的所有会员身份）
     */
    @WebControl(name="memberList", type=Types.LIST)
    public JSONObject memberList(){
        String channelKey = param.getString("channelKey");
        String entId = param.getString("entId");

        if(StringUtils.isBlank(channelKey)) {
            return getJsonResult(false);
        }
        String memberId = param.getString("memberId");
        EasySQL sql = this.getEasySQL("select t1.*,t2.MEMBER_ID,t2.MEMBER_NAME,t3.SKILL_GROUP_NAME");
        sql.append(" from CC_MEDIA_MEMBER_CHANNEL t1");
        sql.append(" left join CC_MEDIA_MEMBER_CONFIG t2 on t1.CONFIG_ID=t2.CONFIG_ID");
        sql.append(" left join "+getTableName(entId,"CC_SKILL_GROUP t1")+" t3 on t1.TO_AGENT_GROUP_ID=t3.SKILL_GROUP_ID");
        sql.append(" where 1=1");
        sql.append(channelKey, " and t1.CHANNEL_KEY = ?");
        sql.append(memberId, " and t2.MEMBER_ID = ?");
        sql.append(" order by t1.RANK_INDEX desc, t1.UPDATE_TIME desc");
        return queryForPageList(sql.getSQL(), sql.getParams());
    }
    
    /**
     * 获取单条渠道策略记录
     */
    @WebControl(name="record", type=Types.RECORD)
    public JSONObject record(){
        String channelKey = param.getString("channelKey");
        String configId = param.getString("configId");
        if(StringUtils.isAnyBlank(configId,channelKey)) {
            return getJsonResult(false);
        }
        return queryForRecord(new EasyRecord("CC_MEDIA_MEMBER_CHANNEL", "CHANNEL_KEY", "CONFIG_ID").setPrimaryValues(channelKey,configId));
    }

}
