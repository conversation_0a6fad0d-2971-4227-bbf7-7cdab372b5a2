<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<application id="online" name="Media-全媒体" package-by="ljp46" package-time="2025-07-10 09:07:33" version="1.0#20250814-1">
    <datasources>
        <datasource description="业务数据源" isnull="true" name="yw-ds"/> 
        <datasource description="Mars数据源" isnull="true" name="mars-ds"/> 
         <datasource description="YCBUSI数据源" isnull="true" name="ycbusi-ds"/> 
    </datasources>
    <description>
        1.0#20250814-1
            1.解决坐席工作台侧边栏“接触历史”，点击“聊天记录”无法打开聊天记录页面的问题。
        1.0#20250710-1
            1.优化席间交流，坐席工作台上传接口。
        1.0#20250703-1
            1.优化坐席辅助接口同步锁导致线程堵塞问题。
            2.优化坐席端工作台上传附件的返回文件路径问题。
        1.0#20250626-1
            1.坐席辅助接口增加同步锁逻辑，转发MQ消息给坐席时，携带serialId。
            2.增加专家咨询会话消息引用及满意度卡片。
    </description>
    <versionHistory>
        1.0#20250612-1 修复发送满意度异常问题。
        1.0#20250514-1 修复公共常用语查询问题。
        1.0#20250417-1 优化专家咨询窗口选择表情页无法正常显示的问题。
        1.0#20250220-1 话务满意度发送服务 缓存标识修改为唯一标识sessionId
        1.0#20250116-1 优化坐席会话窗口点击聊天记录-卡片消息（商品信息或工单信息）无跳转链接（url为空）后刷新整个页面的问题。
    </versionHistory>
</application>
