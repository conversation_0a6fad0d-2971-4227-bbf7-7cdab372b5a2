/*在线客服会员身份策略配置表*/
CREATE TABLE YCUSER.CC_MEDIA_MEMBER_CONFIG (
    CONFIG_ID VARCHAR2(64) NOT NULL,
    MEMBER_ID VARCHAR2(64) NOT NULL,
    MEMBER_NAME VARCHAR2(64) NOT NULL,
    RANK_INDEX NUMBER(3) DEFAULT 0,
    IS_TO_AGENT NUMBER(1) DEFAULT 0,
    TO_AGENT_GROUP_ID VARCHAR2(64),
    QUEUE_LEVEL NUMBER(3) DEFAULT 0,
    CHANNEL_KEYS VARCHAR2(1000),
    CHANNEL_NAMES VARCHAR2(4000),
    CREATE_TIME VARCHAR2(19) NOT NULL,
    UPDATE_TIME VARCHAR2(19) NOT NULL
    PRIMARY KEY (CONFIG_ID)
);

COMMENT ON TABLE YCUSER.CC_MEDIA_MEMBER_CONFIG IS '在线客服会员身份策略配置表';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.CONFIG_ID IS '记录ID（主键）';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.MEMBER_ID IS '会员等级id，从会员等级字典中选择';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.MEMBER_NAME IS '会员等级名称';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.RANK_INDEX IS '等级排序，越大越优先';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.IS_TO_AGENT IS '是否直接转人工，0否，1是';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.TO_AGENT_GROUP_ID IS '转人工技能组';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.QUEUE_LEVEL IS '排队接入优先级，越大越优先';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.CHANNEL_KEYS IS '关联渠道key集合，格式：key1,key2,key3';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.CHANNEL_NAMES IS '关联渠道名称集合，格式：name1,name2,name3';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.CREATE_TIME IS '创建时间，格式：yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CONFIG.UPDATE_TIME IS '更新时间，格式：yyyy-MM-dd HH:mm:ss';

CREATE UNIQUE INDEX IDX_MEMBER_CONFIG1 ON YCUSER.CC_MEDIA_MEMBER_CONFIG (MEMBER_ID);
CREATE UNIQUE INDEX IDX_MEMBER_CONFIG2 ON YCUSER.CC_MEDIA_MEMBER_CONFIG (RANK_INDEX);
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CONFIG to ycuser;
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CONFIG to ycbusi;
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CONFIG to ywdb;

/*在线客服会员策略生效渠道表*/
CREATE TABLE YCUSER.CC_MEDIA_MEMBER_CHANNEL (
    CHANNEL_KEY VARCHAR2(64) NOT NULL,
    CONFIG_ID VARCHAR2(64) NOT NULL,
    CHANNEL_NAME VARCHAR2(200) NOT NULL,
    RANK_INDEX NUMBER(3) DEFAULT 0,
    IS_TO_AGENT NUMBER(1) DEFAULT 0,
    TO_AGENT_GROUP_ID VARCHAR2(64),
    CREATE_TIME VARCHAR2(19) NOT NULL,
    UPDATE_TIME VARCHAR2(19) NOT NULL
    PRIMARY KEY (CHANNEL_KEY,CONFIG_ID)
);

COMMENT ON TABLE YCUSER.CC_MEDIA_MEMBER_CHANNEL IS '在线客服会员策略生效渠道表';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.CHANNEL_KEY IS '渠道KEY';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.CONFIG_ID IS '会员策略id';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.CHANNEL_NAME IS '渠道名称';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.RANK_INDEX IS '等级排序，越大越优先';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.IS_TO_AGENT IS '是否直接转人工，0否，1是';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.TO_AGENT_GROUP_ID IS '转人工技能组';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.CREATE_TIME IS '创建时间，格式：yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN YCUSER.CC_MEDIA_MEMBER_CHANNEL.UPDATE_TIME IS '更新时间，格式：yyyy-MM-dd HH:mm:ss';

CREATE UNIQUE INDEX IDX_MEMBER_CHANNEL1 ON YCUSER.CC_MEDIA_MEMBER_CHANNEL (CHANNEL_KEY,RANK_INDEX);
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CHANNEL to ycuser;
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CHANNEL to ycbusi;
grant insert,select,update,delete on YCUSER.CC_MEDIA_MEMBER_CHANNEL to ywdb;

/*在线客服会员策略进线记录表*/
CREATE TABLE YCBUSI.CC_MEDIA_MEMBER_RECORD (
    CHAT_SESSION_ID VARCHAR2(64) NOT NULL,
    CHANNEL_KEY VARCHAR2(64) NOT NULL,
    CONFIG_ID VARCHAR2(64) ,
    MEMBER_ID VARCHAR2(64) ,
    MEMBER_NAME VARCHAR2(64) ,
    SESSION_ID VARCHAR2(64) NOT NULL,
    CREATE_TIME VARCHAR2(19) NOT NULL,
    LEVEL_NAMES VARCHAR2(200) ,
    PRIMARY KEY (CHAT_SESSION_ID)
);
COMMENT ON TABLE YCBUSI.CC_MEDIA_MEMBER_RECORD IS '在线客服会员策略进线记录表';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.CHAT_SESSION_ID IS '记录ID';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.CHANNEL_KEY IS '渠道KEY';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.CONFIG_ID IS '会员策略id';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.MEMBER_ID IS '会员等级id，从会员等级字典中选择';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.MEMBER_NAME IS '会员等级名称';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.SESSION_ID IS '访客标识';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.CREATE_TIME IS '创建时间，格式：yyyy-MM-dd HH:mm:ss';
COMMENT ON COLUMN YCBUSI.CC_MEDIA_MEMBER_RECORD.LEVEL_NAMES IS '访客会员名称，多个名称示例：vip1,黑钻美粉,铂金美粉';

CREATE UNIQUE INDEX IDX_MEMBER_RECORD1 ON YCBUSI.CC_MEDIA_MEMBER_RECORD (SESSION_ID);
CREATE UNIQUE INDEX IDX_MEMBER_RECORD2 ON YCBUSI.CC_MEDIA_MEMBER_RECORD (CREATE_TIME);
CREATE INDEX IDX_MEMBER_RECORD3 ON YCBUSI.CC_MEDIA_MEMBER_RECORD (LEVEL_NAMES);
CREATE INDEX IDX_MEMBER_RECORD4 ON YCBUSI.CC_MEDIA_MEMBER_RECORD (MEMBER_ID);

grant insert,select,update,delete on YCBUSI.CC_MEDIA_MEMBER_RECORD to ycuser;
grant insert,select,update,delete on YCBUSI.CC_MEDIA_MEMBER_RECORD to ycbusi;
grant insert,select,update,delete on YCBUSI.CC_MEDIA_MEMBER_RECORD to ywdb;

/*渠道会员策略回复语配置表*/
CREATE TABLE YCUSER.CC_CHANNEL_MEMBER_AUTOCONF (
   CHANNEL_KEY VARCHAR2(64) NOT NULL,
   MEMBER_ID VARCHAR2(64) NOT NULL,
   CONF_KEY VARCHAR2(64) NOT NULL,
   TXT_CONTENT VARCHAR2(2000) NOT NULL,
   CREATE_TIME VARCHAR2(19) NOT NULL,
   PRIMARY KEY (CHANNEL_KEY,CONF_KEY,MEMBER_ID)
);
COMMENT ON TABLE YCUSER.CC_CHANNEL_MEMBER_AUTOCONF IS '渠道会员策略回复语配置表';
COMMENT ON COLUMN YCUSER.CC_CHANNEL_MEMBER_AUTOCONF.CHANNEL_KEY IS '渠道KEY';
COMMENT ON COLUMN YCUSER.CC_CHANNEL_MEMBER_AUTOCONF.MEMBER_ID IS '会员等级id，从会员等级字典中选择';
COMMENT ON COLUMN YCUSER.CC_CHANNEL_MEMBER_AUTOCONF.CONF_KEY IS '回复语标识，对应CC_CHANNEL.CHANNEL_AUTO_CONF内的json字段';
COMMENT ON COLUMN YCUSER.CC_CHANNEL_MEMBER_AUTOCONF.TXT_CONTENT IS '回复语内容';
COMMENT ON COLUMN YCUSER.CC_CHANNEL_MEMBER_AUTOCONF.CREATE_TIME IS '创建时间，格式：yyyy-MM-dd HH:mm:ss';
grant insert,select,update,delete on YCUSER.CC_CHANNEL_MEMBER_AUTOCONF to ycuser;
grant insert,select,update,delete on YCUSER.CC_CHANNEL_MEMBER_AUTOCONF to ycbusi;
grant insert,select,update,delete onYCUSER.CC_CHANNEL_MEMBER_AUTOCONF to ywdb;



