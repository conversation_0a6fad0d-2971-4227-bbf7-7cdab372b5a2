<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <groupId>com.yunqu.midea</groupId>
    <artifactId>yc-mediagw</artifactId>
    <packaging>war</packaging>
    <version>1.01</version>

    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <project.reporting.outputEncoding>UTF-8</project.reporting.outputEncoding>
        <java.version>1.8</java.version>
        <maven.compiler.source>${java.version}</maven.compiler.source>
        <maven.compiler.target>${java.version}</maven.compiler.target>
        <maven-compiler-plugin.version>3.8.1</maven-compiler-plugin.version>
        <maven-war-plugin.version>3.3.2</maven-war-plugin.version>
    </properties>

    <dependencies>
        <!-- easitline-core-2.0.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>easitline-core</artifactId>
            <version>2.0</version>
        </dependency>

        <!-- easitline-db-2.0.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>easitline-db</artifactId>
            <version>2.0</version>
        </dependency>
        <!-- easitline-taglib.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>easitline-taglib</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- easitline-utils-2.0.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>easitline-utils</artifactId>
            <version>2.0</version>
        </dependency>
        <!-- yc-sso.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>yc-sso</artifactId>
            <version>1.0</version>
        </dependency>
        <!-- yq_common_core.jar -->
        <dependency>
            <groupId>com.yunqu.midea</groupId>
            <artifactId>yq_common_core</artifactId>
            <version>1.0</version>
        </dependency>
    <!-- commons-io-2.4.jar -->
    <dependency>
        <groupId>commons-io</groupId>
        <artifactId>commons-io</artifactId>
        <version>2.4</version>
    </dependency>

    <!-- commons-lang3-3.7.jar -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-lang3</artifactId>
        <version>3.7</version>
    </dependency>

    <!-- commons-logging-1.2.jar -->
    <dependency>
        <groupId>commons-logging</groupId>
        <artifactId>commons-logging</artifactId>
        <version>1.2</version>
    </dependency>

    <!-- dom4j-1.6.1.jar -->
    <dependency>
        <groupId>dom4j</groupId>
        <artifactId>dom4j</artifactId>
        <version>1.6.1</version>
    </dependency>

    <!-- druid-1.0.25.jar -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>druid</artifactId>
        <version>1.0.25</version>
    </dependency>

    <!-- ehcache-core-2.6.11.jar -->
    <dependency>
        <groupId>net.sf.ehcache</groupId>
        <artifactId>ehcache-core</artifactId>
        <version>2.6.11</version>
    </dependency>

    <!-- ezmorph-1.0.6.jar -->
    <dependency>
        <groupId>net.sf.ezmorph</groupId>
        <artifactId>ezmorph</artifactId>
        <version>1.0.6</version>
    </dependency>

    <!-- fastjson-1.2.83.jar -->
    <dependency>
        <groupId>com.alibaba</groupId>
        <artifactId>fastjson</artifactId>
        <version>1.2.83</version>
    </dependency>

    <!-- hamcrest-core-1.1.jar -->
    <dependency>
        <groupId>org.hamcrest</groupId>
        <artifactId>hamcrest-core</artifactId>
        <version>1.1</version>
    </dependency>

    <!-- httpclient-4.4.1.jar -->
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpclient</artifactId>
        <version>4.4.1</version>
    </dependency>

    <!-- httpcore-4.4.1.jar -->
    <dependency>
        <groupId>org.apache.httpcomponents</groupId>
        <artifactId>httpcore</artifactId>
        <version>4.4.1</version>
    </dependency>

    <!-- jdom-1.1.jar -->
    <dependency>
        <groupId>org.jdom</groupId>
        <artifactId>jdom</artifactId>
        <version>1.1</version>
    </dependency>

    <!-- jstl-1.2.jar -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>jstl</artifactId>
        <version>1.2</version>
    </dependency>

    <!-- jxls-jexcel-1.0.6.jar -->
    <dependency>
        <groupId>org.jxls</groupId>
        <artifactId>jxls-jexcel</artifactId>
        <version>1.0.6</version>
    </dependency>

    <!-- jxls-poi-1.0.12.jar -->
    <dependency>
        <groupId>org.jxls</groupId>
        <artifactId>jxls-poi</artifactId>
        <version>1.0.12</version>
    </dependency>

    <!-- jxls-reader-2.0.2.jar -->
    <dependency>
        <groupId>org.jxls</groupId>
        <artifactId>jxls-reader</artifactId>
        <version>2.0.2</version>
    </dependency>

    <!-- log4j-1.2.17.jar -->
    <dependency>
        <groupId>log4j</groupId>
        <artifactId>log4j</artifactId>
        <version>1.2.17</version>
    </dependency>

    <!-- memcached-2.6.jar -->
<!--    <dependency>-->
<!--        <groupId>net.spy</groupId>-->
<!--        <artifactId>spymemcached</artifactId>-->
<!--        <version>2.12.3</version>-->
<!--    </dependency>-->

    <!-- mongo-java-driver-3.6.3.jar -->
<!--    <dependency>-->
<!--        <groupId>org.mongodb</groupId>-->
<!--        <artifactId>mongo-java-driver</artifactId>-->
<!--        <version>3.6.3</version>-->
<!--    </dependency>-->

    <!-- mysql-connector-java-5.1.38-bin.jar -->
    <dependency>
        <groupId>mysql</groupId>
        <artifactId>mysql-connector-java</artifactId>
        <version>5.1.38</version>
    </dependency>

<!--    &lt;!&ndash; netty-3.9.4.Final.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>io.netty</groupId>-->
<!--        <artifactId>netty</artifactId>-->
<!--        <version>3.9.4.Final</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; ojdbc6.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>com.oracle.database.jdbc</groupId>-->
<!--        <artifactId>ojdbc6</artifactId>-->
<!--        <version>11.2.0.4</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-examples-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi-examples</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-excelant-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi-excelant</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-ooxml-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi-ooxml</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-ooxml-schemas-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi-ooxml-schemas</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

<!--    &lt;!&ndash; poi-scratchpad-3.16.jar &ndash;&gt;-->
<!--    <dependency>-->
<!--        <groupId>org.apache.poi</groupId>-->
<!--        <artifactId>poi-scratchpad</artifactId>-->
<!--        <version>3.16</version>-->
<!--    </dependency>-->

    <!-- quartz-2.2.1.jar -->
    <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz</artifactId>
        <version>2.2.1</version>
    </dependency>

    <!-- quartz-jobs-2.2.1.jar -->
    <dependency>
        <groupId>org.quartz-scheduler</groupId>
        <artifactId>quartz-jobs</artifactId>
        <version>2.2.1</version>
    </dependency>

    <!-- sigar.jar -->
<!--    <dependency>-->
<!--        <groupId>org.fusesource</groupId>-->
<!--        <artifactId>sigar</artifactId>-->
<!--        <version>1.6.4</version>-->
<!--    </dependency>-->

    <!-- slf4j-api-1.7.9.jar -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-api</artifactId>
        <version>1.7.9</version>
    </dependency>

    <!-- slf4j-log4j12-1.7.9.jar -->
    <dependency>
        <groupId>org.slf4j</groupId>
        <artifactId>slf4j-log4j12</artifactId>
        <version>1.7.9</version>
    </dependency>

    <!-- sqljdbc4-4.2.jar -->
<!--    <dependency>-->
<!--        <groupId>com.microsoft.sqlserver</groupId>-->
<!--        <artifactId>mssql-jdbc</artifactId>-->
<!--        <version>6.2.2.jre8</version>-->
<!--    </dependency>-->

    <!-- standard-1.1.2.jar -->
    <dependency>
        <groupId>taglibs</groupId>
        <artifactId>standard</artifactId>
        <version>1.1.2</version>
    </dependency>

    <!-- xmlbeans-2.6.0.jar -->
    <dependency>
        <groupId>org.apache.xmlbeans</groupId>
        <artifactId>xmlbeans</artifactId>
        <version>2.6.0</version>
    </dependency>

    <!-- xstream-1.4.7.jar -->
<!--    <dependency>-->
<!--        <groupId>com.thoughtworks.xstream</groupId>-->
<!--        <artifactId>xstream</artifactId>-->
<!--        <version>1.4.7</version>-->
<!--    </dependency>-->

    <!-- zxing-3.2.1.jar -->
<!--    <dependency>-->
<!--        <groupId>com.google.zxing</groupId>-->
<!--        <artifactId>core</artifactId>-->
<!--        <version>3.2.1</version>-->
<!--    </dependency>-->

    <!-- activemq-all-5.15.2.jar -->
    <dependency>
        <groupId>org.apache.activemq</groupId>
        <artifactId>activemq-all</artifactId>
        <version>5.15.2</version>
    </dependency>

    <!-- commons-codec-1.11.jar -->
    <dependency>
        <groupId>commons-codec</groupId>
        <artifactId>commons-codec</artifactId>
        <version>1.11</version>
    </dependency>

    <!-- commons-collections4-4.1.jar -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-collections4</artifactId>
        <version>4.1</version>
    </dependency>

    <!-- commons-csv-1.4.jar -->
    <dependency>
        <groupId>org.apache.commons</groupId>
        <artifactId>commons-csv</artifactId>
        <version>1.4</version>
    </dependency>

    <!-- commons-fileupload-1.3.1.jar -->
    <dependency>
        <groupId>commons-fileupload</groupId>
        <artifactId>commons-fileupload</artifactId>
        <version>1.3.1</version>
    </dependency>

    <!-- commons-httpclient-3.0.jar -->
    <dependency>
        <groupId>commons-httpclient</groupId>
        <artifactId>commons-httpclient</artifactId>
        <version>3.0</version>
    </dependency>

    <!-- 添加servlet-api依赖 -->
    <dependency>
        <groupId>javax.servlet</groupId>
        <artifactId>javax.servlet-api</artifactId>
        <version>3.1.0</version>
        <scope>provided</scope>
    </dependency>

    <!-- 添加websocket-api依赖 -->
    <dependency>
        <groupId>javax.websocket</groupId>
        <artifactId>javax.websocket-api</artifactId>
        <version>1.1</version>
        <scope>provided</scope>
    </dependency>
    </dependencies>

    <build>
        <sourceDirectory>src</sourceDirectory>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <version>${maven-compiler-plugin.version}</version>
                <configuration>
                    <source>${maven.compiler.source}</source>
                    <target>${maven.compiler.target}</target>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-war-plugin</artifactId>
                <version>${maven-war-plugin.version}</version>
                <configuration>
                    <failOnMissingWebXml>false</failOnMissingWebXml>
                    <webResources>
<!--                        <resource>-->
<!--                            <directory>src</directory>-->
<!--                            <targetPath>WEB-INF/src</targetPath>-->
<!--                            <includes>-->
<!--                                <include>**/*.java</include>-->
<!--                            </includes>-->
<!--                        </resource>-->
                        <resource>
                            <directory>WebContent</directory>
                        </resource>
                    </webResources>
                </configuration>
            </plugin>
            <!-- 注释掉无法找到的插件 -->
            
            <plugin>
                <groupId>com.yunqu.mars.plugin</groupId>
                <artifactId>console-upgrade</artifactId>
                <version>1.0</version>
                <executions>
                    <execution>
                        <goals>
                            <goal>generate-version</goal>
                            <goal>push-mars</goal>
                        </goals>
                        <configuration>
                            <cleanLibDirectory>true</cleanLibDirectory>
                            <jarPatterns>commons-codec*</jarPatterns>
                            <enableWarPushMars>false</enableWarPushMars>
                            <consoleUrl>http://172.16.70.136:9060/easitline-console</consoleUrl>
                            <primaryVersion>1.01</primaryVersion>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-antrun-plugin</artifactId>
                <version>3.1.0</version>
                <executions>
                    <execution>
                        <id>clean-source-classes-dir</id>
                        <phase>prepare-package</phase> <!-- 在打包前执行 -->
                        <goals>
                            <goal>run</goal>
                        </goals>
                        <configuration>
                            <target>
                                <!-- 删除源代码中的 classes 目录 -->
                                <delete dir="${project.basedir}/WebContent/WEB-INF/classes" failonerror="false"/>
                                <delete dir="${project.basedir}/src/main/webapp/WEB-INF/classes" failonerror="false"/>
                            </target>
                        </configuration>
                    </execution>
                </executions>
            </plugin>
        </plugins>
        <finalName>${project.artifactId}</finalName>
    </build>
</project>
