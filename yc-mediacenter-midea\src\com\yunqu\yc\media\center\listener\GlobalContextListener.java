package com.yunqu.yc.media.center.listener;



import com.yunqu.yc.media.center.base.Constants;
import com.yunqu.yc.media.center.context.EntContext;
import com.yunqu.yc.media.center.log.MediaCenterLogger;
import com.yunqu.yc.media.center.mqclient.ConsumerBroker;
import com.yunqu.yc.media.center.mqclient.message.AgentConsumerMessage;
import com.yunqu.yc.media.center.mqclient.message.UserConsumerMessage;
import com.yunqu.yc.media.center.util.CacheUtil;
import com.yunqu.yc.media.center.util.SchedulerTaskUtils;
import org.quartz.SchedulerException;

import javax.servlet.ServletContextEvent;
import javax.servlet.ServletContextListener;
import javax.servlet.annotation.WebListener;
import java.util.Collection;




/** 
 * 应用上下文监听器
 */
@WebListener
public class GlobalContextListener  implements ServletContextListener ,GolbalThreadState{
	
	
	
	private Thread gwConsumerBrokerThread = null;
	private Thread iccsConsumerBrokerThread = null;
	
	private ConsumerBroker gwConsumerBroker = null;
	private ConsumerBroker iccsConsumerBroker = null;
	
	
	public static  boolean  runState =  true;
	

	/**
     * war被卸载时触发,在这个方法里针对应用被卸载时候做一些处理操作。
     */
    public void contextDestroyed(ServletContextEvent arg0)  {
    	runState = false;
    	//添加一个应用销毁标记，用于通知yc-mediagw
    	CacheUtil.put("MEDIACENTER_IS_DESTROIING","1" ,60);
    	try {
    		Collection<EntContext>   ents = EntContext.getEntContexts();
    		for(EntContext entContext:ents){
    			entContext.releaseAll();
    		}
		} catch (Exception e) {
			// TODO: handle exception
		}
    	

    	try {
    		for(int i = 0 ;i<30;i++){
        		if(gwConsumerBroker.isClose()){
        			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] gwConsumerBroker close finish!");
        			break;
        		}
        		Thread.sleep(1000);
        		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Wait for gwConsumerBroker close...");
        	}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
		}
    	
    	try {
    		for(int i = 0 ;i<30;i++){
        		if(iccsConsumerBroker.isClose()){
        			MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] iccsConsumerBroker close finish!");
        			break;
        		}
        		Thread.sleep(1000);
        		MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] Wait for iccsConsumerBroker close...");
        	}
		} catch (Exception ex) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+ex.getMessage());
			MediaCenterLogger.getLogger().error(ex.getMessage(),ex);
		}
    	
    	try {
			SchedulerTaskUtils.deleteAllSchedul();
		} catch (SchedulerException e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
		}

    	MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] contextDestroyed finish!!!!");
    	
    }

	/**
     * war应用加载时被触发，通常用于加载调度、数据源等。
     */
    public void contextInitialized(ServletContextEvent arg0)  {
    	MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] contextInitialized start!!!!");
    	runState = true;
    	CacheUtil.delete("MEDIACENTER_IS_DESTROIING");
		try {
			Thread.sleep(10000);
		} catch (InterruptedException e) {
		}
		EntContext.getContext(null);
    	gwConsumerBroker =  new ConsumerBroker(Constants.MEDIACENTER_BROKER,new UserConsumerMessage(),this );
    	gwConsumerBrokerThread = new Thread(gwConsumerBroker);
    	gwConsumerBrokerThread.start();
    	
    	iccsConsumerBroker = new ConsumerBroker(Constants.ICCS_BROKER,new AgentConsumerMessage(),this );
    	iccsConsumerBrokerThread = new Thread(iccsConsumerBroker);
    	iccsConsumerBrokerThread.start();
    	
    	try {
    		//每间隔5秒检查视频邀请是否超时
    		SchedulerTaskUtils.addTask("com.yunqu.yc.media.center.job.VideoInviteTimeOutJob","0/5 * * * * ? ");
			SchedulerTaskUtils.addTask("com.yunqu.yc.media.center.job.DayOnlineJob", "0 0 0 * * ?");
    		SchedulerTaskUtils.start();
    	} catch (Exception e) {
			MediaCenterLogger.getLogger().error("Thread["+Thread.currentThread().getId()+"] "+e.getMessage());
			MediaCenterLogger.getLogger().error(e.getMessage(),e);
    	}
    	MediaCenterLogger.getLogger().info("Thread["+Thread.currentThread().getId()+"] contextInitialized finish!!!!");
    }

	@Override
	public boolean isRunning() {
		// TODO Auto-generated method stub
		return runState;
	}
    
}
