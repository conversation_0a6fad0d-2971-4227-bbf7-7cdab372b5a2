package com.yunqu.yc.center.dao.media;

import com.alibaba.fastjson.JSONObject;
import com.yunqu.yc.center.base.AppDaoContext;
import com.yunqu.yc.center.base.Constants;
import com.yunqu.yc.center.log.YcCenterLogger;
import org.easitline.common.annotation.Types;
import org.easitline.common.annotation.WebControl;
import org.easitline.common.annotation.WebObject;
import org.easitline.common.core.context.AppContext;
import org.easitline.common.db.EasyRecord;
import org.easitline.common.db.EasySQL;
import org.easitline.common.db.impl.JSONMapperImpl;
import org.easitline.common.utils.string.StringUtils;

import java.sql.SQLException;

@WebObject(name="channel")
public class ChannelDao extends AppDaoContext {

	@WebControl(name="list",type=Types.LIST)
	public  JSONObject list(){
		String entId = this.param.getString("entId");
	    String channelName = this.param.getString("channelName");
		String channelKey = this.param.getString("channelKey");

	    String channelType = this.param.getString("channelType");
	    String channelState = this.param.getString("channelState");
	    EasySQL sql = getEasySQL("select * from CC_CHANNEL where 1=1");
	    sql.append(entId, " and ENT_ID = ?");
	    sql.appendLike(channelName, " and CHANNEL_NAME like ?");
		sql.appendLike(channelKey, " and CHANNEL_KEY like ?");
	    sql.append(channelType, " and CHANNEL_TYPE=?");
	    sql.append(channelState, " and CHANNEL_STATE=?");
	    return queryForPageList(sql.getSQL(), sql.getParams(), null);
	}
	
	@WebControl(name="getChannel",type=Types.RECORD)
	public  JSONObject getChannel(){
		String channelId = this.param.getString("channel.CHANNEL_ID");
	    int channelType = this.param.getIntValue("type");
	    EasyRecord record = new EasyRecord("CC_CHANNEL", new String[] { "CHANNEL_ID", "CHANNEL_TYPE" });
	    record.setPrimaryValues(new Object[] { channelId, Integer.valueOf(channelType) });
	    JSONObject result = queryForRecord(record);
	    JSONObject data = result.getJSONObject("data");
	    String configStr = data.getString("CHANNEL_CONF");
	    if (StringUtils.isBlank(configStr)) {
	      return result;
	    }
	    JSONObject configObj = JSONObject.parseObject(configStr);
	    if (configObj == null) {
	      return result;
	    }
	    String beforeKey = "";
	    if (channelType == 1)
	      beforeKey = "WEBCHAT_";
	    else if (channelType == 2)
	      beforeKey = "WECHAT_";
	    else if (channelType == 3)
	      beforeKey = "WEIBO_";
	    else if (channelType == 5) {
	      beforeKey = "APPCHAT_";
	    }
	    for (String key : configObj.keySet())
	    {
//	      if ("BEGIN_WORK_TIME1".equals(key) || "END_WORK_TIME1".equals(key) || "BEGIN_WORK_TIME2".equals(key) || "END_WORK_TIME2".equals(key) ||
//	        "OFFLINE_CHAT_TYPE".equals(key) || "OFFLINE_CHAT_URL".equals(key) ||
//	        "UPDATE_KEY_TYPE1_TIME1".equals(key) || "UPDATE_KEY_TYPE1_TIME2".equals(key) ||
//	        "UPDATE_KEY_TYPE2_TIME1".equals(key) || "UPDATE_KEY_TYPE2_TIME2".equals(key) ||
//	        "IS_REPLACE".equals(key) || "NEW_FILE_SERVER_ADDR".equals(key) || "DEF_FILE_SERVER_ADDR".equals(key)||
//	        "VIDEO_API_TYPE".equals(key)||"MINIPROGRAM_PAGE".equals(key)||"MINIPROGRAM_APPID".equals(key)||"MINIPROGRAM_THUMB_URL".equals(key)||"SATISFY_TYPE".equals(key)||"MINIPROGRAM_URL".equals(key)) {
//	        data.put("commonConfig." + key, configObj.get(key));
//	      }
			data.put("commonConfig." + key, configObj.get(key));
	      data.put("config." + beforeKey + key, configObj.get(key));
	    }
	    result.put("data", data);
	    return result;
	}
	
	@WebControl(name="getAutoConfig",type=Types.RECORD)
	public  JSONObject getAutoConfig(){
		String channelId=param.getString("channelId");
		JSONObject result = queryForRecord("select CHANNEL_AUTO_CONF from CC_CHANNEL where CHANNEL_ID=?", new Object[]{channelId}, new JSONMapperImpl());
		JSONObject data = result.getJSONObject("data");
		String autoConfigStr = data.getString("CHANNEL_AUTO_CONF");
		if(StringUtils.isBlank(autoConfigStr)){
			return result;
		}
		JSONObject autoConfigObj = JSONObject.parseObject(autoConfigStr);
		if(autoConfigObj == null||autoConfigObj.size()==0){
			return result;
		}
		result.put("data", autoConfigObj);
		return result;
	}
	
	@WebControl(name="keyList",type=Types.LIST)
	public  JSONObject keyList(){
		String entId = param.getString("entId");
		String channelId = param.getString("channelId");
		String keyType = param.getString("keyType");
		String keyName = param.getString("keyName");
		EasySQL sql = this.getEasySQL("select t1.* from CC_CHANNEL_KEY t1 where 1=1");
		sql.append(entId, " and t1.ENT_ID = ?");
		sql.append(channelId, " and t1.CHANNEL_ID = ?");
		sql.append(keyType," and t1.KEY_TYPE=?");
		sql.appendLike(keyName," and t1.KEY_NAME like ?");
		sql.append(param.getString("isShow")," and t1.IS_SHOW=?");
		sql.append("order by SORT_IDX,KEY_CODE");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getChannelKey",type=Types.RECORD)
	public  JSONObject getChannelKey(){
		String pk=param.getString("pk");
		String channelId=param.getString("channelKey.CHANNEL_ID");
		EasyRecord record = new EasyRecord("CC_CHANNEL_KEY", new String[]{"KEY_ID","CHANNEL_ID"});
		record.setPrimaryValues(new Object[]{pk,channelId});
		JSONObject result = queryForRecord(record);
		return result;
	}
	
	@WebControl(name="swList",type=Types.LIST)
	public  JSONObject swList(){
		EasySQL sql = this.getEasySQL("select * from CC_CHANNEL_SW where 1=1");
		sql.append(param.getString("entId"), " and ENT_ID = ?");
		sql.appendLike(param.getString("swText"), " and SW_TEXT like ?");
		sql.append("order by CREATE_TIME");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="getSW",type=Types.RECORD)
	public  JSONObject getSW(){
		return this.queryForRecord("select * from CC_CHANNEL_SW where SW_ID = ?", new Object[]{param.getString("sw.SW_ID")}, null);
	}
	
	@WebControl(name="channelDict",type=Types.DICT)
	public  JSONObject channelDict(){
		this.getLogger().info("channelDict------>"+param);
		return this.getDictByQuery("select CHANNEL_ID,CHANNEL_NAME from CC_CHANNEL where CHANNEL_STATE = 0 and ENT_ID = ?", new Object[]{param.getString("entId")});
	}
	
	
	/**
	 * 渠道满意度指标
	 * @return
	 */
	@WebControl(name="satisfList",type=Types.LIST)
	public  JSONObject satisfList(){
//		String entId = param.getString("entId");
		String satisfName = param.getString("satisfName");
		String channelKey = param.getString("channelKey");
		EasySQL sql = this.getEasySQL("select  t1.ID ,t1.CHANNEL_NO ,t1.CODE,t1.TYPE,t1.name,t1.ENABLE_STATUS,t1.SORT_NUM ,t2.CHANNEL_NAME ");
		sql.append(",LISTAGG(t4.name, ',') WITHIN GROUP (ORDER BY t4.name)  as EXT ");
		sql.append( " from YWDB.C_STF_SATISF_QUOTA t1");
		sql.append( "  left join CC_CHANNEL t2 on t1.CHANNEL_NO = t2.CHANNEL_KEY ");
		sql.append( " left join CC_MEDIA_SATISF_EXT_QUOTA t3 on t3.satisf_id=t1.id");
		sql.append( " left join CC_MEDIA_SATISF_EXT_ITEM t4 on t3.item_id=t4.id");
		sql.append(" where 1=1 ");
		sql.append(channelKey," and t1.CHANNEL_NO=? ");
		sql.appendLike(satisfName," and t1.NAME LIKE ? ");
		sql.append(" group by t1.ID ,t1.ID,t1.CHANNEL_NO ,t1.CODE,t1.TYPE,t1.name,t1.ENABLE_STATUS,t1.SORT_NUM ,t2.CHANNEL_NAME ");
		sql.append(" order by t1.SORT_NUM ");
		return this.queryForPageList(sql.getSQL(), sql.getParams(),null);
	}
	
	@WebControl(name="satisfRecord",type=Types.RECORD)
	public  JSONObject satisfRecord(){
		String channelId=param.getString("satisf.CHANNEL_NO");
		String sId=param.getString("satisf.ID");
		EasyRecord record = new EasyRecord("YWDB.C_STF_SATISF_QUOTA", "ID","CHANNEL_NO");
		record.setPrimaryValues(sId,channelId);
		return  queryForRecord(record);
	}

	/**
	 * 美居APP_H5,美的美居小程序,小天鹅_小程序,小天鹅服务号_新,美的洗悦家,美的服务,COLMO公众号,COLMO_APP,COLMO_小程序,东芝服务小程序,美的到家小程序,美的服务_小程序
	 * @return
	 */
	@WebControl(name = "getH5StyleConfig",type = Types.RECORD)
	public JSONObject getH5StyleConfig() {
		String channelId = param.getString("CHANNEL_ID");
		if(StringUtils.isBlank(channelId)) {
			return null;
		}
		JSONObject record = new JSONObject();
		try {
			record = getQuery().queryForRow("SELECT STYLE_CONF FROM CC_CHANNEL WHERE CHANNEL_ID = ?", new Object[] {channelId}, new JSONMapperImpl());
			if(record!=null&&StringUtils.isNotBlank(record.getString("STYLE_CONF"))) {
				String url = AppContext.getContext(Constants.APP_NAME).getProperty("UPLOAD_FILE_SERVER_URL", "https://ccuat.midea.com/");
				JSONObject data = record.getJSONObject("STYLE_CONF");
				data.put("serverUrl", url);
				record.put("data",data);
			}
			
		} catch (SQLException e) {
			YcCenterLogger.getLogger().error("error:" + e.getMessage(),e);
		}
		return record;
	}
	@WebControl(name = "getRobotConfig",type = Types.RECORD)
	public JSONObject getRobotConfig() {
		String channelId = param.getString("CHANNEL_ID");
		if(StringUtils.isBlank(channelId)) {
			return null;
		}
		JSONObject record = new JSONObject();
		try {
			record = getQuery().queryForRow("SELECT CC_DATA FROM CC_CHANNEL WHERE CHANNEL_ID = ?", new Object[] {channelId}, new JSONMapperImpl());
			if(record!=null&&StringUtils.isNotBlank(record.getString("CC_DATA"))) {
				JSONObject data = record.getJSONObject("CC_DATA");
				record.put("data",data);
			}
		} catch (SQLException e) {
			YcCenterLogger.getLogger().error("error:" + e.getMessage(),e);
		}
		return record;
	}

	/**
	 * 查询渠道可选的技能组
	 * @return
	 */
	@WebControl(name="selGroupList",type=Types.TEMPLATE)
	public JSONObject selGroupList(){
		String entId = param.getString("entId");
		String groupKeyword = param.getString("groupKeyword");
		String channelId = param.getString("channelId");
		EasySQL sql=this.getEasySQL("");
		sql.append("select t1.* from ").append(getTableName(entId,"CC_SKILL_GROUP t1"));//getTableName(entId,"CC_SKILL_GROUP")
		sql.append(" where 1=1 ");
		sql.append(entId," and t1.ENT_ID = ?");
//		sql.append(getBusiOrderId()," and t1.BUSI_ORDER_ID = ?");
		if(StringUtils.isNotBlank(groupKeyword)){
			sql.appendLike(groupKeyword," and (t1.SKILL_GROUP_NAME LIKE ?");
			sql.appendLike(groupKeyword," or t1.SKILL_GROUP_ID LIKE ?)");
		}
		sql.append(channelId," and t1.SKILL_GROUP_ID not in (select SKILL_GROUP_ID from CC_CHANNEL_TRAN_GROUP where CHANNEL_ID = ?)");
		sql.append(" order by t1.CREATE_TIME desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}

	/**
	 * 查询渠道已选转移技能组
	 * @return
	 */
	@WebControl(name="havGroupList",type=Types.TEMPLATE)
	public JSONObject havGroupList(){
		String entId = param.getString("entId");
		String groupKeyword = param.getString("groupKeyword");
		String channelId = param.getString("channelId");
		EasySQL sql=this.getEasySQL("");
		sql.append("select t1.* ");
		sql.append(" from ").append("CC_CHANNEL_TRAN_GROUP t2");//getTableName(entId,"CC_SKILL_GROUP")
		sql.append(" left join").append(getTableName(entId,"CC_SKILL_GROUP t1")).append(" on t2.SKILL_GROUP_ID = t1.SKILL_GROUP_ID");
		sql.append(channelId," where t2.CHANNEL_ID = ?",false);
		sql.append(entId," and t1.ENT_ID = ?");
		if(StringUtils.isNotBlank(groupKeyword)){
			sql.appendLike(groupKeyword," and (t1.SKILL_GROUP_NAME LIKE ?");
			sql.appendLike(groupKeyword," or t1.SKILL_GROUP_ID LIKE ?)");
		}
		sql.append(" order by t1.CREATE_TIME desc");
		return queryForList(sql.getSQL(),sql.getParams());
	}

	/**
	 * 通过channelId获取channelKey
	 * @return
	 */
	@WebControl(name="getChannelKeyByChannelId",type=Types.RECORD)
	public JSONObject getChannelKeyByChannelId(){
		String channelId = param.getString("channelId");
		JSONObject result = new JSONObject();
		try {
			JSONObject data = getQuery().queryForRow("SELECT CHANNEL_KEY FROM CC_CHANNEL WHERE CHANNEL_ID = ?", new Object[] {channelId}, new JSONMapperImpl());
			result.put("state", 1);
			result.put("data", data);
		} catch (SQLException e) {
			YcCenterLogger.getLogger().error("error:" + e.getMessage(),e);
			result.put("state", 0);
			result.put("msg", "获取渠道信息失败");
		}
		return result;
	}
}
