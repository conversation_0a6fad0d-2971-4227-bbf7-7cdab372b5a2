<%@ page language="java" contentType="text/html;charset=UTF-8"%>
<%@ include file="/pages/common/global.jsp" %>
<EasyTag:override name="head">
	<title i18n-content="渠道机器人信息配置"></title>
	<style>
		.layer-foot {
		    bottom: 0;
		    left: 0;
		    position: fixed;
		    width: 100%;
		    height: 48px;
		    line-height: 45px;
		    z-index: 2;
		    background-color: #fff;
		    text-align: center;
		    border-top: 1px solid #eee;
		}
	</style>
</EasyTag:override>
<EasyTag:override name="content">
	<form id="robotForm" class="form-horizontal mb-50" data-mars="channel.getRobotConfig" data-mars-top="true" >
		<input type="hidden" name="CHANNEL_ID" value="${param.channelId}">
		<div>
	    	<table class="table table-edit table-vzebra mt-20">
				<tbody>			
		            <tr>
		                <td class="required">机器人服务host</td>
		                <td><input data-rules="required" type="text" name="ROBOT_IP" class="form-control input-sm" autocomplete="off" placeholder="https://xx.media.com"></td>
		            </tr>
		            <tr>
		                <td>机器人appId</td>
		                <td><input type="text" name="ROBOT_APP_ID"  class="form-control input-sm" autocomplete="off"></td>
		                <td>机器人appSecret</td>
		                <td><input type="text" name="ROBOT_APP_SECRET"  class="form-control input-sm" autocomplete="off"></td>
		            </tr>
		            <tr>
		                <td>机器人渠道(ROBOT_CHANNEL_ID)<i title="无特殊渠道请配置0，只要入口渠道打开的是H5页面，都配置为0" style="cursor: pointer;" class="glyphicon glyphicon-question-sign" data-toggle="tooltip"></i></td>
		                <td colspan="3"><input type="text" name="ROBOT_CHANNEL_ID"  class="form-control input-sm" autocomplete="off" placeholder="0-网页，1-微信，2-API，3-APP，4-微博，5-支付宝，9-大屏实体机器人" value="0"  ></td>
		            </tr>
		            <tr>
		                <td class="required">机器人来源(ROBOT_SOURCE_ID)<i title="云问机器人平台的渠道id，AIGC平台不用配置" style="cursor: pointer;" class="glyphicon glyphicon-question-sign" data-toggle="tooltip"></i></td>
		                <td colspan="3"><input type="text" name="ROBOT_SOURCE_ID"  class="form-control input-sm" autocomplete="off" placeholder="渠道来源id，与appid对应" ></td>
		            </tr>
					<tr>
						<td>机器人微信渠道id(ROBOT_WXC_ID)</td>
						<td colspan="3"><input type="text" name="ROBOT_WXC_ID"  class="form-control input-sm" autocomplete="off" placeholder="机器人微信渠道id" ></td>
					</tr>
		            <tr>
		                <td>机器人站点号(ROBOT_SYS_NUM)</td>
		                <td colspan="3"><input type="text" name="ROBOT_SYS_NUM"  class="form-control input-sm" autocomplete="off"  ></td>
		            </tr>
<%--					<tr>--%>
<%--		                <td>机器人服务</td>--%>
<%--		                <td colspan="3">--%>
<%--							<select name="ROBOT_SERVICE_TYPE" class="form-control input-sm">--%>
<%--								<option value="YUNWEN">云问</option>--%>
<%--								<option value="AIGC">AIGC</option>--%>
<%--							</select>--%>
<%--						</td>--%>
<%--		            </tr>--%>
					<tr>
						<td>坐席辅助类型</td>
						<td colspan="3">
							<select name="ROBOT_AGENT_TYPE" class="form-control input-sm" onchange="chooseArtificial(this)">
								<option value="yunwen">云问机器人</option>
								<option value="midea">美的上海机器人</option>
								<option value="aigc">AIGC</option>
							</select>
						</td>
					</tr>
					<tr>
						<td>辅助渠道</td>
						<td colspan="3">
							<select name="ARTIFICIAL_ID" id="artificialSel" class="form-control input-sm" data-mars="ArtificialDao.dict">
								<option value="">默认</option>
							</select>
						</td>
					</tr>

					<%--CSS工单接口对接，用于robotgw传输--%>
					<tr>
						<td>CSS渠道Id<i title="访客初始化上线时，传给云问用于查询CSS工单列表，美的服务：MMJYWX（20241205上线），未上线渠道：colmo：COLMO-OW，小天鹅：LSWX，美居：SMART，东芝：DZWCP，美的Midea小程序：MDXCX" style="cursor: pointer;" class="glyphicon glyphicon-question-sign" data-toggle="tooltip"></i></td>
						<td colspan="3"><input type="text" name="CSS_API_SOURCE" class="form-control input-sm" autocomplete="off"  ></td>
					</tr>
					<tr>
						<td class="required">CSS工单自动推送<i title="关闭：访客初始化上线时，机器人不推送CSS工单列表" style="cursor: pointer;" class="glyphicon glyphicon-question-sign" data-toggle="tooltip"></i></td>
						<td>
							<label class="radio-inline">
								<input type="radio" checked="checked" name="CSS_QUERY_FLAG" value="0"> 关闭
							</label>
							<label class="radio-inline">
								<input type="radio" name="CSS_QUERY_FLAG" value="1"> 开启
							</label>
						</td>
					</tr>

	        	</tbody>
		    </table>
		</div>
    	<div class="layer-foot text-c">
	      	<button type="button" class="btn btn-primary btn-sm" onclick="channelRobot.saveChannelSet()">保存</button>
	      	<button type="button" class="btn btn-default btn-sm ml-20" onclick="popup.layerClose(this)">关闭</button>
		</div>
	</form>
</EasyTag:override>
<EasyTag:override name="script">
	<script type="text/javascript">
		jQuery.namespace("channelRobot");

		var hisArtificialId;//历史坐席辅助id
		$(function(){
			$("#robotForm").render({success:function(result){
					debugger;
				if(result&&result['channel.getRobotConfig']&&result['channel.getRobotConfig'].data){
					hisArtificialId = result['channel.getRobotConfig'].data.ARTIFICIAL_ID;
				}

				var artificialDict = result && result['ArtificialDao.dict'] && result['ArtificialDao.dict'].data
				if(hisArtificialId && artificialDict){
					$("#artificialSel").find("option[value='"+hisArtificialId+"']").attr("selected","selected");
				}
			}});
			$('[data-toggle="tooltip"]').tooltip();
		})
		

		//保存渠道机器人配置
		channelRobot.saveChannelSet = function(){
			if(!form.validate("#robotForm")){
				return;
			}
			var data = form.getJSONObject("#robotForm");
			ajax.remoteCall("${ctxPath}/servlet/channel?action=saveRobot", data,
				function(result) {
					if (result.state == 1) {
						layer.msg(result.msg, {icon : 1,time:1000},function(){
							popup.layerClose(this);
						});
					} else {
						layer.alert(result.msg, {icon : 5});
					}
			});
		}

		function chooseArtificial(el){
			var value = $(el).find("option:selected").val();
			var reqData = {ROBOT_AGENT_TYPE:value};
			$("#artificialSel").render({data:reqData,success:function(result){
				if(hisArtificialId){
					$("#artificialSel").find("option[value='"+hisArtificialId+"']").attr("selected","selected");
				}
			}});
		}
	</script>
</EasyTag:override>
<%@ include file="/pages/common/layout_list.jsp"%>